<?php

namespace Tests\Unit\Nova\Actions;

use App\Enums\AssociationMembershipChangeModeEnum;
use App\Enums\InvoiceStatusEnum;
use App\Invoice;
use App\Mail\RevertAssociationMembershipUserInfoMail;
use App\Nova\Actions\RevertLastAssociationMembershipChangeAction;
use App\SubscriptionOrder;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class RevertLastAssociationMembershipChangeActionTest extends TestCase
{
    public function test_cannot_revert_last_change_when_change_is_null(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->assertNull($owner->currentAssociationMembershipChange);

        $fields = new ActionFields(collect(), collect());
        $models = collect([$owner]);
        $novaAction = new RevertLastAssociationMembershipChangeAction;

        $message = $novaAction->handle($fields, $models);

        $owner->refresh();

        $this->assertIsObject($message);
        $this->assertObjectHasProperty('danger', $message);

        $this->assertNull($owner->currentAssociationMembershipChange);

        Mail::assertNotQueued(RevertAssociationMembershipUserInfoMail::class);
    }

    public function test_can_revert_last_change_when_change_is_not_processed(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $owner->currentAssociationMembershipChange()->create([
            'association_id_before' => $owner->association_id,
            'association_id_after' => null,
            'change_at' => now()->addMonthNoOverflow()->endOfMonth(),
            'mode' => AssociationMembershipChangeModeEnum::CHANGE,
        ]);

        $this->assertNotNull($owner->currentAssociationMembershipChange);
        $this->assertNull($owner->currentAssociationMembershipChange->canceled_at);

        $fields = new ActionFields(collect(), collect());
        $models = collect([$owner]);
        $novaAction = new RevertLastAssociationMembershipChangeAction;

        $message = $novaAction->handle($fields, $models);

        $owner->refresh();

        $this->assertIsObject($message);
        $this->assertObjectHasProperty('message', $message);

        Mail::assertQueued(RevertAssociationMembershipUserInfoMail::class);
    }

    public function test_can_revert_last_change_when_change_at_is_not_within_a_billing_period(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $owner->currentAssociationMembershipChange()->create([
            'association_id_before' => $owner->association_id,
            'association_id_after' => null,
            'change_at' => now()->subMonthsNoOverflow(2)->endOfMonth(),
            'mode' => AssociationMembershipChangeModeEnum::CHANGE,
        ]);
        $pharmacy->subscribeToPlan('base');

        $this->assertNotNull($owner->currentAssociationMembershipChange);
        $this->assertNull($owner->currentAssociationMembershipChange->canceled_at);

        $fields = new ActionFields(collect(), collect());
        $models = collect([$owner]);
        $novaAction = new RevertLastAssociationMembershipChangeAction;

        $message = $novaAction->handle($fields, $models);

        $owner->refresh();

        $this->assertIsObject($message);
        $this->assertObjectHasProperty('message', $message);

        Mail::assertQueued(RevertAssociationMembershipUserInfoMail::class);
    }

    public function test_cannot_revert_last_change_when_change_at_is_within_a_billing_period(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->subscribeToPlan('base');
        /** @var SubscriptionOrder $subscriptionOrder */
        $subscriptionOrder = $pharmacy->subscriptionOrders()->first();
        $invoice = new Invoice([
            'transaction_id' => Str::uuid(),
            'status_code' => InvoiceStatusEnum::CREATED->value,
        ]);
        $invoice->billingAddress()->associate($pharmacy->billingAddress);
        $invoice->save();
        $invoice->subscriptionOrders()->save($subscriptionOrder);

        $owner->currentAssociationMembershipChange()->create([
            'association_id_before' => $owner->association_id,
            'association_id_after' => null,
            'change_at' => $subscriptionOrder->started_at,
            'mode' => AssociationMembershipChangeModeEnum::CHANGE,
        ]);

        $this->assertNotNull($owner->currentAssociationMembershipChange);
        $this->assertNull($owner->currentAssociationMembershipChange->canceled_at);

        $fields = new ActionFields(collect(), collect());
        $models = collect([$owner]);
        $novaAction = new RevertLastAssociationMembershipChangeAction;

        $message = $novaAction->handle($fields, $models);

        $owner->refresh();

        $this->assertIsObject($message);
        $this->assertObjectHasProperty('danger', $message);

        Mail::assertNotQueued(RevertAssociationMembershipUserInfoMail::class);
    }
}
