<?php

namespace Tests\Unit\Nova\Actions;

use App\Nova\Actions\ReactivateMarkedAsDeletedOwner;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class ReactivateMarkedAsDeletedOwnerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_action_fails_when_owner_not_filled(): void
    {
        $dispatcher = User::getEventDispatcher();
        User::unsetEventDispatcher();
        $fields = new ActionFields(collect([]), collect([]));

        $owner = $this->createPharmacyUser(
            [
                'salutation' => '',
                'created_at' => now()->subYear(),
            ],
            false,
            true
        );

        $this->run_user_deletion();
        $owner->refresh();

        User::setEventDispatcher($dispatcher);

        $collection = collect([
            $owner,
        ]);

        $action = new ReactivateMarkedAsDeletedOwner;
        $actual = $action->handle($fields, $collection);

        $this->assertEquals('Dieser Inhaber hat nicht alle Informationen ausgefüllt.', $actual['danger']);
    }

    public function test_action_fails_when_owner_not_reactivatable(): void
    {
        $dispatcher = User::getEventDispatcher();
        User::unsetEventDispatcher();
        $fields = new ActionFields(collect([]), collect([]));

        $owner = $this->createPharmacyUser(
            [
                'created_at' => now()->subYear(),
            ],
            false,
            true
        );

        User::setEventDispatcher($dispatcher);

        $collection = collect([
            $owner,
        ]);

        $action = new ReactivateMarkedAsDeletedOwner;
        $actual = $action->handle($fields, $collection);

        $this->assertEquals('Der Inhaber kann nicht reaktiviert werden.', $actual['danger']);
    }

    public function test_action_runs_correctly(): void
    {
        Http::fake([
            '*' => Http::sequence()
                ->push(['access_token' => $this->faker->uuid])
                ->push(['access_token' => $this->faker->uuid]),
        ]);

        $dispatcher = User::getEventDispatcher();
        User::unsetEventDispatcher();
        $fields = new ActionFields(collect([]), collect([]));

        $owner = $this->createPharmacyUser(
            [
                'created_at' => now()->subYear(),
            ],
            false,
            true
        );

        $this->run_user_deletion();
        $owner->refresh();

        User::setEventDispatcher($dispatcher);

        $collection = collect([
            $owner,
        ]);

        $action = new ReactivateMarkedAsDeletedOwner;
        $actual = $action->handle($fields, $collection);

        $this->assertEquals('Der Inhaber wurde erfolgreich reaktiviert.', $actual['message']);

        $owner->refresh();

        $this->assertNotSoftDeleted($owner);
        $this->assertStringStartsNotWith('tos_deleted_', $owner->email);
    }

    public function test_action_cant_reactivate_existing_email(): void
    {
        Http::fake([
            '*' => Http::sequence()
                ->push(['access_token' => $this->faker->uuid])
                ->push(['access_token' => $this->faker->uuid]),
        ]);

        $dispatcher = User::getEventDispatcher();
        User::unsetEventDispatcher();
        $fields = new ActionFields(collect([]), collect([]));

        $email = '<EMAIL>';

        $owner = $this->createPharmacyUser(
            [
                'email' => $email,
                'created_at' => now()->subYear(),
            ],
            false,
            true
        );

        $this->run_user_deletion();
        $owner->refresh();

        $this->createPharmacyUser(['email' => $email]);

        User::setEventDispatcher($dispatcher);

        $collection = collect([
            $owner,
        ]);

        $action = new ReactivateMarkedAsDeletedOwner;
        $actual = $action->handle($fields, $collection);

        $this->assertEquals('Die Email-Adresse des Inhabers (<EMAIL>) ist bereits vergeben.', $actual['danger']);

        $owner->refresh();

        $this->assertSoftDeleted($owner);
        $this->assertStringStartsWith('tos_deleted_', $owner->email);
    }

    private function run_user_deletion(): void
    {
        $this->artisan('user:mark-as-deleted')
            ->expectsQuestion('Date from which the TOS must be accepted', '01.01.2023')
            ->expectsConfirmation('Is the date 2023-01-01 correct?', 'yes');
    }
}
