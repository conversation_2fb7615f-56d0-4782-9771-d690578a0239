<?php

namespace Tests\Unit\Nova\Actions;

use App\Association;
use App\Enums\AssociationEnum;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Enums\Settings\PharmacySettingTypes;
use App\Mail\CancelAssociationMembershipUserInfoMail;
use App\Mail\ChangeAssociationMembershipUserInfoMail;
use App\Nova\Actions\ChangeAssociationMembership;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Http\Requests\NovaRequest;
use Tests\TestCase;

class ChangeAssociationMembershipTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_prepares_to_add_membership_from_non_to_westphalia(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        Mail::fake();

        $association = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = null;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = null;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => null]);
        $pharmacy->subscribeToPlan('extended');
        $pharmacy->refresh();

        $this->assertNotSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertNotSame($association->id, $owner->brochureCode->association_id);
        $this->assertNotSame($association->id, $pharmacy->association_id);

        $fields = new ActionFields(collect([
            'change_at' => now()->format('Y-m-d'),
            'association_id' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new ChangeAssociationMembership;

        $actionResponse = $novaAction->handle($fields, $models);

        $this->assertIsObject($actionResponse);
        $this->assertObjectHasProperty('message', $actionResponse);

        Mail::assertQueued(ChangeAssociationMembershipUserInfoMail::class);

        $owner->refresh();
        $pharmacy->refresh();
        $associationMembershipChange = $owner->currentAssociationMembershipChange->refresh();

        $this->assertNull($owner->pharmacyProfile->association_id);
        $this->assertNull($owner->brochureCode->association_id);
        $this->assertNull($pharmacy->association_id);
        $this->assertSame($association->id, $associationMembershipChange->association_id_after);

        $this->assertNull($associationMembershipChange->association_id_before);

        $this->assertNull($associationMembershipChange->change_done_at);

        $this->assertSame(AssociationMembershipChangeModeEnum::ADD, $associationMembershipChange->mode);

        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value
        );
        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value
        );
        $this->assertCount(1, $pharmacy->activeSubscriptions);
    }

    public function test_it_prepares_to_change_membership_from_brandenburg_to_westphalia(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        Mail::fake();

        $brandenburg = Association::find(AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = $brandenburg->id;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = $brandenburg->id;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => $brandenburg->id]);
        $pharmacy->refresh();

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->pharmacyProfile->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->brochureCode->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $pharmacy->association_id
        );

        $fields = new ActionFields(collect([
            'change_at' => now()->format('Y-m-d'),
            'association_id' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new ChangeAssociationMembership;

        $actionResponse = $novaAction->handle($fields, $models);

        $this->assertIsObject($actionResponse);
        $this->assertObjectHasProperty('message', $actionResponse);

        $owner->refresh();
        $pharmacy->refresh();
        $associationMembershipChange = $owner->currentAssociationMembershipChange->refresh();

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->pharmacyProfile->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->brochureCode->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $pharmacy->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $associationMembershipChange->association_id_before
        );

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $associationMembershipChange->association_id_after
        );

        $this->assertNull($associationMembershipChange->change_done_at);

        $this->assertSame(AssociationMembershipChangeModeEnum::CHANGE, $associationMembershipChange->mode);

        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value
        );
        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value
        );
        $this->assertCount(0, $pharmacy->activeSubscriptions);

        Mail::assertQueued(ChangeAssociationMembershipUserInfoMail::class);
    }

    public function test_it_prepares_to_change_membership_from_westphalia_to_non(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        Mail::fake();

        $westphalia = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = $westphalia->id;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = $westphalia->id;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => $westphalia->id]);
        $pharmacy->refresh();

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $owner->pharmacyProfile->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $owner->brochureCode->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $pharmacy->association_id
        );

        $fields = new ActionFields(collect([
            'change_at' => now()->format('Y-m-d'),
            'association_id' => '0',
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new ChangeAssociationMembership;

        $actionResponse = $novaAction->handle($fields, $models);

        $this->assertIsObject($actionResponse);
        $this->assertObjectHasProperty('message', $actionResponse);

        $owner->refresh();
        $pharmacy->refresh();
        $associationMembershipChange = $owner->currentAssociationMembershipChange->refresh();

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $owner->pharmacyProfile->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $owner->brochureCode->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $pharmacy->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $associationMembershipChange->association_id_before
        );

        $this->assertNull($associationMembershipChange->association_id_after);

        $this->assertNull($associationMembershipChange->change_done_at);

        $this->assertSame(AssociationMembershipChangeModeEnum::DELETE, $associationMembershipChange->mode);

        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value
        );
        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value
        );
        $this->assertCount(0, $pharmacy->activeSubscriptions);

        Mail::assertQueued(CancelAssociationMembershipUserInfoMail::class);
    }

    public function test_it_does_not_prepare_to_change_membership(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = null;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = null;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => null]);
        $pharmacy->refresh();

        $this->assertNull($owner->pharmacyProfile->association_id);
        $this->assertNull($owner->brochureCode->association_id);
        $this->assertNull($pharmacy->association_id);

        $fields = new ActionFields(collect([
            'change_at' => now()->format('Y-m-d'),
            'association_id' => null,
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new ChangeAssociationMembership;

        $actionResponse = $novaAction->handle($fields, $models);

        $this->assertIsObject($actionResponse);
        $this->assertObjectHasProperty('danger', $actionResponse);

        $owner->refresh();
        $pharmacy->refresh();

        $this->assertNull($owner->pharmacyProfile->association_id);

        $this->assertNull($owner->brochureCode->association_id);

        $this->assertNull($pharmacy->association_id);

        $this->assertNull($owner->currentAssociationMembershipChange);

        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value
        );
        $this->assertTrue(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value
        );
        $this->assertCount(0, $pharmacy->activeSubscriptions);

        Mail::assertNothingOutgoing();
    }

    public function test_fields_return_correctly(): void
    {
        $action = new ChangeAssociationMembership;
        $fields = $action->fields(new NovaRequest);

        $this->assertCount(2, $fields);

        $associationField = $fields[0];
        $associations = AssociationEnum::getLabels();
        $expectedAssociations = $associations;
        $expectedAssociations[0] = 'Verbandslos';
        ksort($expectedAssociations);

        $this->assertSame('select-field', $associationField->component);
        $this->assertSame('Verband', $associationField->name);
        $this->assertSame('association_id', $associationField->attribute);
        $this->assertObjectHasProperty('optionsCallback', $associationField);
        $this->assertIsArray($associationField->optionsCallback);

        $actualOptions = $associationField->optionsCallback;
        ksort($actualOptions);

        $this->assertSame($expectedAssociations, $actualOptions);

        $dateField = $fields[1];

        $this->assertSame('date-field', $dateField->component);
        $this->assertSame('Wechseldatum', $dateField->name);
        $this->assertSame('change_at', $dateField->attribute);
    }
}
