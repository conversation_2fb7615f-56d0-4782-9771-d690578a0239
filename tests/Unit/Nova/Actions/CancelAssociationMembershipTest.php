<?php

namespace Tests\Unit\Nova\Actions;

use App\Association;
use App\Enums\AssociationEnum;
use App\Mail\ChangeAssociationMembershipUserInfoMail;
use App\Nova\Actions\CancelAssociationMembership;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class CancelAssociationMembershipTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_prepares_to_remove_membership_from_westphalia_to_non(): void
    {
        Mail::fake();

        $association = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V]);
        $pharmacy->refresh();

        $this->assertSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertSame($association->id, $owner->brochureCode->association_id);
        $this->assertSame($association->id, $pharmacy->association_id);

        $fields = new ActionFields(collect([
            'terminated_at' => now()->format('Y-m-d'),
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new CancelAssociationMembership;

        $message = $novaAction->handle($fields, $models);

        $this->assertObjectHasProperty('message', $message);

        $owner->refresh();
        $pharmacy->refresh();
        $associationMembershipChange = $owner->currentAssociationMembershipChange->refresh();

        $this->assertSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertSame($association->id, $owner->brochureCode->association_id);
        $this->assertSame($association->id, $pharmacy->association_id);
        $this->assertSame($association->id, $associationMembershipChange->association_id_before);

        $this->assertNull($associationMembershipChange->association_id_after);

        $this->assertNull($associationMembershipChange->change_done_at);

        Mail::assertQueued(ChangeAssociationMembershipUserInfoMail::class);
    }

    public function test_it_does_not_prepare_to_cancel_because_termination_already_done(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = null;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = null;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => null]);
        $pharmacy->refresh();

        $this->assertNull($owner->pharmacyProfile->association_id);
        $this->assertNull($owner->brochureCode->association_id);
        $this->assertNull($pharmacy->association_id);

        $fields = new ActionFields(collect([
            'terminated_at' => now()->format('Y-m-d'),
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new CancelAssociationMembership;

        $message = $novaAction->handle($fields, $models);

        $this->assertObjectHasProperty('danger', $message);

        $owner->refresh();
        $pharmacy->refresh();

        $this->assertNull($owner->pharmacyProfile->association_id);
        $this->assertNull($owner->brochureCode->association_id);
        $this->assertNull($owner->currentAssociationMembershipChange);
        $this->assertNull($pharmacy->association_id);

        Mail::assertNothingQueued();
    }

    public function test_available_dates(): void
    {
        $novaAction = new CancelAssociationMembership;

        $owner = $this->createPharmacyUser();
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertIsArray($dates);
        $this->assertCount(13, $dates);

        foreach ($dates as $date) {
            $this->assertInstanceOf(Carbon::class, $date);
            $this->assertTrue($date->isFuture());
        }
    }

    // case 1 earliest date ist 15.12.24 -> frühester Kündigungstermin ist 31.12.24
    public function test_earliest_cancel_date_is_end_of_month_of_creation_month(): void
    {
        $novaAction = new CancelAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => now()->subMonthNoOverflow()->setDay(15)]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(now()->subMonthNoOverflow()->endOfMonth()));
    }

    // case 2 earliest date ist 01.01.25 -> frühester Kündigungstermin ist 31.12.24
    public function test_earliest_cancel_date_is_one_day_before_latest_entry(): void
    {
        $novaAction = new CancelAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => now()->subMonthNoOverflow()->startOfMonth()]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(now()->subMonthsNoOverflow(2)->endOfMonth()));
    }

    // case 3 earliest date ist 31.12.24 -> frühester Kündigungstermin ist 31.12.24
    public function test_earliest_cancel_date_is_equal_to_creation_day(): void
    {
        $novaAction = new CancelAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => now()->subMonthNoOverflow()->endOfMonth()]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(now()->subMonthNoOverflow()->endOfMonth()));
    }

    // case 4 earliest date ist 01.02.24 -> frühester Kündigungstermin ist 31.12.24 wegen höchstens 6 Monate zurück
    public function test_cancel_date_boundary(): void
    {
        $novaAction = new CancelAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => now()->subMonthsNoOverflow(12)]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(now()->subMonthsNoOverflow(6)->endOfMonth()));
    }
}
