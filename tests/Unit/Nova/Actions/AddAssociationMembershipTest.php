<?php

namespace Tests\Unit\Nova\Actions;

use App\Association;
use App\Enums\AssociationEnum;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Mail\ChangeAssociationMembershipUserInfoMail;
use App\Nova\Actions\AddAssociationMembership;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Laravel\Nova\Fields\ActionFields;
use Tests\TestCase;

class AddAssociationMembershipTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_prepares_to_add_membership_from_non_to_westphalia(): void
    {
        Mail::fake();

        $association = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = null;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = null;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => null]);
        $pharmacy->refresh();

        $this->assertNotSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertNotSame($association->id, $owner->brochureCode->association_id);
        $this->assertNotSame($association->id, $pharmacy->association_id);

        $fields = new ActionFields(collect([
            'change_at' => now()->format('Y-m-d'),
            'association_id' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new AddAssociationMembership;

        $actionResponse = $novaAction->handle($fields, $models);

        $this->assertIsObject($actionResponse);
        $this->assertObjectHasProperty('message', $actionResponse);

        Mail::assertQueued(ChangeAssociationMembershipUserInfoMail::class);

        $owner->refresh();
        $pharmacy->refresh();
        $associationMembershipChange = $owner->currentAssociationMembershipChange->refresh();

        $this->assertNull($owner->pharmacyProfile->association_id);
        $this->assertNull($owner->brochureCode->association_id);
        $this->assertNull($pharmacy->association_id);
        $this->assertSame($association->id, $associationMembershipChange->association_id_after);

        $this->assertNull($associationMembershipChange->association_id_before);

        $this->assertNull($associationMembershipChange->change_done_at);

        $this->assertSame(AssociationMembershipChangeModeEnum::ADD, $associationMembershipChange->mode);
    }

    public function test_it_prepares_to_change_membership_from_brandenburg_to_westphalia(): void
    {
        $this->markTestSkipped('AP-2839 will fix this test');
        Mail::fake();

        $brandenburg = Association::find(AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = $brandenburg->id;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = $brandenburg->id;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => $brandenburg->id]);
        $pharmacy->refresh();

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->pharmacyProfile->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->brochureCode->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $pharmacy->association_id
        );

        $fields = new ActionFields(collect([
            'change_at' => now()->format('Y-m-d'),
            'association_id' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new AddAssociationMembership;

        $actionResponse = $novaAction->handle($fields, $models);

        $this->assertIsObject($actionResponse);
        $this->assertObjectHasProperty('message', $actionResponse);

        $owner->refresh();
        $pharmacy->refresh();
        $associationMembershipChange = $owner->currentAssociationMembershipChange->refresh();

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->pharmacyProfile->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->brochureCode->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $pharmacy->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $associationMembershipChange->association_id_before
        );

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $associationMembershipChange->association_id_after
        );

        $this->assertNull($associationMembershipChange->change_done_at);

        $this->assertSame(AssociationMembershipChangeModeEnum::CHANGE, $associationMembershipChange->mode);

        Mail::assertQueued(ChangeAssociationMembershipUserInfoMail::class);
    }

    public function test_it_does_not_prepare_to_change_membership(): void
    {
        Mail::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = null;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = null;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => null]);
        $pharmacy->refresh();

        $this->assertNull($owner->pharmacyProfile->association_id);
        $this->assertNull($owner->brochureCode->association_id);
        $this->assertNull($pharmacy->association_id);

        $fields = new ActionFields(collect([
            'change_at' => now()->format('Y-m-d'),
            'association_id' => null,
        ]), collect());
        $models = collect([$owner]);
        $novaAction = new AddAssociationMembership;

        $this->expectException(\AssertionError::class);
        $novaAction->handle($fields, $models);

        Mail::assertNothingOutgoing();
    }

    // case 1 earliest date ist 15.11.24 -> frühester Eintritt ist 01.01.25
    public function test_earliest_entry_date_is_end_of_quarter(): void
    {
        $this->travelTo(Carbon::create(2025, 05, 15));
        $novaAction = new AddAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => Carbon::create(2024, 11, 15)]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(Carbon::create(2025, 01, 01)));
    }

    // case 2 earliest date ist 01.01.25 -> frühester Eintritt ist 01.01.25
    public function test_earliest_entry_date_is_start_of_quarter(): void
    {
        $this->travelTo(Carbon::create(2025, 05, 15));
        $novaAction = new AddAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => Carbon::create(2025, 1, 1)]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(Carbon::create(2025, 01, 01)));
    }

    // case 3 earliest date ist 31.12.24 -> frühester Eintritt ist 01.01.25
    public function test_earliest_entry_date_is_a_day_later(): void
    {
        $this->travelTo(Carbon::create(2025, 05, 15));
        $novaAction = new AddAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => Carbon::create(2024, 12, 31)]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(Carbon::create(2025, 01, 01)));
    }

    // case 4 earliest date ist 01.02.24 -> frühester Eintritt ist 01.01.25 wegen höchstens 2 Quartale zurück
    public function test_earliest_entry_date_boundary(): void
    {
        $this->travelTo(Carbon::create(2025, 05, 15));
        $novaAction = new AddAssociationMembership;

        $owner = $this->createPharmacyUser(['created_at' => Carbon::create(2024, 2, 1)]);
        $dates = self::callProtectedMethod($novaAction, 'getAvailableDates', [$owner]);

        $this->assertTrue(collect($dates)->first()->isSameDay(Carbon::create(2025, 01, 01)));
    }
}
