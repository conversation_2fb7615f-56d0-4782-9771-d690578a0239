<?php

namespace Tests\Unit\Traits;

use App\Enums\Newsletter\MailcoachList;
use App\Integrations\MailcoachIntegration;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\AccessHelper;
use Tests\TestCase;

class InteractsWithMailcoachTest extends TestCase
{
    use AccessHelper;
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        User::factory()->create()->setIntegration(new MailcoachIntegration(advertisingListId: '308c3d29-c847-43f1-a036-be5e01a7c435'));
        User::factory()->create()->setIntegration(new MailcoachIntegration(advertisingListId: '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c'));
        User::factory()->create()->setIntegration(new MailcoachIntegration(systemListId: '67b1364c-0ed0-429b-a0c5-a61b7c2999a4t', advertisingListId: 'f3a3ce94-ea02-4a37-ae77-4dcb2601398e'));
        User::factory()->create()->setIntegration(new MailcoachIntegration(systemListId: '408502bc-7ad9-4b13-86f7-4b39895b4b02'));
        User::factory()->create();
    }

    public function test_it_retrieves_all_users_subscribed_to_a_certain_list(): void
    {
        $matchedUserIds = User::subscribedToMailcoachList(MailcoachList::Advertising)->pluck('id')->values()->toArray();

        $this->assertCount(3, $matchedUserIds);
        $this->assertEquals($matchedUserIds, [1, 2, 3]);

        $matchedUserIds = User::subscribedToMailcoachList(MailcoachList::System)->pluck('id')->values()->toArray();

        $this->assertCount(2, $matchedUserIds);
        $this->assertEquals($matchedUserIds, [3, 4]);
    }

    public function test_it_retrieves_all_users_subscribed_to_a_certain_combination_of_lists(): void
    {
        $matchedUserIds = User::subscribedToMailcoachList([MailcoachList::System, MailcoachList::Advertising])->pluck('id')->values()->toArray();

        $this->assertCount(1, $matchedUserIds);
        $this->assertEquals($matchedUserIds, [3]);
    }

    public function test_it_retrieves_all_users_not_subscribed_to_a_certain_list(): void
    {
        $matchedUserIds = User::notSubscribedToMailcoachList(MailcoachList::Advertising)->pluck('id')->values()->toArray();

        $this->assertCount(2, $matchedUserIds);
        $this->assertEquals($matchedUserIds, [4, 5]);

        $matchedUserIds = User::notSubscribedToMailcoachList(MailcoachList::System)->pluck('id')->values()->toArray();

        $this->assertCount(3, $matchedUserIds);
        $this->assertEquals($matchedUserIds, [1, 2, 5]);
    }

    public function test_it_retrieves_all_users_not_subscribed_to_a_certain_combination_of_lists(): void
    {
        $matchedUserIds = User::notSubscribedToMailcoachList([MailcoachList::System, MailcoachList::Advertising])->pluck('id')->values()->toArray();

        $this->assertCount(4, $matchedUserIds);
        $this->assertEquals($matchedUserIds, [1, 2, 4, 5]);
    }

    public function test_it_retrieves_a_user_by_its_mailcoach_subscriber_id(): void
    {
        $matchedUserIds = User::byMailcoachSubscriberId('408502bc-7ad9-4b13-86f7-4b39895b4b02')->pluck('id')->values()->toArray();

        $this->assertCount(1, $matchedUserIds);
        $this->assertEquals($matchedUserIds, [4]);
    }

    public function test_it_fetches_active_subscriptions_of_a_user(): void
    {
        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration(advertisingListId: '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c'));

        $this->assertSame(
            ['advertisingListId' => '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c'],
            $user->mailcoachSubscriptions()->toArray()
        );

        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration(
            systemListId: '67b1364c-0ed0-429b-a0c5-a61b7c2999a4t',
            advertisingListId: '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c')
        );

        $this->assertSame([
            'systemListId' => '67b1364c-0ed0-429b-a0c5-a61b7c2999a4t',
            'advertisingListId' => '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c',
        ], $user->mailcoachSubscriptions()->toArray());
    }

    public function test_if_there_are_active_subscriptions_for_a_user(): void
    {
        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration(
            advertisingListId: '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c')
        );

        $this->assertTrue($user->hasMailcoachSubscriptions());

        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration(
            systemListId: '67b1364c-0ed0-429b-a0c5-a61b7c2999a4t',
            advertisingListId: '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c')
        );

        $this->assertTrue($user->hasMailcoachSubscriptions());

        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration);

        $this->assertFalse($user->hasMailcoachSubscriptions());

        $user = User::factory()->create();

        $this->assertFalse($user->hasMailcoachSubscriptions());
    }

    public function test_if_there_are_specific_subscriptions_for_a_user(): void
    {
        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration(
            advertisingListId: '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c')
        );

        $this->assertFalse($user->hasMailcoachSubscriptions([MailcoachList::Advertising, MailcoachList::System]));
        $this->assertTrue($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
        $this->assertFalse($user->hasMailcoachSubscriptions(MailcoachList::System));

        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration(
            systemListId: '67b1364c-0ed0-429b-a0c5-a61b7c2999a4t')
        );

        $this->assertFalse($user->hasMailcoachSubscriptions([MailcoachList::Advertising, MailcoachList::System]));
        $this->assertFalse($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
        $this->assertTrue($user->hasMailcoachSubscriptions(MailcoachList::System));

        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration(
            systemListId: '67b1364c-0ed0-429b-a0c5-a61b7c2999a4t',
            advertisingListId: '5ccc00eb-caf3-455d-b8ce-a0b4aa84893c')
        );

        $this->assertTrue($user->hasMailcoachSubscriptions([MailcoachList::Advertising, MailcoachList::System]));
        $this->assertTrue($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
        $this->assertTrue($user->hasMailcoachSubscriptions(MailcoachList::System));

        $user = User::factory()->create();
        $user->setIntegration(new MailcoachIntegration);

        $this->assertFalse($user->hasMailcoachSubscriptions([MailcoachList::Advertising, MailcoachList::System]));
        $this->assertFalse($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
        $this->assertFalse($user->hasMailcoachSubscriptions(MailcoachList::System));

        $user = User::factory()->create();

        $this->assertFalse($user->hasMailcoachSubscriptions([MailcoachList::Advertising, MailcoachList::System]));
        $this->assertFalse($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
        $this->assertFalse($user->hasMailcoachSubscriptions(MailcoachList::System));
    }
}
