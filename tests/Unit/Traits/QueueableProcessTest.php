<?php

namespace Tests\Unit\Traits;

use App\Enums\Newsletter\MailcoachList;
use App\Events\PharmacySaved;
use App\Jobs\SubscribeToMailcoachListJob;
use App\Pharmacy;
use App\Processes\Payload;
use App\Processes\Process;
use App\Processes\ProcessJob;
use App\Traits\QueueableProcess;
use App\User;
use Closure;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class QueueableProcessTest extends TestCase
{
    public function test_process_has_been_dispatched(): void
    {
        Queue::fake();

        $process = new class extends Process
        {
            use QueueableProcess;

            public function tasks(): array
            {
                return [];
            }
        };

        $payload = new class extends Payload {};
        $process->onQueue()->run($payload);

        Queue::assertPushed(ProcessJob::class);
    }

    public function test_process_does_not_dispatch(): void
    {
        Queue::fake();

        $process = new class extends Process
        {
            use QueueableProcess;

            public function tasks(): array
            {
                return [];
            }
        };

        $payload = new class extends Payload {};
        $process->run($payload);

        Queue::assertNotPushed(ProcessJob::class);
    }

    public function test_process_is_executed(): void
    {
        Event::fake();
        Queue::fake();

        $pharmacy = Pharmacy::factory()->createQuietly();

        $process = new class extends Process
        {
            use QueueableProcess;

            public function tasks(): array
            {
                return [
                    new class
                    {
                        public function __invoke($payload, Closure $next)
                        {
                            $payload->pharmacy->name = 'Apotheke';
                            $payload->pharmacy->save();

                            return $next($payload);
                        }
                    },
                ];
            }
        };

        $payload = new class($pharmacy) extends Payload
        {
            public function __construct(
                public Pharmacy $pharmacy,
            ) {}
        };

        $process->onQueue()->run($payload);

        Queue::assertPushed(ProcessJob::class);
        Queue::assertPushed(function (ProcessJob $job) {
            $job->handle();

            return true;
        });
        Event::assertDispatched(PharmacySaved::class);
    }

    public function test_process_can_dispatch_job(): void
    {
        Queue::fake();

        $user = User::factory()->create();
        assert($user instanceof User);

        $process = new class extends Process
        {
            use QueueableProcess;

            public function tasks(): array
            {
                return [
                    new class
                    {
                        public function __invoke($payload, Closure $next): object
                        {
                            SubscribeToMailcoachListJob::dispatch($payload->user, MailcoachList::Advertising);

                            return $next($payload);
                        }
                    },
                ];
            }
        };

        $payload = new class($user) extends Payload
        {
            public function __construct(
                public User $user,
            ) {}
        };

        $process->onQueue()->run($payload);

        Queue::assertPushed(ProcessJob::class);
        Queue::assertPushed(function (ProcessJob $job) {
            $job->handle();

            return true;
        });
        Queue::assertPushed(SubscribeToMailcoachListJob::class);
    }
}
