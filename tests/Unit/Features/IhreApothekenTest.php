<?php

namespace Tests\Unit\Features;

use App\Features\IhreApotheken;
use App\Settings\IaSettings;
use Illuminate\Support\Carbon;
use Laravel\Pennant\Feature;
use Tests\TestCase;

class IhreApothekenTest extends TestCase
{
    public function test_it_does_not_resolve_before(): void
    {
        Carbon::setTestNow('2024-06-30');

        $settings = new IaSettings;
        $settings->enabledAt = Carbon::parse('2025-01-01');
        $settings->save();

        $this->assertFalse(Feature::active(IhreApotheken::class));
    }

    public function test_it_does_resolve_after(): void
    {
        Carbon::setTestNow('2025-06-30');

        $settings = new IaSettings;
        $settings->enabledAt = Carbon::parse('2025-01-01');
        $settings->save();

        $this->assertTrue(Feature::active(IhreApotheken::class));
    }
}
