<?php

namespace Tests\Unit\Enums;

use App\Enums\Newsletter\MailcoachList;
use App\Settings\MailcoachSettings;
use Tests\TestCase;

class MailcoachListTest extends TestCase
{
    public function test_it_retrieves_uuid(): void
    {
        app(MailcoachSettings::class)->advertisingMailingListId = 'test-advertising-uuid';
        app(MailcoachSettings::class)->systemMailingListId = 'test-system-uuid';

        $this->assertEquals('test-advertising-uuid', MailcoachList::Advertising->uuid());
        $this->assertEquals('test-system-uuid', MailcoachList::System->uuid());
    }

    public function test_it_retrieves_from_uuid(): void
    {
        app(MailcoachSettings::class)->advertisingMailingListId = 'test-advertising-uuid';
        app(MailcoachSettings::class)->systemMailingListId = 'test-system-uuid';

        $this->assertEquals(MailcoachList::Advertising, MailcoachList::fromUuid('test-advertising-uuid'));
        $this->assertEquals(MailcoachList::System, MailcoachList::fromUuid('test-system-uuid'));
    }

    public function test_it_retrieves_reference_field_name(): void
    {
        $this->assertEquals(
            'advertisingListId',
            MailcoachList::Advertising->referenceFieldName()
        );

        $this->assertEquals(
            'systemListId',
            MailcoachList::System->referenceFieldName()
        );
    }

    public function test_it_retrieves_from_reference_field_name(): void
    {
        $this->assertEquals(
            MailcoachList::Advertising,
            MailcoachList::fromReferenceFieldName('advertisingListId')
        );

        $this->assertEquals(
            MailcoachList::System,
            MailcoachList::fromReferenceFieldName('systemListId')
        );
    }
}
