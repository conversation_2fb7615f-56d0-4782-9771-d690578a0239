<?php

namespace Tests\Unit\Support\NotificationCenter;

use App\Association;
use App\AssociationNews;
use App\Enums\AssociationEnum;
use App\Enums\AssociationNews\AssociationNewsNotificationOptionEnum;
use App\Enums\KimAddressStatus;
use App\KimAddress;
use App\Settings\NgdaSettings;
use App\Settings\SubscriptionSettings;
use App\Support\NotificationCenter\ApoGuideQRCodeNotification;
use App\Support\NotificationCenter\ConnectNIDNotification;
use App\Support\NotificationCenter\NewAssociationNewsNotification;
use App\Support\NotificationCenter\NewsletterNotification;
use App\Support\NotificationCenter\NotificationList;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use ReflectionClass;
use Tests\TestCase;

class NotificationListTest extends TestCase
{
    use RefreshDatabase;

    public function test_authenticated_user_can_see(): void
    {
        $this->actingAs(User::factory()->create());

        $this->assertTrue(NotificationList::isVisible());
    }

    public function test_unauthenticated_user_cannot_see(): void
    {
        $this->assertFalse(NotificationList::isVisible());
    }

    /** @group ap-2182 */
    public function test_it_gets_all(): void
    {
        $association = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);

        $ngdaSettings = app(NgdaSettings::class);
        $ngdaSettings->enabled_at = now()->subDay();
        $ngdaSettings->save();

        $subscriptionSettings = app(SubscriptionSettings::class);
        $subscriptionSettings->stripeEnabledAt = now()->addDay();
        $subscriptionSettings->save();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $owner->pharmacyProfile->update(['association_id' => $association->id]);

        KimAddress::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => KimAddressStatus::ACTIVATED,
        ]);

        $this->actingAs($owner);
        $notifications = NotificationList::all();

        $this->assertCount(3, $notifications);
        $this->assertInstanceOf(ConnectNIDNotification::class, $notifications[0]);
        $this->assertInstanceOf(ApoGuideQRCodeNotification::class, $notifications[1]);
        $this->assertInstanceOf(NewsletterNotification::class, $notifications[2]);

        $this->createAssociationNews(association: $association);

        $this->assertCount(4, $notifications = NotificationList::all());
        $this->assertInstanceOf(ConnectNIDNotification::class, $notifications[0]);
        $this->assertInstanceOf(ApoGuideQRCodeNotification::class, $notifications[1]);
        $this->assertInstanceOf(NewAssociationNewsNotification::class, $notifications[2]);
        $this->assertInstanceOf(NewsletterNotification::class, $notifications[3]);
    }

    /** @group ap-2182 */
    public function test_it_counts(): void
    {
        $association = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);

        $ngdaSettings = app(NgdaSettings::class);
        $ngdaSettings->enabled_at = now()->subDay();
        $ngdaSettings->save();

        $subscriptionSettings = app(SubscriptionSettings::class);
        $subscriptionSettings->stripeEnabledAt = now()->addDay();
        $subscriptionSettings->save();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $owner->pharmacyProfile->update(['association_id' => $association->id]);

        KimAddress::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => KimAddressStatus::ACTIVATED,
        ]);
        $this->actingAs($owner);

        $this->assertEquals(3, NotificationList::count());

        $this->createAssociationNews(association: $association);

        $this->assertEquals(4, NotificationList::count());
        $this->assertSame(1, NewAssociationNewsNotification::count());
    }

    public function test_to_livewire_and_from_livewire_methods_exist_in_notification_classes(): void
    {
        $methodsToCheck = ['fromLivewire', 'toLivewire'];

        $notificationListReflector = new ReflectionClass(NotificationList::class);
        $notifications = $notificationListReflector->getStaticPropertyValue('notifications');

        foreach ($notifications as $notificationClass) {
            if (class_exists($notificationClass)) {
                $classReflector = new ReflectionClass($notificationClass);
                foreach ($methodsToCheck as $method) {
                    $methodExistsInConcreteClass = $classReflector->hasMethod($method) &&
                        $classReflector->getMethod($method)->getDeclaringClass()->getName() === $notificationClass;

                    $this->assertTrue(
                        $methodExistsInConcreteClass,
                        "Method $method must exist in $notificationClass and and not only in the abstract class"
                    );
                }
            }
        }
    }

    private function createAssociationNews(?User $associationUser = null, ?Association $association = null): void
    {
        if ($associationUser === null) {
            [$associationUser] = $this->createAssociationUser($association);
        }

        AssociationNews::factory()->create([
            'association_id' => $association->id,
            'user_id' => $associationUser->id,
            'notification_option' => [
                AssociationNewsNotificationOptionEnum::Mail,
                AssociationNewsNotificationOptionEnum::Notification,
            ],
        ]);
    }
}
