<?php

namespace Tests\Unit\Models;

use App\Association;
use App\User;
use App\UserPharmacyProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserPharmacyProfileTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_belongs_to_an_user(): void
    {
        $user = User::factory()->create();

        $association = Association::factory()->create();
        $profile = UserPharmacyProfile::create([
            'user_id' => $user->id,
            'association_id' => $association->id,
        ]);

        $user->refresh();

        $this->assertTrue($profile->user->is($user));
        $this->assertTrue($user->pharmacyProfile->is($profile->fresh()));
    }

    /** @test */
    public function it_belongs_to_an_association(): void
    {
        $user = User::factory()->create();

        $association = Association::factory()->create();
        $profile = UserPharmacyProfile::create([
            'user_id' => $user->id,
            'association_id' => $association->id,
        ]);

        $this->assertTrue($profile->association->is($association));
        $this->assertTrue($association->userPharmacyProfiles->first()->is($profile->fresh()));
    }
}
