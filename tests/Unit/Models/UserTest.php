<?php

namespace Tests\Unit\Models;

use App\Apomail;
use App\Association;
use App\AssociationMembershipChange;
use App\AssociationMembershipHistory;
use App\BillingAddress;
use App\Enums\ApomailStatus;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Enums\AssociationRoleEnum;
use App\Enums\PasswordStatusEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\SalutationEnum;
use App\User;
use App\UserAssociationProfile;
use App\UserPharmacyProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_check_if_email_is_verified(): void
    {
        /** @var User $user */
        $user = User::factory()->create(['email_verified_at' => null]);

        $this->assertFalse($user->emailVerified());

        $user->email_verified_at = now();
        $user->save();
        $this->assertTrue($user->emailVerified());
    }

    public function test_it_can_get_his_full_name(): void
    {
        $user = User::factory()->create([
            'title' => 'Prof. Dr.',
            'first_name' => 'Willi',
            'last_name' => 'Wind',
        ]);

        $expected = 'Prof. Dr. Willi Wind';

        $this->assertSame($expected, $user->name);
    }

    public function test_associations(): void
    {
        /** @var User $user */
        $user = User::factory()->create();
        /** @var Association $association */
        $association = Association::factory()->create();

        $association->assignUser($user, AssociationRoleEnum::ADMIN);

        $actual = $user->associations()->get();
        $this->assertTrue($association->assignedUsers->first()->is($user));

        $this->assertCount(1, $actual);
    }

    public function test_has_association_role(): void
    {
        $user = User::factory()->create();
        $association = Association::factory()->create();

        $association->assignUser($user, AssociationRoleEnum::ADMIN);

        $actual = $user->hasAssociationRole($association, AssociationRoleEnum::ADMIN);

        $this->assertTrue($actual);
    }

    public function test_is_pharmacy_user(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->assertFalse($user->isPharmacyUser());

        UserPharmacyProfile::create([
            'user_id' => $user->id,
        ]);

        $this->assertTrue($user->refresh()->isPharmacyUser());
    }

    public function test_is_association_user(): void
    {
        /** @var User $user */
        $user = User::factory()->create();

        $this->assertFalse($user->isAssociationUser());

        UserAssociationProfile::create([
            'user_id' => $user->id,
        ]);

        $this->assertTrue($user->refresh()->isAssociationUser());
    }

    public function test_billing_addresses_default_empty(): void
    {
        $user = User::factory()->create();

        $this->assertCount(0, $user->billingAddressesAll());
    }

    public function test_billing_addresses_from_all_sources(): void
    {
        $ownedBillingAddresses = 0;
        $viewableBillingAddresses = 0;

        [$user] = $this->createPharmacyUserWithPharmacy();
        $secondPharmacy = $this->createPharmacy();
        $secondPharmacy->assignUser($user, PharmacyRoleEnum::OWNER);
        $secondPharmacy->save();
        $viewableBillingAddresses += 2;

        $billingAddresses = BillingAddress::factory()->times(2)->create([
            'user_id' => $user->id,
        ]);
        $ownedBillingAddresses += 2;
        $viewableBillingAddresses += 2;

        $invoices = $this->createInvoices(3);
        $invoices->last()->billingAddress()->associate($billingAddresses->first());
        $invoices->last()->save();

        $this->assertCount($ownedBillingAddresses, $user->refresh()->billingAddresses);
        $this->assertCount($viewableBillingAddresses, $user->refresh()->billingAddressesAll());
    }

    public function test_it_can_check_if_it_belongs_to_an_association(): void
    {
        $user = $this->createPharmacyUser();

        $association = Association::factory()->create();

        $user->pharmacyProfile->update([
            'association_id' => $association->id,
        ]);

        $this->assertTrue($user->belongsToAssociation($association));
        $this->assertTrue($user->belongsToAssociation($association->id));
        $this->assertFalse($user->belongsToAssociation($association->id + 1));
        $this->assertFalse($user->belongsToAssociation());

        $user->pharmacyProfile->update([
            'association_id' => null,
        ]);

        $this->assertTrue($user->belongsToAssociation());
        $this->assertFalse($user->belongsToAssociation($association->id));
    }

    public function test_it_can_check_if_it_belongs_to_any_association(): void
    {
        $user = $this->createPharmacyUser();

        $association = Association::factory()->create();

        $user->pharmacyProfile->update([
            'association_id' => $association->id,
        ]);

        $this->assertTrue($user->belongsToAnyAssociation());
        $this->assertTrue($user->belongsToAnyAssociation([$association->id, $association->id + 1]));
        $this->assertTrue($user->belongsToAnyAssociation([(string) $association->id, (string) ($association->id + 1)]));
        $this->assertFalse($user->belongsToAnyAssociation([$association->id + 1]));

        $user->pharmacyProfile->update([
            'association_id' => null,
        ]);

        $this->assertFalse($user->belongsToAnyAssociation());
        $this->assertFalse($user->belongsToAnyAssociation([$association->id + 1]));
        $this->assertFalse($user->belongsToAnyAssociation(['']));
        $this->assertTrue($user->belongsToAnyAssociation(['null']));
    }

    public function test_has_reserved_apomails(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->assertFalse($user->hasReservedApomail());

        $apomail = Apomail::factory()->create([
            'owner_id' => $user,
            'status' => ApomailStatus::RESERVED,
        ]);

        $user->usingApomails()->attach($apomail);

        $this->assertTrue($user->hasReservedApomail());

        $apomail->update([
            'status' => ApomailStatus::ACTIVE,
        ]);

        $this->assertFalse($user->hasReservedApomail());
    }

    public function test_has_registered_apomails(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->assertFalse($user->hasRegisteredApomail());

        $apomail = Apomail::factory()->create([
            'owner_id' => $user,
            'status' => ApomailStatus::ACTIVE,
        ]);

        $user->usingApomails()->attach($apomail);

        $this->assertTrue($user->hasRegisteredApomail());

        $apomail->update([
            'status' => ApomailStatus::RESERVED,
        ]);

        $this->assertFalse($user->hasRegisteredApomail());
    }

    public function test_it_can_update_all_fillable_properties(): void
    {
        $user = User::factory()->create([
            'salutation' => SalutationEnum::MR,
            'title' => 'Prof. Dr.',
            'first_name' => 'Willi',
            'last_name' => 'Wind',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '0123456789',
            'username' => 'williwind',
            'email_verified_at' => now(),
            'password_status' => PasswordStatusEnum::SECURE,
            'updated_at' => now(),
            'failed_login_attempts' => 0,
            'organisation_uuid' => '12345678-1234-1234-1234-123456789012',
            'is_beta_tester' => true,
            'is_at_idp' => true,
            'matrix_bootstrap_done' => true,
        ]);

        $newDate = now()->addDay();

        $user->update([
            'salutation' => SalutationEnum::MS,
            'title' => 'Only Prof.',
            'first_name' => 'Freddi',
            'last_name' => 'Fred',
            'email' => '<EMAIL>',
            'password' => Hash::make('2ndpassword'),
            'phone' => '012345678910',
            'username' => 'williwind2',
            'email_verified_at' => $newDate,
            'password_status' => PasswordStatusEnum::NOT_JET_CHECKED,
            'failed_login_attempts' => 1,
            'organisation_uuid' => '12345678-1234-1234-1234-12345678901213',
            'is_beta_tester' => false,
            'is_at_idp' => false,
            'matrix_bootstrap_done' => false,
        ]);

        $this->assertSame(SalutationEnum::MS, $user->salutation);
        $this->assertSame('Only Prof.', $user->title);
        $this->assertSame('Freddi', $user->first_name);
        $this->assertSame('Fred', $user->last_name);
        $this->assertSame('<EMAIL>', $user->email);
        $this->assertTrue(Hash::check('2ndpassword', $user->password));
        $this->assertSame('012345678910', $user->phone);
        $this->assertSame('williwind2', $user->username);
        $this->assertTrue($newDate->is($user->email_verified_at));
        $this->assertSame(PasswordStatusEnum::NOT_JET_CHECKED, $user->password_status);
        $this->assertSame(1, $user->failed_login_attempts);
        $this->assertSame('12345678-1234-1234-1234-12345678901213', $user->organisation_uuid);
        $this->assertFalse($user->is_beta_tester);
        $this->assertFalse($user->is_at_idp);
        $this->assertFalse($user->matrix_bootstrap_done);
    }

    public function test_is_owner_of_current_pharmacy(): void
    {
        $employee = User::factory()->create();
        $subOwner = User::factory()->create();
        UserPharmacyProfile::create([
            'user_id' => $employee->id,
            'association_id' => null,
        ]);
        UserPharmacyProfile::create([
            'user_id' => $subOwner->id,
            'association_id' => null,
        ]);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->assignUser($employee, PharmacyRoleEnum::EMPLOYEE);
        $pharmacy->assignUser($subOwner, PharmacyRoleEnum::SUB_OWNER);

        $this->actingAs($employee);

        $this->assertFalse($employee->isOwnerOfPharmacy($pharmacy));

        $this->actingAs($subOwner);

        $this->assertFalse($subOwner->isOwnerOfPharmacy($pharmacy));

        $this->actingAs($owner);

        $this->assertTrue($owner->isOwnerOfPharmacy($pharmacy));
    }

    public function test_user_has_association_membership_changes(): void
    {
        $user = $this->createPharmacyUser();

        AssociationMembershipChange::create([
            'user_id' => $user->id,
            'association_id_before' => $user->pharmacyProfile->association_id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now(),
            'canceled_at' => now(),
        ]);

        AssociationMembershipChange::create([
            'user_id' => $user->id,
            'association_id_before' => $user->pharmacyProfile->association_id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now(),
        ]);

        $this->assertSame(2, $user->refresh()->associationMembershipChanges->count());
    }

    public function test_user_has_current_association_membership_change(): void
    {
        $user = $this->createPharmacyUser();

        AssociationMembershipChange::create([
            'user_id' => $user->id,
            'association_id_before' => $user->pharmacyProfile->association_id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now(),
        ]);

        $user->refresh();

        $this->assertTrue($user->currentAssociationMembershipChange->exists);
        $this->assertSame(1, $user->associationMembershipChanges->count());
    }

    public function test_user_has_no_current_association_membership_change(): void
    {
        $user = $this->createPharmacyUser();

        $this->assertNull($user->currentAssociationMembershipChange);
        $this->assertSame(0, $user->associationMembershipChanges->count());
    }

    public function test_user_has_correct_association_membership_histories(): void
    {
        $user = $this->createPharmacyUser();

        $this->travelTo(now()->addMonthsNoOverflow(2));

        AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => $user->pharmacyProfile->association_id,
            'started_at' => now(),
        ]);

        $this->travelTo(now()->addMonthsNoOverflow(2));

        AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => $user->pharmacyProfile->association_id,
            'started_at' => now(),
        ]);

        $user->refresh();

        $this->assertSame(3, $user->associationMembershipHistories->count());
    }

    public function test_user_has_correct_current_association_membership_history(): void
    {
        $user = $this->createPharmacyUser();

        $this->travelTo(now()->addMonthsNoOverflow(2));

        $associationMembershipHistory = AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => $user->pharmacyProfile->association_id,
            'started_at' => now(),
        ]);

        $user->refresh();

        $this->assertTrue($user->currentAssociationMembershipHistory->exists);
        $this->assertSame(2, $user->associationMembershipHistories->count());
        $this->assertSame($associationMembershipHistory->id, $user->currentAssociationMembershipHistory->id);
    }

    public function test_get_earliest_association_change_date(): void
    {
        // Feste Zeitpunkte für die Tests
        $createdAt1 = now()->setDate(2024, 1, 10)->startOfDay();
        $startedAt1 = now()->setDate(2023, 12, 5)->startOfDay();

        // Fall 1: created_at ist später als started_at, keine boundary
        $user = User::factory()->create([
            'created_at' => $createdAt1,
        ]);
        $history = AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => 1,
            'started_at' => $startedAt1,
        ]);
        $user->setRelation('currentAssociationMembershipHistory', $history);
        $expected = $createdAt1->copy();
        $this->assertTrue($user->getEarliestAssociationChangeDate()->eq($expected));

        // Fall 2: started_at ist später als created_at, keine boundary
        $createdAt2 = now()->setDate(2023, 10, 10)->startOfDay();
        $startedAt2 = now()->setDate(2024, 2, 15)->startOfDay();
        $user = User::factory()->create([
            'created_at' => $createdAt2,
        ]);
        $history = AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => 1,
            'started_at' => $startedAt2,
        ]);
        $user->setRelation('currentAssociationMembershipHistory', $history);
        $expected = $startedAt2->copy();
        $this->assertTrue($user->getEarliestAssociationChangeDate()->eq($expected));

        // Fall 3: boundary ist das späteste Datum
        $createdAt3 = now()->setDate(2023, 5, 10)->startOfDay();
        $startedAt3 = now()->setDate(2023, 4, 5)->startOfDay();
        $boundary3 = now()->setDate(2024, 3, 1)->startOfDay();
        $user = User::factory()->create([
            'created_at' => $createdAt3,
        ]);
        $history = AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => 1,
            'started_at' => $startedAt3,
        ]);
        $user->setRelation('currentAssociationMembershipHistory', $history);
        $expected = $boundary3->copy();
        $this->assertTrue($user->getEarliestAssociationChangeDate($boundary3)->eq($expected));

        // Fall 4: started_at ist das späteste Datum (mit boundary)
        $createdAt4 = now()->setDate(2023, 5, 10)->startOfDay();
        $startedAt4 = now()->setDate(2024, 6, 15)->startOfDay();
        $boundary4 = now()->setDate(2024, 3, 1)->startOfDay();
        $user = User::factory()->create([
            'created_at' => $createdAt4,
        ]);
        $history = AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => 1,
            'started_at' => $startedAt4,
        ]);
        $user->setRelation('currentAssociationMembershipHistory', $history);
        $expected = $startedAt4->copy();
        $this->assertTrue($user->getEarliestAssociationChangeDate($boundary4)->eq($expected));

        // Fall 5: created_at ist das späteste Datum (mit boundary)
        $createdAt5 = now()->setDate(2024, 7, 10)->startOfDay();
        $startedAt5 = now()->setDate(2024, 6, 15)->startOfDay();
        $boundary5 = now()->setDate(2024, 3, 1)->startOfDay();
        $user = User::factory()->create([
            'created_at' => $createdAt5,
        ]);
        $history = AssociationMembershipHistory::create([
            'user_id' => $user->id,
            'association_id' => 1,
            'started_at' => $startedAt5,
        ]);
        $user->setRelation('currentAssociationMembershipHistory', $history);
        $expected = $createdAt5->copy();
        $this->assertTrue($user->getEarliestAssociationChangeDate($boundary5)->eq($expected));
    }
}
