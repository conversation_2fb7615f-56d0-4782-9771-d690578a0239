<?php

namespace Tests\Unit\Models;

use App\BillingAddress;
use App\Invoice;
use App\Pharmacy;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BillingAddressTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_determine_if_it_is_editable()
    {
        $user = User::factory()->create();
        $billingAddress = BillingAddress::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->assertTrue($billingAddress->isEditable());

        Invoice::factory()->create([
            'billing_address_id' => $billingAddress->id,
        ]);

        $this->assertFalse($billingAddress->isEditable());
    }

    /** @test */
    public function it_can_determine_if_it_is_deletable()
    {
        $user = User::factory()->create();
        $billingAddress = BillingAddress::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->assertTrue($billingAddress->isDeletable());

        Pharmacy::factory()->create([
            'billing_address_id' => $billingAddress->id,
        ]);

        $this->assertFalse($billingAddress->isDeletable());

        Invoice::factory()->create([
            'billing_address_id' => $billingAddress->id,
        ]);

        $this->assertFalse($billingAddress->isDeletable());
    }
}
