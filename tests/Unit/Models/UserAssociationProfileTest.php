<?php

namespace Tests\Unit\Models;

use App\Association;
use App\User;
use App\UserAssociationProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserAssociationProfileTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_belongs_to_an_user(): void
    {
        $user = User::factory()->create();

        $association = Association::factory()->create();
        $profile = UserAssociationProfile::create([
            'user_id' => $user->id,
            'association_id' => $association->id,
        ]);

        $this->assertTrue($profile->user->is($user));
        $this->assertTrue($user->associationProfile->is($profile->fresh()));
    }

    /** @test */
    public function it_belongs_to_an_association(): void
    {
        $user = User::factory()->create();

        $association = Association::factory()->create();
        $profile = UserAssociationProfile::create([
            'user_id' => $user->id,
            'association_id' => $association->id,
        ]);

        $this->assertNotNull($profile->association);
        $this->assertTrue($profile->association->is($association));
        $this->assertTrue($association->userAssociationProfiles->first()->is($profile->fresh()));
    }
}
