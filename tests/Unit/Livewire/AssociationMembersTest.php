<?php

namespace Tests\Unit\Livewire;

use App\Association;
use App\AssociationMembershipChange;
use App\Contracts\IOrderable;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Enums\AssociationRoleEnum;
use App\Enums\SalutationEnum;
use App\Livewire\Association\Components\AssociationMembers;
use App\Subscription;
use App\SubscriptionOrder;
use App\User;
use App\UserPharmacyProfile;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\FileUploadConfiguration;
use Livewire\Livewire;
use PhpOffice\PhpSpreadsheet\Calculation\Calculation;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

/**
 * @group AssociationMembers
 */
class AssociationMembersTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public $emailCounter = 13;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpFaker();
    }

    public function test_it_renders_the_association_members_component(): void
    {
        [$associationAdmin] = $this->createAssociationAdminOwnerAndPharmacy();

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->assertViewIs('livewire.association.components.members')
            ->assertViewHas('members');
    }

    public function test_it_renders_the_association_members_component_with_members(): void
    {
        $association = Association::factory()->create();

        UserPharmacyProfile::factory()
            ->count(3)
            ->for($association)
            ->has(User::factory())
            ->create();

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->assertViewIs('livewire.association.components.members')
            ->assertViewHas('members');
    }

    public function test_it_validates_file_upload(): void
    {
        $association = Association::factory()->create();

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', 'not-an-uploaded-file')
            ->call('addMembers')
            ->assertHasErrors(['file']);
    }

    public function test_it_does_not_close_modal_on_error(): void
    {
        $association = Association::factory()->create();

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', 'not-an-uploaded-file')
            ->call('addMembers')
            ->assertHasErrors(['file'])
            ->assertSet('modalOpen', true);
    }

    #[DataProvider('allowed_file_type_provider')]
    public function test_it_adds_members_from_accepted_file_types(string $extension): void
    {
        Storage::fake(FileUploadConfiguration::disk());
        Queue::fake();

        $association = Association::factory()->create();
        $fileName = 'association_members_test_excel'.$extension;
        $filePath = $this->getTestExcelPath($fileName);

        $this->assertFileExists($filePath);

        $file = UploadedFile::fake()->createWithContent($fileName, file_get_contents(base_path($filePath)));

        $this->assertFileEquals($filePath, $file);

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', $file)
            ->call('addMembers')
            ->assertHasNoErrors()
            ->assertSet('modalOpen', false)
            ->assertDispatched('close-modal');

        unlink($filePath);
    }

    public function test_it_adds_errors_not_accepted_file_types(): void
    {
        Storage::fake(FileUploadConfiguration::disk());

        $association = Association::factory()->create();
        $fileName = 'association_members_test_excel.csv';
        $filePath = $this->getTestExcelPath($fileName);
        $file = UploadedFile::fake()->createWithContent($fileName, file_get_contents(base_path($filePath)));

        Livewire::test(AssociationMembers::class, [
            'association' => $association,
        ])
            ->set('file', $file)
            ->call('addMembers')
            ->assertHasErrors()
            ->assertSet('modalOpen', true)
            ->assertNotDispatched('close-modal');

        unlink($filePath);
    }

    public static function allowed_file_type_provider(): array
    {
        return [
            ['.xls'],
            ['.xlsx'],
        ];
    }

    public function getTestExcelPath(string $fileName): string
    {
        $filePath = 'tests'.DIRECTORY_SEPARATOR.'storage';
        $file = $filePath.DIRECTORY_SEPARATOR.$fileName;
        $spreadsheet = new Spreadsheet;
        Calculation::getInstance($spreadsheet)->disableCalculationCache();

        $spreadsheet = $this->setExcelContent($spreadsheet);

        if (Str::contains($fileName, '.xlsx', true)) {
            $writer = new Xlsx($spreadsheet);
        } else {
            $writer = new Xls($spreadsheet);
        }
        $writer->save($file);

        return $file;
    }

    public function setExcelContent(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet = $this->setFirstHeader($spreadsheet);
        $spreadsheet = $this->setSecondHeader($spreadsheet);

        return $this->setSpreadsheet($spreadsheet);
    }

    public function setFirstHeader(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet->getActiveSheet()->setCellValue('H1', 'Adresse der Apotheke');

        return $spreadsheet;
    }

    public function setSecondHeader(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet->getActiveSheet()->setCellValue('A2', 'Anrede (Auswahlfeld)');
        $spreadsheet->getActiveSheet()->setCellValue('B2', 'Akadem. Grad');
        $spreadsheet->getActiveSheet()->setCellValue('C2', 'Vorname Inhaber');
        $spreadsheet->getActiveSheet()->setCellValue('D2', 'Nachname Inhaber');
        $spreadsheet->getActiveSheet()->setCellValue('E2', 'E-Mail-Adresse Inhaber');
        $spreadsheet->getActiveSheet()->setCellValue('F2', 'OHG Name');
        $spreadsheet->getActiveSheet()->setCellValue('G2', 'Name der Apotheke');
        $spreadsheet->getActiveSheet()->setCellValue('H2', 'Adresszusatz');
        $spreadsheet->getActiveSheet()->setCellValue('I2', 'Straße + Hausnr.');
        $spreadsheet->getActiveSheet()->setCellValue('J2', 'Postleitzahl');
        $spreadsheet->getActiveSheet()->setCellValue('K2', 'Ort');
        $spreadsheet->getActiveSheet()->setCellValue('L2', 'Datum (Format: TT.MM.YYYY)');

        return $spreadsheet;
    }

    public function setSpreadsheet(Spreadsheet $spreadsheet): Spreadsheet
    {
        $spreadsheet->getActiveSheet()->setCellValue('A3', $this->faker->randomElement(SalutationEnum::getLabels()));
        $spreadsheet->getActiveSheet()->setCellValue('B3', $this->faker->title);
        $spreadsheet->getActiveSheet()->setCellValue('C3', $this->faker->firstName);
        $spreadsheet->getActiveSheet()->setCellValue('D3', $this->faker->lastName);
        $spreadsheet->getActiveSheet()->setCellValue('E3', 'qm+'.$this->emailCounter.'@gedisa.de');
        $spreadsheet->getActiveSheet()->setCellValue('F3', $this->faker->pharmacyName);
        $spreadsheet->getActiveSheet()->setCellValue('G3', $this->faker->pharmacyName);
        $spreadsheet->getActiveSheet()->setCellValue('H3', $this->faker->streetSuffix());
        $spreadsheet->getActiveSheet()->setCellValue(
            'I3',
            $this->faker->streetName.(random_int(1, 9999).' '.((bool) random_int(0, 5) ? Str::random(1) : ''))
        );
        $spreadsheet->getActiveSheet()->setCellValue('J3', random_int(10000, 99999));
        $spreadsheet->getActiveSheet()->setCellValue('K3', $this->faker->city);
        $spreadsheet->getActiveSheet()->setCellValue('L3', date('d.m.Y', strtotime(now())));

        $this->emailCounter++;

        return $spreadsheet;
    }

    public function test_get_remove_member_date_options_without_invoices_in_livewire_context_as_admin(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->call('getRemoveMemberDateOptions', $owner->id)
            ->assertHasNoErrors();
    }

    public function test_get_remove_member_date_options_with_invoices_in_livewire_context_as_admin(): void
    {
        [$associationAdmin, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();

        $travelMonth = 6;
        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);
        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->call('getRemoveMemberDateOptions', $owner->id)
            ->assertHasNoErrors();
    }

    public function test_return_of_get_remove_member_date_options_without_invoices_as_admin(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        $diffFuture = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            Carbon::now()->copy()->firstOfMonth()->addYearNoOverflow()->floor()
        ));
        $diffPast = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            $owner->currentAssociationMembershipHistory->started_at->copy()->firstOfMonth()->floor()
        ));
        $dateRange = $diffFuture + $diffPast;

        $compareDates = collect();
        $livewire = new AssociationMembers;
        $dateOptions = $livewire->getRemoveMemberDateOptions($owner);

        $this->assertSame($dateOptions->count(), (int) $dateRange + 1);

        for ($i = 0; $i <= $dateRange; $i++) {
            $newDate = $owner->created_at->addMonths($i);
            $compareDates->put(
                $newDate->format('Y-m'),
                __('months.'.$newDate->format('n')).' '.$newDate->format('Y')
            );
            $this->assertArrayHasKey($newDate->format('Y-m'), $dateOptions);
            $this->assertSame(
                $compareDates[$newDate->format('Y-m')],
                $dateOptions[$newDate->format('Y-m')]
            );
        }

        $this->assertSame(0, $compareDates->diff($dateOptions)->count());
    }

    public function test_return_of_get_remove_member_date_options_with_invoices_as_admin(): void
    {
        $this->markTestSkipped('AP-2244 should fix this.');
        [$associationAdmin, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();

        $travelMonth = 6;
        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);
        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationAdmin);

        $diffFuture = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            Carbon::now()->copy()->firstOfMonth()->addYearNoOverflow()->floor()
        ));
        $diffPast = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            $owner->getLastPossibleDateChangingAssociationRetroactively()->copy()->firstOfMonth()->floor()
        ));
        $dateRange = $diffFuture + $diffPast;

        $compareDates = collect();
        $livewire = new AssociationMembers;
        $dateOptions = $livewire->getRemoveMemberDateOptions($owner);

        $this->assertSame($dateOptions->count(), $dateRange + 1);

        for ($i = 0; $i <= $dateRange; $i++) {
            $newDate = $owner->getLastPossibleDateChangingAssociationRetroactively()->addMonthsNoOverflow($i);
            $compareDates->put(
                $newDate->format('Y-m'),
                __('months.'.$newDate->format('n')).' '.$newDate->format('Y')
            );
            $this->assertArrayHasKey($newDate->format('Y-m'), $dateOptions);
            $this->assertSame(
                $compareDates[$newDate->format('Y-m')],
                $dateOptions[$newDate->format('Y-m')]
            );
        }

        $this->assertSame(0, $compareDates->diff($dateOptions)->count());
    }

    public function test_get_remove_member_date_options_without_invoices_in_livewire_context_as_association_member(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change test_user_can_be_edited_because_no_association_member_ship_changeassociation membership
         */
        $this->markTestSkipped();

        [$associationEmployee, $owner] = $this->createAssociationAdminOwnerAndPharmacy(
            AssociationRoleEnum::EMPLOYEE
        );

        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->call('getRemoveMemberDateOptions', $owner->id)
            ->assertHasNoErrors();
    }

    public function test_get_remove_member_date_options_with_invoices_in_livewire_context_as_association_member(): void
    {
        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy(
            AssociationRoleEnum::EMPLOYEE
        );

        $travelMonth = 6;

        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);

        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->call('getRemoveMemberDateOptions', $owner->id)
            ->assertHasNoErrors();
    }

    public function test_return_of_get_remove_member_date_options_without_invoices_as_association_member(): void
    {
        [$associationEmployee, $owner] = $this->createAssociationAdminOwnerAndPharmacy(
            AssociationRoleEnum::EMPLOYEE
        );

        $this->actingAs($associationEmployee);

        $diffFuture = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            Carbon::now()->copy()->firstOfMonth()->addYearNoOverflow()->floor()
        ));
        $diffPast = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            $owner->currentAssociationMembershipHistory->started_at->copy()->firstOfMonth()->floor()
        ));
        $dateRange = $diffFuture + $diffPast;

        $compareDates = collect();
        $livewire = new AssociationMembers;
        $dateOptions = $livewire->getRemoveMemberDateOptions($owner);

        $this->assertSame($dateOptions->count(), (int) $dateRange + 1);

        for ($i = 0; $i <= 12; $i++) {
            $newDate = $owner->created_at->addMonths($i);
            $compareDates->put(
                $newDate->format('Y-m'),
                __('months.'.$newDate->format('n')).' '.$newDate->format('Y')
            );
            $this->assertArrayHasKey($newDate->format('Y-m'), $dateOptions);
            $this->assertSame(
                $compareDates[$newDate->format('Y-m')],
                $dateOptions[$newDate->format('Y-m')]
            );
        }

        $this->assertSame(0, $compareDates->diff($dateOptions)->count());
    }

    public function test_return_of_get_remove_member_date_options_with_invoices_as_association_member(): void
    {
        $this->markTestSkipped('AP-2244 should fix this.');
        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy(
            AssociationRoleEnum::EMPLOYEE
        );

        $travelMonth = 6;
        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);

        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationEmployee);

        $diffFuture = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            Carbon::now()->copy()->firstOfMonth()->addYearNoOverflow()->floor()
        ));
        $diffPast = (int) abs(now()->copy()->firstOfMonth()->floor()->diffInMonths(
            $owner->getLastPossibleDateChangingAssociationRetroactively()->copy()->firstOfMonth()->floor()
        ));
        $dateRange = $diffFuture + $diffPast;

        $compareDates = collect();
        $livewire = new AssociationMembers;
        $dateOptions = $livewire->getRemoveMemberDateOptions($owner);

        $this->assertSame($dateOptions->count(), $dateRange + 1);

        for ($i = 0; $i <= 12; $i++) {
            $newDate = $owner->getLastPossibleDateChangingAssociationRetroactively()->addMonths($i);
            $compareDates->put(
                $newDate->format('Y-m'),
                __('months.'.$newDate->format('n')).' '.$newDate->format('Y')
            );
            $this->assertArrayHasKey($newDate->format('Y-m'), $dateOptions);
            $this->assertSame(
                $compareDates[$newDate->format('Y-m')],
                $dateOptions[$newDate->format('Y-m')]
            );
        }

        $this->assertSame(0, $compareDates->diff($dateOptions)->count());
    }

    public function test_dont_remove_member_when_no_user_to_remove_is_set(): void
    {
        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();

        $oldAssociationId = $owner->pharmacyProfile->association_id;

        $travelMonth = 6;
        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);
        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->set('removeMemberDate', now()->addMonthsNoOverflow())
            ->call('removeAssociationMember', $owner->id)
            ->assertHasNoErrors()
            ->assertDispatched('change-modal-state');

        $owner->refresh();

        $this->assertSame($oldAssociationId, $owner->pharmacyProfile->association_id);

        $this->assertNull($owner->currentAssociationMembershipChange);
    }

    public function test_dont_remove_member_when_no_remove_date_is_set(): void
    {
        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();

        $oldAssociationId = $owner->pharmacyProfile->association_id;

        $travelMonth = 6;
        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);
        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->set('userToRemove', $owner)
            ->call('removeAssociationMember', $owner->id)
            ->assertHasErrors()
            ->assertNotDispatched('change-modal-state');

        $owner->refresh();

        $this->assertSame($oldAssociationId, $owner->pharmacyProfile->association_id);

        $this->assertNull($owner->currentAssociationMembershipChange);
    }

    public function test_remove_member_with_invoices_in_livewire_context_as_association_admin(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();

        $travelMonth = 6;
        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);
        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->set('userToRemove', $owner)
            ->set('removeMemberDate', now()->addMonthsNoOverflow())
            ->call('removeAssociationMember', $owner->id)
            ->assertHasNoErrors()
            ->assertDispatched('change-modal-state');

        $owner->refresh();

        $this->assertNotNull($owner->currentAssociationMembershipChange);
        $this->assertTrue($owner->currentAssociationMembershipChange->exists);
    }

    public function test_remove_member_with_invoices_in_livewire_context_as_association_member(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        [$associationEmployee, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy(
            AssociationRoleEnum::EMPLOYEE
        );

        $travelMonth = 6;
        $this->travelTo(now()->addMonthsNoOverflow($travelMonth));

        for ($i = 0; $i <= $travelMonth; $i++) {
            $this->prolongSubscription();
        }

        $this->assertDatabaseCount(SubscriptionOrder::class, $travelMonth);

        $invoices = $this->createInvoices($travelMonth / 2, $pharmacy->billingAddress);
        $subscriptionOrders = SubscriptionOrder::where(
            'started_at', '<=', now()->subMonthsNoOverflow($travelMonth / 2)
        )->get();

        for ($i = 1; $i <= $invoices->count(); $i++) {
            $invoice = $invoices->find($i);
            $subscriptionOrder = $subscriptionOrders->find($i);

            $subscriptionOrder->invoice_id = $invoice->id;
            $subscriptionOrder->save();
            $subscriptionOrder->refresh();
        }
        $this->actingAs($associationEmployee);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationEmployee->association(),
        ])
            ->set('userToRemove', $owner)
            ->set('removeMemberDate', now()->addMonthsNoOverflow())
            ->call('removeAssociationMember', $owner->id)
            ->assertHasNoErrors()
            ->assertDispatched(
                'change-modal-state',
            );

        $owner->refresh();

        $this->assertNotNull($owner->currentAssociationMembershipChange);
        $this->assertTrue($owner->currentAssociationMembershipChange->exists);
    }

    public function test_user_can_be_edited_because_no_association_member_ship_change(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        $livewire = new AssociationMembers;

        $can = $livewire->userCanBeEdited($owner);

        $this->assertTrue($can);
    }

    public function test_user_can_be_edited_because_association_member_change_is_canceled(): void
    {
        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
         */
        $this->markTestSkipped();

        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => $owner->pharmacyProfile->association_id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now()->copy()->endOfMonth(),
            'canceled_at' => now(),
        ]);

        $owner->refresh();

        $this->actingAs($associationAdmin);

        $livewire = new AssociationMembers;

        $can = $livewire->userCanBeEdited($owner);

        $this->assertTrue($can);
    }

    public function test_user_cannot_be_edited_because_has_association_member_change(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => $owner->pharmacyProfile->association_id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now()->endOfMonth(),
        ]);

        $owner->refresh();

        $this->actingAs($associationAdmin);

        $livewire = new AssociationMembers;

        $can = $livewire->userCanBeEdited($owner);

        $this->assertFalse($can);
    }

    /**
     * @group failing
     */
    public function test_it_cannot_see_delete_association_membership_modal(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->assertSet('userToRemove', null)
            ->assertDontSee('Apotheker '.trim($owner->title.' '.$owner->last_name).', '.$owner->first_name)
            ->assertNotDispatched('change-modal-state');
    }

    public function test_it_cannot_see_delete_association_membership_modal_because_no_member_id(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->call('openRemoveAssociationMemberModal', '')
            ->assertSet('userToRemove', null)
            ->assertDontSee('Apotheker '.trim($owner->title.' '.$owner->last_name).', '.$owner->first_name)
            ->assertNotDispatched('change-modal-state');

        $livewire = new AssociationMembers;
        $opened = $livewire->openRemoveAssociationMemberModal('');

        $this->assertFalse($opened);
    }

    public function test_it_cannot_see_delete_association_membership_modal_because_no_user_to_remove_found(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();
        $usersCount = User::count();

        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->call('openRemoveAssociationMemberModal', $usersCount + 1)
            ->assertSet('userToRemove', null)
            ->assertDontSee('Apotheker '.trim($owner->title.' '.$owner->last_name).', '.$owner->first_name)
            ->assertNotDispatched('change-modal-state');

        $livewire = new AssociationMembers;
        $opened = $livewire->openRemoveAssociationMemberModal($usersCount + 1);

        $this->assertFalse($opened);
    }

    public function test_it_can_see_delete_association_membership_modal(): void
    {
        [$associationAdmin, $owner] = $this->createAssociationAdminOwnerAndPharmacy();

        $this->actingAs($associationAdmin);

        Livewire::test(AssociationMembers::class, [
            'association' => $associationAdmin->association(),
        ])
            ->call('openRemoveAssociationMemberModal', $owner->id)
            ->assertSee(trim($owner->title.' '.$owner->last_name).', '.$owner->first_name)
            ->assertDispatched(
                'change-modal-state',
            );

        $livewire = new AssociationMembers;
        $opened = $livewire->openRemoveAssociationMemberModal($owner->id);

        $this->assertTrue($opened);
    }

    private function createAssociationAdminOwnerAndPharmacy($associationRole = AssociationRoleEnum::ADMIN): array
    {
        [$associationUser, $association] = $this->createAssociationUser(
            Association::factory()->create(),
            $associationRole
        );

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = $association->id;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = $association->id;
        $owner->brochureCode->save();

        $pharmacy->subscribeToPlan('extended');
        $pharmacy->association_id = $association->id;
        $pharmacy->save();

        $owner->refresh();
        $pharmacy->refresh();

        $this->assertTrue($pharmacy->isSubscribedTo('extended'));
        $this->assertSame(1, $pharmacy->acceptedTermsOfUse()->count());
        $this->assertSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertSame($association->id, $owner->brochureCode->association_id);
        $this->assertSame($association->id, $pharmacy->association_id);

        return [$associationUser, $owner, $pharmacy];
    }

    public function test_membership_started_at_is_the_earliest_date_to_select(): void
    {
        [$associationAdmin, $owner, $pharmacy] = $this->createAssociationAdminOwnerAndPharmacy();
        $invoice = $this->createInvoices(1, $pharmacy->billingAddress)->first();
        $pharmacy->subscriptionOrders()->each(fn ($order) => $order->update(['invoice_id' => $invoice->id, 'ended_at' => $owner->currentAssociationMembershipHistory->started_at->subMonthNoOverflow()]));

        $this->actingAs($associationAdmin);

        $livewire = new AssociationMembers;

        $dateOptions = $livewire->getRemoveMemberDateOptions($owner);

        $this->assertSame(
            $owner->currentAssociationMembershipHistory->started_at->format('Y-m'),
            $dateOptions->keys()->first()
        );
    }

    private function prolongSubscription()
    {
        Subscription::query()
            ->whereDate('cycle_ends_at', '<', now())
            ->where(function (Builder $query) {
                $query->whereNull('ends_at')
                    ->orWhereColumn('ends_at', '>', 'cycle_ends_at');
            })
            ->whereDoesntHave('subscribable.oldSubscriptions', function (Builder $query) {
                return $query->whereDate('cycle_started_at', '>=', now());
            })
            ->eachById(function (Subscription $subscription) {
                $orderable = $subscription->subscribable;

                if (! $orderable instanceof IOrderable) {
                    return;
                }

                try {
                    DB::transaction(function () use ($orderable, $subscription) {
                        $newSubscription = $subscription->addACycle();

                        $newSubscription->save();

                        $order = $newSubscription->makeOrderForCurrentCycle($orderable);
                        $order->forceFill([
                            'subscription_id' => $newSubscription->id,
                            'total_price' => $order->calcPrice(),
                        ])->save();
                    });
                } catch (Exception $e) {
                    report($e);
                }
            }, 100);
    }
}
