<?php

namespace Tests\Unit\Livewire\User;

use App\Enums\PharmacyRoleEnum;
use App\Enums\SalutationEnum;
use App\Livewire\User\EditUser;
use App\Mail\UserChangeEmailMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use Livewire\Livewire;
use Tests\TestCase;

class EditUserTest extends TestCase
{
    use RefreshDatabase;

    public function test_a_user_can_update_his_account_but_email_needs_verification(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->actingAs($user);

        $updates = [
            'salutation' => SalutationEnum::MR,
            'title' => 'Dr.',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone' => '**********',
            'email' => '<EMAIL>',
            'email_confirmation' => '<EMAIL>',
        ];

        Livewire::actingAs($user)
            ->test(EditUser::class, ['user' => $user])
            ->set('form.salutation', $updates['salutation'])
            ->set('form.title', $updates['title'])
            ->set('form.first_name', $updates['first_name'])
            ->set('form.last_name', $updates['last_name'])
            ->set('form.phone', $updates['phone'])
            ->set('form.email', $updates['email'])
            ->set('form.email_confirmation', $updates['email_confirmation'])
            ->call('save')
            ->assertOk()
            ->assertHasNoErrors();

        $this->assertDatabaseHas('users', Arr::except($updates, ['email', 'email_confirmation']));
        $this->assertNotSame($user->fresh()->email, $updates['email']);
    }

    public function test_a_company_user_can_update_his_account_but_email_needs_verification(): void
    {
        $user = $this->createCompanyUser();
        $pharmacy = $this->createPharmacy();
        $pharmacy->assignUser($user, PharmacyRoleEnum::OWNER);

        $this->actingAs($user);

        $updates = [
            'name' => 'Changed Name OHG',
            'salutation' => SalutationEnum::MS,
            'title' => 'Prof.',
            'first_name' => 'Joan',
            'last_name' => 'Doe',
            'phone' => '**********',
            'email' => '<EMAIL>',
            'email_confirmation' => '<EMAIL>',
        ];

        Livewire::actingAs($user)
            ->test(EditUser::class, ['user' => $user])
            ->set('form.name', null)
            ->set('form.salutation', $updates['salutation'])
            ->set('form.title', $updates['title'])
            ->set('form.first_name', $updates['first_name'])
            ->set('form.last_name', $updates['last_name'])
            ->set('form.phone', $updates['phone'])
            ->set('form.email', $updates['email'])
            ->set('form.email_confirmation', $updates['email_confirmation'])
            ->call('save')
            ->assertHasErrors('form.name')
            ->set('form.name', $updates['name'])
            ->call('save')
            ->assertOk()
            ->assertHasNoErrors();

        $this->assertDatabaseHas('users', Arr::except($updates, ['email', 'email_confirmation', 'name']));
        $this->assertNotSame($user->fresh()->email, $updates['email']);
        $this->assertDatabaseHas('company_users', ['name' => $updates['name']]);
    }

    public function test_a_user_can_request_to_change_his_email_address(): void
    {
        Mail::fake();

        [$user] = $this->createPharmacyUserWithPharmacy();

        $data = $user->toArray();

        $data['email'] = '<EMAIL>';
        $data['email_confirmation'] = '<EMAIL>';

        Livewire::actingAs($user)
            ->test(EditUser::class, ['user' => $user])
            ->assertDontSee('E-Mail erneut senden')
            ->set('form.email', $data['email'])
            ->set('form.email_confirmation', $data['email_confirmation'])
            ->call('save')
            ->assertHasNoErrors();

        $this->assertNotSame('<EMAIL>', $user->fresh()->email);

        $actionUrl = '';
        Mail::assertSent(UserChangeEmailMail::class, function ($mail) use (&$actionUrl) {
            $actionUrl = $mail->actionUrl;

            return $mail->hasTo('<EMAIL>');
        });

        Livewire::actingAs($user)
            ->test(EditUser::class, ['user' => $user])
            ->assertSee('E-Mail erneut senden');

        $this->secondUser();

        $this->actingAs($user)->get($actionUrl)->assertOk();

        $this->assertSame('<EMAIL>', $user->fresh()->email);
    }

    protected function secondUser(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();
        [$defaultUser] = $this->createPharmacyUserWithPharmacy();

        $request = $user->toArray();

        // E-Mail is as change requested from another user
        $request['email'] = '<EMAIL>';
        $request['email_confirmation'] = '<EMAIL>';

        Livewire::actingAs($user)
            ->test(EditUser::class, ['user' => $user])
            ->set('form.email', $request['email'])
            ->set('form.email_confirmation', $request['email_confirmation'])
            ->call('save')
            ->assertHasErrors('email');

        // email is used by another user
        $request['email'] = $defaultUser->email;
        $request['email_confirmation'] = $defaultUser->email;

        Livewire::actingAs($user)
            ->test(EditUser::class, ['user' => $user])
            ->set('form.email', $request['email'])
            ->set('form.email_confirmation', $request['email_confirmation'])
            ->call('save')
            ->assertHasNoErrors();
    }
}
