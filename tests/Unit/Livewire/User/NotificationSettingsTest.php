<?php

namespace Tests\Unit\Livewire\User;

use App\Enums\Newsletter\MailcoachList;
use App\Http\Integrations\Mailcoach\Requests\SubscribeToListRequest;
use App\Integrations\MailcoachIntegration;
use App\Livewire\User\NotificationSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Livewire\Livewire;
use Saloon\Http\Faking\MockResponse;
use Saloon\Laravel\Facades\Saloon;
use Tests\TestCase;

class NotificationSettingsTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_indicates_unsubscribed_if_not_subscribed(): void
    {
        $user = $this->createPharmacyUser();

        $subscribed = Livewire::actingAs($user)
            ->test(NotificationSettings::class, ['user' => $user])
            ->get('subscribed');

        $this->assertFalse($subscribed);
    }

    public function test_it_indicates_subscribed_if_advertising_list_id_is_not_null(): void
    {
        $user = $this->createPharmacyUser();

        $user->setIntegration(new MailcoachIntegration(advertisingListId: 'test'));

        $subscribed = Livewire::actingAs($user)
            ->test(NotificationSettings::class, ['user' => $user])
            ->get('subscribed');

        $this->assertTrue($subscribed);
    }

    public function test_it_shows_tooltip_if_foreign_subscribed(): void
    {
        $this->createPharmacyUser([
            'email' => '<EMAIL>',
        ])->setIntegration(new MailcoachIntegration(advertisingListId: 'test'));

        $user = $this->createPharmacyUser([
            'email' => '<EMAIL>',
        ]);

        Livewire::actingAs($user)
            ->test(NotificationSettings::class, ['user' => $user])
            ->assertSee('Ein anderer Nutzer deiner Apotheke hat den Newsletter abonniert.');
    }

    public function test_subscriber_id_is_set_as_advertising_list_id_after_subscribing_to_newsletter(): void
    {
        Saloon::fake([
            SubscribeToListRequest::class => MockResponse::make([
                'data' => [
                    'uuid' => Str::uuid()->toString(),
                    'emailListUuid' => Str::uuid()->toString(),
                    'email' => '<EMAIL>',
                    'firstName' => 'Max',
                    'lastName' => 'Mustermann',
                    'extraAttributes' => [],
                    'tags' => [],
                    'subscribedAt' => now(),
                    'unsubscribedAt' => null,
                    'createdAt' => now(),
                    'updatedAt' => now(),
                ],
            ]),
        ]);

        $user = $this->createPharmacyUser();

        Livewire::actingAs($user)
            ->test(NotificationSettings::class, ['user' => $user])
            ->call('toggleNewsletter');

        $this->assertNotNull($user->refresh()->hasMailcoachSubscriptions(MailcoachList::Advertising));
    }
}
