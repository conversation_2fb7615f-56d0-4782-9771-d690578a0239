<?php

namespace Tests\Unit\Livewire\Ngda\WizardSteps;

use App\Data\NgdaPayload;
use App\Livewire\Ngda\WizardSteps\ConnectWithPharmacyStep;
use Livewire\Livewire;
use Tests\TestCase;

class ConnectWithPharmacyStepTest extends TestCase
{
    public function test_shows_error_when_no_pharmacy_is_selected(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        session()->put('ngda-payload', NgdaPayload::from([
            'id' => 'APO123',
            'name' => 'Kronenapotheke',
        ]));

        Livewire::actingAs($owner)->test(ConnectWithPharmacyStep::class)
            ->call('submit')
            ->assertHasErrors('selectedPharmacy.id');
    }

    public function test_shows_error_when_user_is_not_sure(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        session()->put('ngda-payload', NgdaPayload::from([
            'id' => 'APO123',
            'name' => 'Kronenapotheke',
        ]));

        Livewire::actingAs($owner)->test(ConnectWithPharmacyStep::class)
            ->call('submit')
            ->assertHasErrors('sureAboutPharmacySelection');
    }
}
