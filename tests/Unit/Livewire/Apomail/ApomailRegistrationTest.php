<?php

namespace Tests\Unit\Livewire\Apomail;

use App\Livewire\Apomail\ApomailRegistration;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class ApomailRegistrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_mounts_correct_apomail()
    {
        $pharmacy = $this->createPharmacyUserWithPharmacy()[1];
        [$apomail, $employee] = $this->createEmployeeWithApomailUser($pharmacy);

        $this->actingAs($employee);

        Livewire::test(ApomailRegistration::class)
            ->assertSet('apomail.id', $apomail->id);
    }
}
