<?php

namespace Tests\Unit\Livewire;

use App\Domains\Subscription\Application\StripeProducts\AddOns\TIMStripeProduct;
use App\Enums\PharmacyRoleEnum;
use App\Livewire\ChatEnd2EndAndApoPortalRelease;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Livewire;
use Tests\TestCase;

class ChatEnd2EndAndApoPortalReleaseTest extends TestCase
{
    use RefreshDatabase;

    public function test_checkbox_group_not_shown_no_pharmacies(): void
    {
        $user = $this->createPharmacyUser();
        Livewire::actingAs($user)
            ->test(ChatEnd2EndAndApoPortalRelease::class)
            ->assertDontSee('checkbox-group');
    }

    public function test_owned_pharmacies_that_can_activate_chat_are_listed(): void
    {
        Http::fake();

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->update(['name' => 'Test Pharmacy']);
        $this->createLocalStripeSubscription($pharmacy, TIMStripeProduct::make());

        $pharmacySubscribedButNotAcceptedTerms = $this->createPharmacyForUser($owner);
        $this->declineTermsForPharmacy($pharmacySubscribedButNotAcceptedTerms);
        $pharmacySubscribedButNotAcceptedTerms->update(['name' => 'Test Pharmacy 2']);
        $this->createLocalStripeSubscription($pharmacySubscribedButNotAcceptedTerms, TIMStripeProduct::make());

        $pharmacyAcceptedTermsButNotSubscribed = $this->createPharmacyForUser($owner);
        $pharmacyAcceptedTermsButNotSubscribed->update(['name' => 'Test Pharmacy 3']);

        $pharmacyActivatedChat = $this->createPharmacyForUser($owner);
        $pharmacyActivatedChat->update(['name' => 'Test Pharmacy 4']);
        $this->createLocalStripeSubscription($pharmacyActivatedChat, TIMStripeProduct::make());
        $pharmacyActivatedChat->update(['uses_chat' => true]);

        $pharmacyActivatedChatAndPatientChat = $this->createPharmacyForUser($owner);
        $pharmacyActivatedChatAndPatientChat->update(['name' => 'Test Pharmacy 5']);
        $this->createLocalStripeSubscription($pharmacyActivatedChatAndPatientChat, TIMStripeProduct::make());
        $pharmacyActivatedChatAndPatientChat->update(['uses_chat' => true, 'uses_patient_chat' => true]);

        $pharmacyNotOwned = $this->createPharmacy();
        Livewire::actingAs($owner)
            ->test(ChatEnd2EndAndApoPortalRelease::class)
            ->assertSee('checkbox-group')
            ->assertSee($pharmacy->name)
            ->assertSee($pharmacyActivatedChat->name)
            ->assertDontSee($pharmacySubscribedButNotAcceptedTerms->name)
            ->assertDontSee($pharmacyAcceptedTermsButNotSubscribed->name)
            ->assertDontSee($pharmacyNotOwned->name)
            ->assertDontSee($pharmacyActivatedChatAndPatientChat->name);
    }

    public function test_employee_cannot_activate_chat(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, TIMStripeProduct::make());

        Livewire::actingAs($owner)
            ->test(ChatEnd2EndAndApoPortalRelease::class)
            ->assertSee('checkbox-group')
            ->assertSee($pharmacy->name);

        $branchManager = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);

        Livewire::actingAs($branchManager)
            ->test(ChatEnd2EndAndApoPortalRelease::class)
            ->assertDontSee('checkbox-group')
            ->assertDontSee($pharmacy->name);
    }

    public function test_sub_owner_can_activate_chat(): void
    {
        [$subOwner, $pharmacy] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, TIMStripeProduct::make());

        Livewire::actingAs($subOwner)
            ->test(ChatEnd2EndAndApoPortalRelease::class)
            ->assertSee('checkbox-group')
            ->assertSee($pharmacy->name);
    }

    public function test_save_cannot_deactivate(): void
    {
        Http::fake();
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, TIMStripeProduct::make());
        $pharmacy->update(['uses_chat' => false, 'uses_patient_chat' => false]);

        $pharmacy2 = $this->createPharmacyForUser($owner);
        $this->acceptTermsForPharmacy($pharmacy2);
        $this->createLocalStripeSubscription($pharmacy2, TIMStripeProduct::make());
        $pharmacy2->update(['uses_chat' => true, 'uses_patient_chat' => false]);

        $pharmacy3 = $this->createPharmacyForUser($owner);
        $this->acceptTermsForPharmacy($pharmacy3);
        $this->createLocalStripeSubscription($pharmacy3, TIMStripeProduct::make());
        $pharmacy3->update(['uses_chat' => true, 'uses_patient_chat' => false]);

        $pharmacy4 = $this->createPharmacyForUser($owner);
        $this->acceptTermsForPharmacy($pharmacy4);
        $this->createLocalStripeSubscription($pharmacy4, TIMStripeProduct::make());
        $pharmacy4->update(['uses_chat' => false, 'uses_patient_chat' => false]);

        $pharmacy5 = $this->createPharmacyForUser($owner);
        $this->acceptTermsForPharmacy($pharmacy5);
        $this->createLocalStripeSubscription($pharmacy5, TIMStripeProduct::make());
        $pharmacy5->update(['uses_chat' => true, 'uses_patient_chat' => true]);

        Livewire::actingAs($owner)
            ->test(ChatEnd2EndAndApoPortalRelease::class)
            ->set('selectedPharmacies', [$pharmacy->id, $pharmacy2->id])
            ->call('beforeSave');

        $pharmacy->refresh();
        $pharmacy2->refresh();
        $pharmacy3->refresh();
        $pharmacy4->refresh();
        $pharmacy5->refresh();
        $this->assertTrue($pharmacy->uses_chat && $pharmacy->uses_patient_chat);
        $this->assertTrue($pharmacy2->uses_chat && $pharmacy2->uses_patient_chat);
        $this->assertTrue($pharmacy3->uses_chat);
        $this->assertFalse((bool) $pharmacy3->uses_patient_chat);
        $this->assertFalse($pharmacy4->uses_chat && $pharmacy4->uses_patient_chat);
        $this->assertTrue($pharmacy5->uses_chat && $pharmacy5->uses_patient_chat);
    }
}
