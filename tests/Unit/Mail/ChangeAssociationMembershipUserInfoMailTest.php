<?php

namespace Tests\Unit\Mail;

use App\Enums\AssociationEnum;
use App\Mail\ChangeAssociationMembershipUserInfoMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Tests\TestCase;

class ChangeAssociationMembershipUserInfoMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_mailable_content(): void
    {
        $user = $this->createPharmacyUser();

        $associationId = Arr::random(array_keys(AssociationEnum::getLabels()));

        $changeDate = now()->endOfMonth();

        $mailable = new ChangeAssociationMembershipUserInfoMail($user, $associationId, $changeDate);

        $mailable->assertSeeInText($changeDate->copy()->addDay()->format('d.m.Y'));

        $mailable->assertSeeInText(AssociationEnum::getLabels()[$associationId]);

        $mailable->assertSeeInText($user->greeting);
    }

    public function test_get_deadline_date_when_termination_in_future(): void
    {
        $user = $this->createPharmacyUser();

        $associationId = Arr::random(array_keys(AssociationEnum::getLabels()));

        $changeDate = now()->endOfMonth();

        $mailable = new ChangeAssociationMembershipUserInfoMail($user, $associationId, $changeDate);

        $deadlineDate = $mailable->getDeadlineForTosAndSubscription(Carbon::make($changeDate));

        $mailable->assertSeeInText($deadlineDate->format('d.m.Y'));

        $mailable->assertSeeInText($changeDate->copy()->addDay()->format('d.m.Y'));

        $this->assertNotSame($changeDate->format('Y-m-d'), $deadlineDate->format('Y-m-d'));

        $this->assertSame(now()->endOfMonth()->format('Y-m-d'), $changeDate->format('Y-m-d'));

        $this->assertSame(
            now()->endOfMonth()->addMonthsNoOverflow()->addDay()->format('Y-m-d'),
            $deadlineDate->format('Y-m-d')
        );
    }

    public function test_get_deadline_date_when_termination_in_past(): void
    {
        $user = $this->createPharmacyUser();

        $associationId = Arr::random(array_keys(AssociationEnum::getLabels()));

        $changeDate = now()->subMonthsNoOverflow(2)->endOfMonth();

        $mailable = new ChangeAssociationMembershipUserInfoMail($user, $associationId, $changeDate);

        $deadlineDate = $mailable->getDeadlineForTosAndSubscription(Carbon::make($changeDate));

        $mailable->assertSeeInText($deadlineDate->format('d.m.Y'));

        $mailable->assertSeeInText($changeDate->copy()->addDay()->format('d.m.Y'));

        $this->assertNotSame($changeDate->format('Y-m-d'), $deadlineDate->format('Y-m-d'));

        $this->assertSame(
            now()->subMonthsNoOverflow(2)->endOfMonth()->format('Y-m-d'),
            $changeDate->format('Y-m-d')
        );

        $this->assertSame(
            now()->addMonthsNoOverflow()->addDay()->format('Y-m-d'),
            $deadlineDate->format('Y-m-d')
        );
    }
}
