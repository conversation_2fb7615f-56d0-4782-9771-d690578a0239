<?php

namespace Tests\Unit\Mail;

use App\Enums\AssociationEnum;
use App\Mail\CancelAssociationMembershipUserInfoMail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Tests\TestCase;

class CancelAssociationMembershipUserInfoMailTest extends TestCase
{
    use RefreshDatabase;

    public function test_mailable_content(): void
    {
        $user = $this->createPharmacyUser();

        $associationId = Arr::random(array_keys(AssociationEnum::getLabels()));

        $terminationDate = now()->endOfMonth();

        $mailable = new CancelAssociationMembershipUserInfoMail($user, $associationId, $terminationDate);

        $mailable->assertSeeInText($terminationDate->format('d.m.Y'));

        $mailable->assertSeeInText(AssociationEnum::getLabels()[$associationId]);

        $mailable->assertSeeInText($user->greeting);
    }

    public function test_get_deadline_date_when_termination_in_future(): void
    {
        $user = $this->createPharmacyUser();

        $associationId = Arr::random(array_keys(AssociationEnum::getLabels()));

        $terminationDate = now()->endOfMonth();

        $mailable = new CancelAssociationMembershipUserInfoMail($user, $associationId, $terminationDate);

        $deadlineDate = $mailable->getDeadlineForTosAndSubscription(Carbon::make($terminationDate));

        $mailable->assertSeeInText($deadlineDate->format('d.m.Y'));

        $mailable->assertSeeInText($terminationDate->format('d.m.Y'));

        $this->assertNotSame($terminationDate->format('Y-m-d'), $deadlineDate->format('Y-m-d'));

        $this->assertSame(now()->endOfMonth()->format('Y-m-d'), $terminationDate->format('Y-m-d'));

        $this->assertSame(
            now()->endOfMonth()->addMonthsNoOverflow()->addDay()->format('Y-m-d'),
            $deadlineDate->format('Y-m-d')
        );
    }

    public function test_get_deadline_date_when_termination_in_past(): void
    {
        $user = $this->createPharmacyUser();

        $associationId = Arr::random(array_keys(AssociationEnum::getLabels()));

        $terminationDate = now()->subMonthsNoOverflow(2)->endOfMonth();

        $mailable = new CancelAssociationMembershipUserInfoMail($user, $associationId, $terminationDate);

        $deadlineDate = $mailable->getDeadlineForTosAndSubscription(Carbon::make($terminationDate));

        $mailable->assertSeeInText($deadlineDate->format('d.m.Y'));

        $mailable->assertSeeInText($terminationDate->format('d.m.Y'));

        $this->assertNotSame($terminationDate->format('Y-m-d'), $deadlineDate->format('Y-m-d'));

        $this->assertSame(
            now()->subMonthsNoOverflow(2)->endOfMonth()->format('Y-m-d'),
            $terminationDate->format('Y-m-d')
        );

        $this->assertSame(
            now()->addMonthsNoOverflow()->addDay()->format('Y-m-d'),
            $deadlineDate->format('Y-m-d')
        );
    }
}
