<?php

namespace Tests\Unit\Actions\Newsletter;

use App\Actions\Newsletter\SubscribeToMailcoachList;
use App\Enums\Newsletter\MailcoachList;
use App\Http\Integrations\Mailcoach\Requests\SubscribeToListRequest;
use Illuminate\Support\Str;
use Saloon\Http\Faking\MockResponse;
use Saloon\Laravel\Facades\Saloon;
use Tests\TestCase;

class SubscribeNewsletterTest extends TestCase
{
    public function test_subscription_successful(): void
    {
        Saloon::fake([
            SubscribeToListRequest::class => MockResponse::make([
                'data' => [
                    'uuid' => Str::uuid()->toString(),
                    'emailListUuid' => Str::uuid()->toString(),
                    'email' => '<EMAIL>',
                    'firstName' => 'Max',
                    'lastName' => 'Mustermann',
                    'extraAttributes' => [],
                    'tags' => [],
                    'subscribedAt' => now(),
                    'unsubscribedAt' => null,
                    'createdAt' => now(),
                    'updatedAt' => now(),
                ],
            ]),
        ]);

        $user = $this->createPharmacyUser();

        $subscriber = SubscribeToMailcoachList::execute($user, MailcoachList::System);

        $this->assertNotNull($subscriber->subscribedAt);
        $this->assertNull($subscriber->unsubscribedAt);
    }
}
