<?php

namespace Tests\Unit\Actions\Newsletter;

use App\Actions\Newsletter\DeleteSubscriber;
use App\Enums\Newsletter\MailcoachList;
use App\Http\Integrations\Mailcoach\Requests\DeleteSubscriberRequest;
use App\Integrations\MailcoachIntegration;
use Saloon\Http\Faking\MockResponse;
use Saloon\Laravel\Facades\Saloon;
use Tests\TestCase;

class DeleteSubscriberTest extends TestCase
{
    public function test_unsubscribing_successful(): void
    {
        Saloon::fake([
            DeleteSubscriberRequest::class => MockResponse::make(status: 204),
        ]);

        $user = $this->createPharmacyUser();
        $user->setIntegration(new MailcoachIntegration(
            systemListId: 1,
            advertisingListId: 1,
        ));

        DeleteSubscriber::execute($user, MailcoachList::Advertising);

        $this->assertFalse($user->refresh()->hasMailcoachSubscriptions(MailcoachList::Advertising));
    }
}
