<?php

namespace Tests\Unit\Actions\Users;

use App\Actions\TokenService\AddUsersToTokenServiceAction;
use App\Actions\Users\CreateDatabaseUserAction;
use App\Actions\Users\CreateIDPUserAction;
use App\Actions\Users\CreateUserAction;
use App\Enums\PharmacyRoleEnum;
use App\Enums\SalutationEnum;
use App\Pharmacy;
use App\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;
use Mockery;
use Mockery\MockInterface;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class CreateUserActionTest extends TestCase
{
    use RefreshDatabase;

    public function test_create_fails_without_set_values(): void
    {
        $this->expectException(\Error::class);
        $createUserAction = app(CreateUserAction::class);

        $createUserAction->createUser();
    }

    public function test_create_minimally_possible_user(): void
    {
        $this->mock(CreateDatabaseUserAction::class, function ($mock) {
            $mock->shouldReceive('create');
            $mock->shouldReceive('get')->andReturn(User::factory()->make());
        });
        $this->partialMock(CreateIDPUserAction::class, function ($mock) {
            $mock->shouldReceive('createUser')->once();
        });

        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->createUser();
    }

    public function test_create_pharmacy_user(): void
    {
        $pharmacies = collect([Pharmacy::factory()->make(['uses_chat' => true]), Pharmacy::factory()->make()]);

        $this->mock(CreateDatabaseUserAction::class, function ($mock) use ($pharmacies) {
            $mock->shouldReceive('create');
            $mock->shouldReceive('asPharmacyUser');
            $mock->shouldReceive('assignToPharmacy')->twice();
            $mock->shouldReceive('get')->andReturn(User::factory()->make([
                'pharmacies' => $pharmacies,
            ]));
        });
        $this->partialMock(CreateIDPUserAction::class, function ($mock) {
            $mock->shouldReceive('createUser')->once();
        });
        $this->mock(AddUsersToTokenServiceAction::class, function ($mock) {
            $mock->shouldReceive('execute')->once();
        });

        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setSalutation(SalutationEnum::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setPhone('phone')
            ->setNotificationsEmail('<EMAIL>')
            ->setPharmacyUser()
            ->setPharmacies($pharmacies, PharmacyRoleEnum::EMPLOYEE)
            ->createUser();
    }

    public function test_user_cannot_create_sub_owners(): void
    {
        $this->expectException(HttpException::class);
        $user = Mockery::mock(User::class);
        $user->allows('cannot')->andReturns(true);
        $this->actingAs($user);

        $this->mock(CreateDatabaseUserAction::class, function ($mock) {
            $mock->shouldReceive('create');
            $mock->shouldReceive('asPharmacyUser');
            $mock->shouldReceive('get')->andReturn(User::factory()->make());
        });

        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setSalutation(SalutationEnum::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setPhone('phone')
            ->setNotificationsEmail('<EMAIL>')
            ->setPharmacyUser()
            ->setPharmacies(collect([Pharmacy::factory()->make(), Pharmacy::factory()->make()]), PharmacyRoleEnum::SUB_OWNER)
            ->createUser();
    }

    public function test_user_creates_sub_owners(): void
    {
        $user = Mockery::mock(User::class);
        $user->allows('cannot')->andReturns(false);
        $this->actingAs($user);

        $this->mock(CreateDatabaseUserAction::class, function ($mock) {
            $mock->shouldReceive('create');
            $mock->shouldReceive('asPharmacyUser');
            $mock->shouldReceive('assignToPharmacy')->twice();
            $mock->shouldReceive('get')->andReturn(User::factory()->make());
        });
        $this->partialMock(CreateIDPUserAction::class, function ($mock) {
            $mock->shouldReceive('createUser')->once();
        });
        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setSalutation(SalutationEnum::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setPhone('phone')
            ->setNotificationsEmail('<EMAIL>')
            ->setPharmacyUser()
            ->setPharmacies(collect([Pharmacy::factory()->make(), Pharmacy::factory()->make()]), PharmacyRoleEnum::SUB_OWNER)
            ->createUser();
    }

    public function test_idp_user_cannot_be_created(): void
    {
        $this->expectExceptionObject(ValidationException::withMessages([
            'notifications_email' => ['E-Mail für Benachrichtigungen muss ausgefüllt werden.'],
        ]));

        $this->mock(CreateDatabaseUserAction::class, function ($mock) {
            $mock->shouldReceive('create');
            $mock->shouldReceive('get')->andReturn(User::factory()->make());
        });
        $this->partialMock(CreateIDPUserAction::class, function (MockInterface $mock) {
            $mock->allows('createUser')->never();
        });

        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setSalutation(SalutationEnum::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setPhone('phone')
            ->setNotificationsEmail('')
            ->createUser();
    }

    public function test_idp_validation_fails(): void
    {
        $this->expectExceptionObject(ValidationException::withMessages([
            'notifications_email' => ['Bla.'],
        ]));

        $this->mock(CreateDatabaseUserAction::class, function ($mock) {
            $mock->shouldReceive('create');
            $mock->shouldReceive('get')->andReturn(User::factory()->create());
        });
        $this->partialMock(CreateIDPUserAction::class, function (MockInterface $mock) {
            $mock->allows('createUser')->once()->andThrow(
                ValidationException::withMessages([
                    'notifications_email' => ['Bla.'],
                ])
            );
        });

        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setSalutation(SalutationEnum::MR)
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setPhone('phone')
            ->setNotificationsEmail('<EMAIL>')
            ->createUser();

        $this->assertDatabaseEmpty('users');
    }

    public function test_create_company_user(): void
    {
        $user = User::factory()->create();
        $this->mock(CreateDatabaseUserAction::class, function (MockInterface $mock) use ($user) {
            $mock->allows('create');
            $mock->allows('get')->andReturns($user);
        });
        $this->partialMock(CreateIDPUserAction::class, function (MockInterface $mock) {
            $mock->expects('createUser');
        });

        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setPhone('phone')
            ->setNotificationsEmail('<EMAIL>')
            ->setCompany('company')
            ->createUser();

        $this->assertDatabaseHas('company_users', [
            'user_id' => $user->id,
            'name' => 'company',
        ]);
    }

    public function test_company_name_null(): void
    {
        $user = User::factory()->create();
        $this->mock(CreateDatabaseUserAction::class, function (MockInterface $mock) use ($user) {
            $mock->allows('create');
            $mock->allows('get')->andReturns($user);
        });
        $this->partialMock(CreateIDPUserAction::class, function (MockInterface $mock) {
            $mock->expects('createUser');
        });

        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail('<EMAIL>')
            ->setPhone('phone')
            ->setNotificationsEmail('<EMAIL>')
            ->setCompany(null)
            ->createUser();

        $this->assertDatabaseEmpty('company_users');
    }

    public function ensure_email_can_be_a_duplicate(): void
    {
        $this->partialMock(CreateIDPUserAction::class, function ($mock) {
            $mock->shouldReceive('createUser')->twice();
        });
        $createUserAction = app(CreateUserAction::class);
        assert($createUserAction instanceof CreateUserAction);
        $email = '<EMAIL>';

        $this->assertDatabaseCount('users', 0);
        $createUserAction
            ->setFirstName('John')
            ->setLastName('Doe')
            ->setLoginEmail($email)
            ->setNotificationsEmail($email)
            ->createUser();

        $this->assertDatabaseCount('users', 1);
        $createUserAction = app(CreateUserAction::class);
        assert($createUserAction instanceof CreateUserAction);

        $createUserAction
            ->setFirstName('Jane')
            ->setLastName('Doe')
            ->setLoginEmail($email)
            ->setNotificationsEmail($email)
            ->createUser();

        $this->assertDatabaseCount('users', 2);
    }
}
