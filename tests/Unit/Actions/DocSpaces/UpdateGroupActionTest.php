<?php

namespace Tests\Unit\Actions\DocSpaces;

use App\Actions\DocSpaces\CreateGroupAction;
use App\Actions\DocSpaces\UpdateGroupAction;
use App\DocSpaceGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class UpdateGroupActionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_a_docspace_group_can_be_created_and_updated()
    {
        $createResponse = [
            'name' => 'TEST',
            'id' => $this->faker->uuid,
        ];

        $updateResponse = array_merge($createResponse, [
            'name' => 'TEST UPDATE',
        ]);

        Http::fake([
            config('oidc-auth.provider.urlAccessToken') => Http::response(json_encode([
                'access_token' => '123',
            ])),
            config('sdr.apiUrl').'/group/v1' => Http::response(json_encode($createResponse)),
            config('sdr.apiUrl').'/group/v1/*' => Http::response(json_encode($updateResponse)),
            config('sdr.apiUrl').'/user/v1' => Http::response(json_encode([
                'id' => $this->faker->uuid,
            ])),
        ]);

        [$owner, $pharmacy] = $this->createOwnerForSDR(true, true, true);

        app(CreateGroupAction::class, [
            'pharmacy' => $pharmacy,
            'name' => $createResponse['name'],
            'selectedUsers' => [$owner->id => $owner],
        ])->execute();

        $group = DocSpaceGroup::first();
        $user = $this->createPharmacyEmployee($pharmacy);

        app(UpdateGroupAction::class, [
            'pharmacy' => $pharmacy,
            'group' => $group,
            'name' => $updateResponse['name'],
            'selectedUsers' => [$owner->id => $owner, $user->id => $user],
        ])->execute();

        $this->assertEquals(DocSpaceGroup::count(), 1);
        $this->assertEquals(DocSpaceGroup::first()->users->count(), 2);
    }
}
