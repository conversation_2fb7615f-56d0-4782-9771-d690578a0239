<?php

namespace Tests\Unit\Actions\DocSpaces;

use App\Actions\DocSpaces\CreateDocSpaceAction;
use App\DocSpace;
use App\DocSpaceGroup;
use App\Enums\DocSpaceType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CreateDocSpaceActionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_a_docspace_can_be_created()
    {
        $response = $this->fakeSDRHttpWithOIDCAuth();

        [$owner, $pharmacy] = $this->createOwnerForSDR(true, true, true);

        $docSpaceGroup = DocSpaceGroup::create([
            'name' => 'Test Group',
            'sdr_group_id' => $this->faker->uuid,
            'pharmacy_id' => $pharmacy->id,
        ]);

        app(CreateDocSpaceAction::class, [
            'pharmacy' => $pharmacy,
            'name' => $response['name'],
            'description' => $response['description'],
            'type' => $response['auditProof'] ? DocSpaceType::REVISION_SAFE->value : DocSpaceType::NORMAL->value,
            'retentionDuration' => $response['retentionDuration'],
            'groups' => [$docSpaceGroup->id],
            'hardQuotaInGB' => $response['quota']['hard'],
        ])->execute();

        $this->assertEquals(DocSpace::count(), 1);
        $this->assertEquals(DocSpace::first()->docSpaceGroups->count(), 1);
    }
}
