<?php

namespace Tests\Unit\Actions;

use App\Actions\Subscription\CancelAllMembershipToDate;
use App\Contracts\IOrderable;
use App\Enums\InvoiceStatusEnum;
use App\Invoice;
use App\Subscription;
use App\SubscriptionOrder;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\LazilyRefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tests\TestCase;

class CancelAllMembershipToDateTest extends TestCase
{
    use LazilyRefreshDatabase;

    public function test_a_subscription_can_be_canceled(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->travelTo(Carbon::parse('2020-05-05'));

        $pharmacy->subscribeToPlan('extern_base');

        $subscription = Subscription::first();
        $subscription->update([
            'cycle_ends_at' => now()->addMonths(5)->endOfMonth(),
        ]);

        $this->travelTo(Carbon::parse('2020-08-15'));

        $latestOrder = SubscriptionOrder::first();
        $latestOrder->update([
            'ended_at' => now()->addMonths(5)->endOfMonth(),
            'total_price' => config('subscription.plans.extern_base.price_per_month') * 5,
        ]);

        $this->assertEquals(config('subscription.plans.extern_base.price_per_month') * 5, $latestOrder->total_price);

        $date = Carbon::parse('2020-08-31');
        app(CancelAllMembershipToDate::class)->handle($pharmacy, $date);

        $latestOrder->refresh();

        // Subscription is removed to only 6 full months
        $this->assertEquals(config('subscription.plans.extern_base.price_per_month') * 3, $latestOrder->total_price);
        $this->assertEquals($date->format('d-m-Y'), $latestOrder->ended_at->format('d-m-Y'));
        $this->assertEquals($date->format('d-m-Y'), $latestOrder->subscription->ends_at->format('d-m-Y'));

        $this->assertTrue($pharmacy->isSubscribedTo('extern_base'));
    }

    public function test_it_deletes_orders_if_full_order_is_in_future(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->travelTo(Carbon::parse('2020-04-05'));

        $pharmacy->subscribeToPlan('extern_base');

        $this->travelTo(Carbon::parse('2020-06-01'));

        $this->prolongSubscription();

        $this->travelTo(Carbon::parse('2020-06-03'));

        $date = Carbon::parse('2020-05-31');

        $this->assertDatabaseCount('subscription_orders', 2);

        app(CancelAllMembershipToDate::class)->handle($pharmacy, $date);

        $this->assertDatabaseCount('subscription_orders', 1);

        $latestOrder = SubscriptionOrder::first();

        // Subscription is removed to only 8 full months
        $this->assertEquals(config('subscription.plans.extern_base.price_per_month') * 1, $latestOrder->total_price);
        $this->assertEquals($date->format('d-m-Y'), $latestOrder->ended_at->format('d-m-Y'));
        $this->assertEquals($date->format('d-m-Y'), $latestOrder->subscription->ends_at->format('d-m-Y'));

        $this->assertFalse($pharmacy->fresh()->isSubscribedTo('extern_base'));
    }

    public function test_a_subscription_can_be_canceled_with_multiple_pharmacies(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $secondPharmacy = $this->createPharmacyForUser($user);

        $this->travelTo(Carbon::parse('2020-05-05'));

        $pharmacy->subscribeToPlan('extern_base');
        $secondPharmacy->subscribeToPlan('extern_base');

        $this->travelTo(Carbon::parse('2020-06-15'));

        $latestOrder = SubscriptionOrder::first();

        $this->assertEquals(config('subscription.plans.extern_base.price_per_month') * 1, $latestOrder->total_price);

        $date = Carbon::parse('2020-05-31');
        $this->assertCount(2, Subscription::all());
        $this->assertCount(2, SubscriptionOrder::all());
        app(CancelAllMembershipToDate::class)->handle($pharmacy, $date);

        $this->assertEquals(0, $latestOrder->refresh()->total_price);

        $this->assertCount(2, Subscription::all());
        $this->assertCount(2, SubscriptionOrder::all());
        $this->assertFalse($pharmacy->isSubscribedTo('extern_base'));
        $this->assertTrue($secondPharmacy->isSubscribedTo('extern_base'));
    }

    public function test_cancel_in_the_future(): void
    {
        $this->actingAs($this->createStaff());
        $pharmacy = $this->createPharmacyUserWithPharmacy()[1];

        $this->assertFalse($pharmacy->hasActiveSubscription());
        $pharmacy->subscribeToPlan('base');

        $this->assertCount(1, $pharmacy->oldSubscriptions);
        $this->assertCount(1, $pharmacy->subscriptionOrders);

        $subscription = $pharmacy->oldSubscriptions->first();
        $this->assertSame(now()->toDateString(), $subscription->cycle_started_at->toDateString());
        $this->assertSame(now()->addMonthNoOverflow()->endOfMonth()->toDateString(), $subscription->cycle_ends_at->toDateString());
        $this->assertNull($subscription->ends_at);

        $subscriptionOrder = $pharmacy->subscriptionOrders->first();
        $this->assertSame(now()->toDateString(), $subscriptionOrder->started_at->toDateString());
        $this->assertSame(now()->addMonthNoOverflow()->endOfMonth()->toDateString(), $subscriptionOrder->ended_at->toDateString());

        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), $cancelDate = now()->addMonthsNoOverflow(6));

        $subscription = $pharmacy->oldSubscriptions->first();
        $subscriptionOrder = $pharmacy->subscriptionOrders->first();

        $this->assertSame(now()->toDateString(), $subscription->cycle_started_at->toDateString());
        $this->assertSame(now()->addMonthNoOverflow()->endOfMonth()->toDateString(), $subscription->cycle_ends_at->toDateString());
        $this->assertSame(now()->addMonthsNoOverflow(6)->endOfMonth()->toDateString(), $subscription->ends_at->toDateString());

        $this->assertSame(now()->toDateString(), $subscriptionOrder->started_at->toDateString());
        $this->assertSame(now()->addMonthNoOverflow()->endOfMonth()->toDateString(), $subscriptionOrder->ended_at->toDateString());
    }

    public function test_cancel_in_the_past(): void
    {
        $startMonth = now()->startOfMonth();
        $this->actingAs($this->createStaff());
        $pharmacy = $this->createPharmacyUserWithPharmacy()[1];
        $this->assertFalse($pharmacy->hasActiveSubscription());

        $this->travelTo(now()->subMonthsNoOverflow(6)->endOfMonth()->subDay());

        $pharmacy->subscribeToPlan('base');
        $this->assertCount(1, $pharmacy->oldSubscriptions);
        $this->assertCount(1, $pharmacy->subscriptionOrders);

        $pharmacy->oldSubscriptions->first()->cancel();
        $this->assertFalse($pharmacy->hasActiveSubscription());

        $this->travelTo(now()->addMonthsNoOverflow(2));
        $pharmacy->subscribeToPlan('extended');
        $this->assertTrue($pharmacy->refresh()->hasActiveSubscription());
        $this->assertCount(2, $pharmacy->oldSubscriptions);
        $this->assertCount(2, $pharmacy->subscriptionOrders);

        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $pharmacy->refresh();

        $this->assertCount(2, $pharmacy->oldSubscriptions);
        $this->assertCount(5, $pharmacy->subscriptionOrders);

        $this->assertSame(now()->startOfMonth()->toDateString(), $startMonth->toDateString());

        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), $cancelDate = now()->subMonthsNoOverflow(6));
        $pharmacy->refresh();
        $this->assertCount(1, $pharmacy->oldSubscriptions);
        $this->assertCount(1, $pharmacy->subscriptionOrders);

        $subscription = $pharmacy->oldSubscriptions->first();
        $subscriptionOrder = $pharmacy->subscriptionOrders->first();

        $this->assertSame($cancelDate->endOfMonth()->toDateString(), $subscription->ends_at->toDateString());
        $this->assertSame($cancelDate->endOfMonth()->toDateString(), $subscriptionOrder->ended_at->toDateString());
    }

    public function test_cancel_upgrade(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $pharmacy->subscribeToPlan('base');
        $pharmacy->subscribeToPlan('extended');

        $subscriptions = Subscription::all();
        $subscriptionOrders = SubscriptionOrder::all();
        $this->assertCount(2, $subscriptions);
        $this->assertCount(2, $subscriptionOrders);
        $this->assertCount(2, $pharmacy->activeSubscriptions);
        $this->assertNull($pharmacy->activeSubscriptions->first()->ends_at);

        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), $cancelDate = now());
        $pharmacy->refresh();

        $this->assertSame(now()->toDateString(), $pharmacy->oldSubscriptions->first()->ends_at->toDateString());
        $this->assertSame($cancelDate->toDateString(), $pharmacy->oldSubscriptions->last()->ends_at->toDateString());
    }

    public function test_cancel_downgrade(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $pharmacy->subscribeToPlan('extended');
        $pharmacy->subscribeToPlan('base');

        $this->assertCount(1, Subscription::all());
        $this->assertNull($pharmacy->activeSubscriptions->first()->ends_at);

        $cancelDate = now()->endOfMonth();
        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), $cancelDate);
        $pharmacy->refresh();

        $this->assertSame($cancelDate->toDateString(), $pharmacy->activeSubscriptions->first()->ends_at->toDateString());
    }

    public function test_cancelling_with_subscription_history(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->subscribeToPlan('base');

        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $pharmacy->oldSubscriptions->first()->cancelNow();
        $firstSubscriptionEndsAt = now();

        $this->assertCount(1, $pharmacy->oldSubscriptions);
        $this->assertCount(3, $pharmacy->subscriptionOrders);

        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $pharmacy->subscribeToPlan('extended');
        $pharmacy->refresh();

        $this->assertCount(2, $pharmacy->oldSubscriptions);
        $this->assertCount(4, $pharmacy->subscriptionOrders);

        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();
        $cancelDate = now();
        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();

        $pharmacy->activeSubscriptions()->first()->cancelNow();
        $pharmacy->refresh();

        $this->assertCount(2, $pharmacy->oldSubscriptions);
        $this->assertCount(6, $pharmacy->subscriptionOrders);

        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());

        $pharmacy->subscribeToPlan('base');
        $pharmacy->refresh();

        $this->assertCount(3, $pharmacy->oldSubscriptions);
        $this->assertCount(7, $pharmacy->subscriptionOrders);

        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), $cancelDate);
        $pharmacy->refresh();

        $this->assertCount(2, $pharmacy->oldSubscriptions);
        $this->assertCount(5, $pharmacy->subscriptionOrders);
        $this->assertSame($firstSubscriptionEndsAt->toDateString(), $pharmacy->oldSubscriptions()->orderBy('created_at')->first()->ends_at->toDateString());
        $this->assertSame($firstSubscriptionEndsAt->endOfMonth()->toDateString(), $pharmacy->oldSubscriptions()->orderBy('created_at')->first()->cycle_ends_at->toDateString());
        $this->assertSame($cancelDate->toDateString(), $pharmacy->oldSubscriptions()->orderBy('created_at', 'DESC')->first()->ends_at->toDateString());
        $this->assertSame($cancelDate->toDateString(), $pharmacy->subscriptionOrders()->orderBy('created_at', 'DESC')->first()->ended_at->toDateString());
        $this->assertNotSame($cancelDate->toDateString(), $pharmacy->subscriptionOrders()->orderBy('created_at', 'DESC')->get()[1]->ended_at->toDateString());
        $this->assertSame($cancelDate->addMonthNoOverflow()->endOfMonth()->toDateString(), $pharmacy->oldSubscriptions()->orderBy('created_at', 'DESC')->first()->cycle_ends_at->toDateString());
    }

    public function test_ignore_orders_that_ended_before_cancel_date(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->subscribeToPlan('base');

        $this->travelTo(now()->addMonthsNoOverflow(2)->startOfMonth());
        $this->prolongSubscription();

        $invoice = Invoice::factory([
            'billing_address_id' => $pharmacy->billingAddress->id,
            'status_code' => InvoiceStatusEnum::COMPLETED->value,
        ])->create();

        SubscriptionOrder::each(static fn ($order) => $order->update(['invoice_id' => $invoice->id]));

        $this->travelTo(now()->addMonthNoOverflow()->startOfMonth());
        $this->prolongSubscription();

        $this->assertCount(3, $pharmacy->subscriptionOrders);

        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), now());

        $this->expectException(\Exception::class);
        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), now()->subMonthsNoOverflow(2));
    }

    public function test_cancellation_date_in_the_future_does_not_change_cycle(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->subscribeToPlan('base');

        app(CancelAllMembershipToDate::class)->handle($pharmacy->refresh(), now()->addMonthsNoOverflow(2));
        $pharmacy->refresh();

        $this->assertSame(now()->addMonthsNoOverflow(2)->endOfMonth()->toDateString(), $pharmacy->oldSubscriptions->first()->ends_at->toDateString());
        $this->assertSame(now()->addMonthNoOverflow()->endOfMonth()->toDateString(), $pharmacy->oldSubscriptions->first()->cycle_ends_at->toDateString());
    }

    public function test_can_cancel_to_the_end_of_month_of_last_created_invoice(): void
    {
        $threeMonthsAgo = now()->subMonthsNoOverflow(3)->toImmutable();

        Http::fake([
            '*' => Http::response([
                'status_code' => 2,
                'error_code' => 0,
                'transaction_id' => Str::uuid(),
            ]),
        ]);

        $this->travelTo($threeMonthsAgo);

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->acceptTerms(false);

        $this->assertNotNull($pharmacy->billingAddress);
        $this->assertSame(0, $pharmacy->acceptedTermsOfUse()->count());
        $this->assertFalse($pharmacy->hasActiveSubscription());
        $this->assertDatabaseCount(Subscription::class, 0);

        $this->acceptTermsForPharmacy($pharmacy);
        $pharmacy->subscribeToPlan('extended');
        $pharmacy->refresh();

        $this->assertSame(1, $pharmacy->acceptedTermsOfUse()->count());
        $this->assertTrue($pharmacy->hasActiveSubscription());
        $this->assertDatabaseCount(Subscription::class, 1);
        $this->assertDatabaseCount(SubscriptionOrder::class, 1);

        $this->travelBack();

        $this->prolongSubscription();

        $this->assertTrue($pharmacy->hasActiveSubscription());
        $this->assertDatabaseCount(Subscription::class, 1);
        $this->assertSame(2, SubscriptionOrder::all()->count());
        $this->assertCount(0, $owner->billingAddressesAll()->first()->invoices);
        $owner->refresh();

        $this->swapQueue();

        $this->artisan(
            'payment:dispatch-invoices',
            [
                'from' => $threeMonthsAgo->format('Y-m-d'),
                'till' => now()->endOfMonth()->format('Y-m-d'),
            ]
        )->expectsConfirmation('Dispatching all invoices for orders between '.$threeMonthsAgo->format('Y-m-d').' and '.now()->endOfMonth()->format('Y-m-d'), 'yes');
        $owner->refresh();

        $this->assertCount(1, $owner->billingAddressesAll()->first()->invoices);

        app(CancelAllMembershipToDate::class)->handle(
            $pharmacy->refresh(),
            now()->subMonthNoOverflow()->endOfMonth()
        );

        $pharmacy->refresh();

        $this->assertFalse($pharmacy->hasActiveSubscription());
        $this->assertSame(2, SubscriptionOrder::all()->count());

        $this->travelTo(now()->subMonthNoOverflow());
        $pharmacy->refresh();

        $this->assertTrue($pharmacy->hasActiveSubscription());

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Die Kündigung ist nicht möglich, da mindestens eine Bestellung bearbeitet werden würde, die im gewählten Zeitraum bereits abgerechnet wurde.');

        app(CancelAllMembershipToDate::class)->handle(
            $pharmacy->refresh(),
            now()->subMonthNoOverflow()->endOfMonth()
        );

        $this->assertSame(2, SubscriptionOrder::all()->count());

        $this->travelTo(now()->subMonthNoOverflow());
        $this->assertTrue($pharmacy->hasActiveSubscription());
    }

    private function prolongSubscription()
    {
        Subscription::query()
            ->whereDate('cycle_ends_at', '<', now())
            ->where(function (Builder $query) {
                $query->whereNull('ends_at')
                    ->orWhereColumn('ends_at', '>', 'cycle_ends_at');
            })
            ->whereDoesntHave('subscribable.oldSubscriptions', function (Builder $query) {
                return $query->whereDate('cycle_started_at', '>=', now());
            })
            ->eachById(function (Subscription $subscription) {
                $orderable = $subscription->subscribable;

                if (! $orderable instanceof IOrderable) {
                    return;
                }

                try {
                    DB::transaction(function () use ($orderable, $subscription) {
                        $newSubscription = $subscription->addACycle();

                        $newSubscription->save();

                        $order = $newSubscription->makeOrderForCurrentCycle($orderable);
                        $order->forceFill([
                            'subscription_id' => $newSubscription->id,
                            'total_price' => $order->calcPrice(),
                        ])->save();
                    });
                } catch (Exception $e) {
                    report($e);
                }
            }, 100);
    }
}
