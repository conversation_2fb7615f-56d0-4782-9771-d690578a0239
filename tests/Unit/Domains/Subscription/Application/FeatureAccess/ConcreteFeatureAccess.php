<?php

namespace Tests\Unit\Domains\Subscription\Application\FeatureAccess;

use App\Domains\Subscription\Application\FeatureAccess\FeatureAccess;
use Illuminate\Database\Eloquent\Builder;

class ConcreteFeatureAccess extends FeatureAccess
{
    protected function canUseNew(): bool
    {
        return true;
    }

    protected function scopeCanUseNew(Builder $builder): Builder
    {
        return $builder;
    }
}
