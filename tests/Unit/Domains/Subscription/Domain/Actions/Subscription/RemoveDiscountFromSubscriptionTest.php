<?php

namespace Domains\Subscription\Domain\Actions\Subscription;

use App\Domains\Subscription\Application\Discounts\BaseSubscriptionAssociationPaidStripeDiscount;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Domains\Subscription\Domain\Actions\Subscription\AddDiscountToSubscription;
use App\Domains\Subscription\Domain\Actions\Subscription\RemoveDiscountFromSubscription;
use Tests\TestCase;

class RemoveDiscountFromSubscriptionTest extends TestCase
{
    public function test_pharmacy_is_not_subscribed(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->expectExceptionMessage('Apotheke [1] hat kein laufendes Abo.');

        app(RemoveDiscountFromSubscription::class)->execute($pharmacy, new BaseSubscriptionAssociationPaidStripeDiscount);
    }

    public function test_discount_is_removed(): void
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createRealStripeSubscription($pharmacy, BaseStripeProduct::make());

        app(AddDiscountToSubscription::class)->execute($pharmacy, new BaseSubscriptionAssociationPaidStripeDiscount);

        $this->assertNull($pharmacy->subscription()->refresh()->discounts);

        $subscription = app(RemoveDiscountFromSubscription::class)->execute($pharmacy, new BaseSubscriptionAssociationPaidStripeDiscount);

        $this->assertCount(0, $subscription->discounts);
        $this->assertNull($subscription->discount);
    }
}
