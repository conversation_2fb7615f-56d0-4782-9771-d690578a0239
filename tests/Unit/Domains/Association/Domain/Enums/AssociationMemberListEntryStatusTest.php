<?php

namespace Domains\Association\Domain\Enums;

use App\Domains\Association\Domain\Enums\AssociationMemberListEntryStatus;
use App\Domains\Association\Domain\Enums\MemberImportStatus;
use Tests\TestCase;

class AssociationMemberListEntryStatusTest extends TestCase
{
    public function test_member_import_status_is_mappable(): void
    {
        foreach (MemberImportStatus::cases() as $case) {
            $result = AssociationMemberListEntryStatus::from($case->value);
            $this->assertNotNull($result);
        }
    }

    public function test_each_case_has_color_and_translation(): void
    {
        foreach (AssociationMemberListEntryStatus::cases() as $case) {
            $this->assertNotEmpty($case->color());
            $this->assertNotEmpty($case->translate());
        }
    }
}
