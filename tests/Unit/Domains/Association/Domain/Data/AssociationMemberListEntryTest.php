<?php

namespace Domains\Association\Domain\Data;

use App\Domains\Association\Domain\Data\AssociationMemberListEntry;
use App\Domains\Association\Domain\Enums\AssociationMemberListEntryStatus;
use Carbon\Carbon;
use Tests\TestCase;

class AssociationMemberListEntryTest extends TestCase
{
    private function makeEntry(AssociationMemberListEntryStatus $status): AssociationMemberListEntry
    {
        return new AssociationMemberListEntry(
            id: '1',
            title: null,
            firstName: null,
            lastName: null,
            email: '<EMAIL>',
            createdAt: Carbon::now(),
            pharmaciesCount: null,
            status: $status,
            statusDescription: null,
        );
    }

    public function test_is_deletable_only_for_failed(): void
    {
        foreach (AssociationMemberListEntryStatus::cases() as $status) {
            $entry = $this->makeEntry($status);
            $expected = $status === AssociationMemberListEntryStatus::Failed;
            $this->assertSame($expected, $entry->isDeletable(), "Status $status->name should be deletable: ".($expected ? 'true' : 'false'));
        }
    }

    public function test_is_association_changable_for_registration_sent_and_active(): void
    {
        foreach (AssociationMemberListEntryStatus::cases() as $status) {
            $expected = in_array($status, [
                AssociationMemberListEntryStatus::RegistrationSent,
                AssociationMemberListEntryStatus::Active,
            ], true);
            $entry = $this->makeEntry($status);
            $this->assertSame($expected, $entry->isAssociationChangable(), "Status $status->name should be association changable: ".($expected ? 'true' : 'false'));
        }
    }

    public function test_is_editable_logic(): void
    {
        foreach (AssociationMemberListEntryStatus::cases() as $status) {
            $expected = $status === AssociationMemberListEntryStatus::Failed
                || in_array($status, [
                    AssociationMemberListEntryStatus::RegistrationSent,
                    AssociationMemberListEntryStatus::Active,
                ], true);
            $entry = $this->makeEntry($status);
            $this->assertSame($expected, $entry->isEditable(), "Status $status->name should be editable: ".($expected ? 'true' : 'false'));
        }
    }
}
