<?php

namespace Tests\Unit\Domains\ShiftPlan\Application\Controllers\Api;

use App\ShiftPlan;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\ShiftPlanHelper;
use Tests\TestCase;

/** @group shiftplan */
class ShiftPlanControllerTest extends TestCase
{
    use RefreshDatabase, ShiftPlanHelper;

    public function test_it_can_fetch_all_shift_plans(): void
    {
        $this->changeShiftPlanFeatureSettings(true);

        $this
            ->json('GET', route('api.shift-plan.plans.index'))
            ->assertUnauthorized();

        $pharmacy = $this->setupWorld(isOwner: true, isBetaUser: false);
        $user = $pharmacy->owner();
        ShiftPlan::factory()->create(['owner_id' => $user->id]);

        $this
            ->json('GET', route('api.shift-plan.plans.index'))
            ->assertOk()
            ->assertJsonCount(1, 'data');
    }

    public function test_it_can_fetch_only_owner_related_shift_plans(): void
    {
        $this->changeShiftPlanFeatureSettings(true);

        $this
            ->json('GET', route('api.shift-plan.plans.index'))
            ->assertUnauthorized();

        $pharmacy = $this->setupWorld(isOwner: true, isBetaUser: false);
        $firstUser = $pharmacy->owner();
        [$secondUser] = $this->createPharmacyUserWithPharmacy();
        /** @var ShiftPlan $firstShiftPlan */
        $firstShiftPlan = ShiftPlan::factory()->create(['owner_id' => $firstUser->id]);
        ShiftPlan::factory()->create(['owner_id' => $secondUser->id]);

        $this->actingAs($firstUser);

        $this
            ->json('GET', route('api.shift-plan.plans.index'))
            ->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJsonFragment(['id' => $firstShiftPlan->uuid]);
    }
}
