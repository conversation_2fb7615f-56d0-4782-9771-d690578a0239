<?php

namespace Tests\Unit\Policies;

use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
use App\Domains\Subscription\Application\StripeProducts\AddOns\CalendarStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\AddOns\TIMStripeProduct;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Enums\InvoiceStatusEnum;
use App\Enums\KimAddressStatus;
use App\Enums\PermissionEnum;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\StaffRoleEnum;
use App\Invoice;
use App\KimAddress;
use App\Pharmacy;
use App\Policies\PharmacyPolicy;
use App\Settings\RetaxSettings;
use App\Staff;
use App\User;
use App\Vaccination;
use App\VaccinationImport;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Cashier\Subscription;
use LogicException;
use Tests\TestCase;

class PharmacyPolicyTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_view_any(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertTrue($pharmacyPolicy->viewAny($unassignedOwner));
        $this->assertTrue($pharmacyPolicy->viewAny(User::factory()->create()));
        $this->assertTrue($pharmacyPolicy->viewAny($noProfileOwner->refresh()));
        $this->assertTrue($pharmacyPolicy->viewAny($owner));
        $this->assertTrue($pharmacyPolicy->viewAny($noProfileSubOwner->refresh()));
        $this->assertTrue($pharmacyPolicy->viewAny($subOwner));
        $this->assertTrue($pharmacyPolicy->viewAny($noProfileEmployee->refresh()));
        $this->assertTrue($pharmacyPolicy->viewAny($employee));
    }

    public function test_user_can_index(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertTrue($pharmacyPolicy->index($unassignedOwner));
        $this->assertFalse($pharmacyPolicy->index(User::factory()->create()));
        $this->assertTrue($pharmacyPolicy->index($owner));
        $this->assertFalse($pharmacyPolicy->index($noProfileOwner->refresh()));
        $this->assertTrue($pharmacyPolicy->index($subOwner));
        $this->assertFalse($pharmacyPolicy->index($noProfileSubOwner->refresh()));
        $this->assertTrue($pharmacyPolicy->index($employee));
        $this->assertFalse($pharmacyPolicy->index($noProfileEmployee->refresh()));
    }

    public function test_user_can_view(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertFalse($pharmacyPolicy->view($unassignedOwner, $unassignedPharmacy));
        $this->assertFalse($pharmacyPolicy->view(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->view($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->view($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->view($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->view($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->view($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->view($employee, $pharmacy));
    }

    public function test_user_can_create(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertTrue($pharmacyPolicy->create($unassignedOwner));
        $this->assertFalse($pharmacyPolicy->create(User::factory()->create()));
        $this->assertTrue($pharmacyPolicy->create($owner));
        $this->assertFalse($pharmacyPolicy->create($noProfileOwner->refresh()));
        $this->assertFalse($pharmacyPolicy->create($subOwner));
        $this->assertFalse($pharmacyPolicy->create($noProfileSubOwner->refresh()));
        $this->assertFalse($pharmacyPolicy->create($employee));
        $this->assertFalse($pharmacyPolicy->create($noProfileEmployee->refresh()));
    }

    public function test_user_can_update(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employeeWithPermission = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::EDIT_PHARMACY]
        );
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);
        try {
            $pharmacyPolicy->update($unassignedOwner, $unassignedPharmacy);
        } catch (\Exception $e) {
            $this->assertInstanceOf(LogicException::class, $e);
        }
        $this->assertFalse($pharmacyPolicy->update(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->update($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->update($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->update($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->update($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->update($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->update($employee, $pharmacy));
        $this->assertTrue($pharmacyPolicy->update($employeeWithPermission, $pharmacy));
    }

    public function test_user_can_select_billing_address(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employeeWithPermission = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE, [PharmacyPermissionsEnum::SELECT_PHARMACY_BILLING_ADDRESS]);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertFalse($pharmacyPolicy->selectBillingAddress($unassignedOwner, $unassignedPharmacy));
        $this->assertFalse($pharmacyPolicy->selectBillingAddress(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->selectBillingAddress($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->selectBillingAddress($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->selectBillingAddress($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->selectBillingAddress($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->selectBillingAddress($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->selectBillingAddress($employee, $pharmacy));
        $this->assertTrue($pharmacyPolicy->selectBillingAddress($employeeWithPermission, $pharmacy));
    }

    public function test_user_can_delete(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertFalse($pharmacyPolicy->delete($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($noProfileEmployee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($noProfileOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($noProfileSubOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($unassignedOwner, $pharmacy));

        // AP-2489 disable delete pharmacy
        return;

        $this->assertFalse($pharmacyPolicy->delete($unassignedOwner, $unassignedPharmacy));
        $this->assertFalse($pharmacyPolicy->delete(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->delete($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->delete($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->delete($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->delete($employee, $pharmacy));

        // Are there unprocessed subscription orders?
        $this->createSubscriptionOrder([
            'orderable_id' => $pharmacy->id,
        ]);
        $pharmacy->refresh();
        $this->assertFalse($pharmacyPolicy->delete($owner, $pharmacy));

        $this->swapQueue();

        // Are there invoices with status_code 3 or 12?
        $this->artisan(
            'payment:dispatch-invoices',
            [
                'from' => now()->startOfMonth()->format('Y-m-d'),
                'till' => now()->endOfMonth()->format('Y-m-d'),
            ]
        )->expectsConfirmation('Dispatching all invoices for orders between '.now()->startOfMonth()->format('Y-m-d').' and '.now()->endOfMonth()->format('Y-m-d'), 'yes');

        $pharmacy->refresh();

        Invoice::first()->update(['status_code' => InvoiceStatusEnum::PENDING->value]);
        $this->assertFalse($pharmacyPolicy->delete($owner, $pharmacy));

        Invoice::first()->update(['status_code' => InvoiceStatusEnum::COMPLETED->value]);
        $this->assertTrue($pharmacyPolicy->delete($owner, $pharmacy));

        Invoice::first()->update(['status_code' => InvoiceStatusEnum::CANCELED->value]);
        $this->assertTrue($pharmacyPolicy->delete($owner, $pharmacy));

        // is a subscription active?
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $this->assertTrue($pharmacyPolicy->delete($owner, $pharmacy));
        $pharmacy->subscriptions()->create(Subscription::factory()->raw());
        $this->assertFalse($pharmacyPolicy->delete($owner, $pharmacy->refresh()));
    }

    public function test_user_can_activate(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertFalse($pharmacyPolicy->activate($unassignedOwner, $unassignedPharmacy));
        $this->assertFalse($pharmacyPolicy->activate(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->activate($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->activate($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->activate($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->activate($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->activate($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->activate($employee, $pharmacy));
    }

    public function test_user_can_deactivate(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);

        $this->assertFalse($pharmacyPolicy->deactivate($unassignedOwner, $unassignedPharmacy));
        $this->assertFalse($pharmacyPolicy->deactivate(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->deactivate($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->deactivate($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->deactivate($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->deactivate($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->deactivate($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->deactivate($employee, $pharmacy));
    }

    public function test_administrate_users(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);
        try {
            $this->assertFalse($pharmacyPolicy->administrateUsers($unassignedOwner, $unassignedPharmacy));

        } catch (Exception $e) {
            $this->assertInstanceOf(LogicException::class, $e);
        }
        $this->assertFalse($pharmacyPolicy->administrateUsers(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->administrateUsers($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->administrateUsers($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->administrateUsers($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertTrue($pharmacyPolicy->administrateUsers($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->administrateUsers($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->administrateUsers($employee, $pharmacy));
    }

    public function test_administrate_sub_owners(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);
        $companyUser = $this->createCompanyUser();
        $compancyPharmacy = $this->createPharmacy();
        $compancyPharmacy->assignUser($companyUser, PharmacyRoleEnum::OWNER);

        $this->assertFalse($pharmacyPolicy->administrateSubOwners($unassignedOwner));
        $this->assertFalse($pharmacyPolicy->administrateSubOwners(User::factory()->create()));
        $this->assertFalse($pharmacyPolicy->administrateSubOwners($noProfileOwner->refresh()));
        $this->assertFalse($pharmacyPolicy->administrateSubOwners($owner));
        $this->assertFalse($pharmacyPolicy->administrateSubOwners($noProfileSubOwner->refresh()));
        $this->assertFalse($pharmacyPolicy->administrateSubOwners($subOwner));
        $this->assertFalse($pharmacyPolicy->administrateSubOwners($noProfileEmployee->refresh()));
        $this->assertFalse($pharmacyPolicy->administrateSubOwners($employee));
        $this->assertTrue($pharmacyPolicy->administrateSubOwners($companyUser));
    }

    public function test_administrate_pharmacy_sub_owners(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$noProfileSubOwner, $noProfilePharmacy, $noProfileOwner] = $this->createPharmacyUserWithRole(
            PharmacyRoleEnum::SUB_OWNER
        );
        $noProfileEmployee = $this->createPharmacyEmployee($noProfilePharmacy, PharmacyRoleEnum::EMPLOYEE);
        $noProfileOwner->pharmacyProfile->forceDelete();
        $noProfileSubOwner->pharmacyProfile->forceDelete();
        $noProfileEmployee->pharmacyProfile->forceDelete();
        [$unassignedOwner, $unassignedPharmacy] = $this->createPharmacyUserWithPharmacy();
        $unassignedPharmacy->unassignUser($unassignedOwner);
        $companyUser = $this->createCompanyUser();
        $compancyPharmacy = $this->createPharmacy();
        $compancyPharmacy->assignUser($companyUser, PharmacyRoleEnum::OWNER);

        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners($unassignedOwner, $unassignedPharmacy));
        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners(User::factory()->create(), $pharmacy));
        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners($noProfileOwner->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners($noProfileSubOwner->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners($noProfileEmployee->refresh(), $noProfilePharmacy));
        $this->assertFalse($pharmacyPolicy->administratePharmacySubOwners($employee, $pharmacy));
        $this->assertTrue($pharmacyPolicy->administratePharmacySubOwners($companyUser, $compancyPharmacy));
    }

    public function test_access_calendar(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employeeWithPermissionCalendar = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::CALENDAR]
        );
        $employeeWithPermissionCalendarAdmin = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::CALENDAR_ADMIN]
        );

        $this->assertFalse($pharmacyPolicy->accessCalendar($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($employeeWithPermissionCalendar, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($employeeWithPermissionCalendarAdmin, $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, CalendarStripeProduct::make());

        $this->assertFalse($pharmacyPolicy->accessCalendar($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($employeeWithPermissionCalendar, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($employeeWithPermissionCalendarAdmin, $pharmacy));

        $pharmacy->uses_calendar = true;

        $this->assertTrue($pharmacyPolicy->accessCalendar($owner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessCalendar($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendar($employee, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessCalendar($employeeWithPermissionCalendar, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessCalendar($employeeWithPermissionCalendarAdmin, $pharmacy));
    }

    public function test_access_calendar_as_admin(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employeeWithPermissionCalendar = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::CALENDAR]
        );
        $employeeWithPermissionCalendarAdmin = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::CALENDAR_ADMIN]
        );

        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employeeWithPermissionCalendar, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employeeWithPermissionCalendarAdmin, $pharmacy));

        $pharmacy->update(['uses_calendar' => true]);
        $pharmacy->refresh();

        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employeeWithPermissionCalendar, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employeeWithPermissionCalendarAdmin, $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, CalendarStripeProduct::make());
        $pharmacy->refresh();

        $this->assertTrue($pharmacyPolicy->accessCalendarAsAdmin($owner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessCalendarAsAdmin($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessCalendarAsAdmin($employeeWithPermissionCalendar, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessCalendarAsAdmin($employeeWithPermissionCalendarAdmin, $pharmacy));
    }

    public function test_activate_calendar(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);

        $this->assertFalse($pharmacyPolicy->activateCalendar($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->activateCalendar($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->activateCalendar($employee, $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, CalendarStripeProduct::make());
        $pharmacy->refresh();

        $this->assertTrue($pharmacyPolicy->activateCalendar($owner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->activateCalendar($subOwner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->activateCalendar($employee, $pharmacy));
    }

    public function test_data_after_subscription_if_not_paying(): void
    {
        $user = $this->createMock(User::class);
        $user->method('needsToPayForPortal')->willReturn(false);

        $pharmacy = $this->createMock(Pharmacy::class);
        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertFalse($pharmacyPolicy->dataAfterSubscription($user, $pharmacy));
    }

    public function test_data_after_subscription_if_not_subscribed_in_the_past(): void
    {
        $user = $this->createMock(User::class);
        $pharmacy = $this->createMock(Pharmacy::class);
        $user->method('needsToPayForPortal')->willReturn(false);
        $pharmacy->method('wasSubscribedToOneOf')->willReturn(false);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertFalse($pharmacyPolicy->dataAfterSubscription($user, $pharmacy));
    }

    public function test_data_after_subscription_if_subscribed_in_the_past_with_vaccination_imports(): void
    {
        $user = $this->createMock(User::class);
        $pharmacy = $this->createMock(Pharmacy::class);
        $user->method('needsToPayForPortal')->willReturn(true);
        $pharmacy->method('wasSubscribedToOneOf')->willReturn(true);
        $pharmacy->method('ownerOrFail')->willReturn($user);
        $pharmacy->method('hasAcceptedTerms')->willReturn(true);

        $pharmacy->vaccinationImports()->save(VaccinationImport::factory()->create());
        $pharmacyPolicy = new PharmacyPolicy;

        $this->assertTrue($pharmacyPolicy->dataAfterSubscription($user, $pharmacy));
    }

    public function test_data_after_subscription_if_subscribed_in_the_past_with_vaccinations(): void
    {
        $user = $this->createMock(User::class);
        $pharmacy = $this->createMock(Pharmacy::class);
        $user->method('needsToPayForPortal')->willReturn(true);
        $pharmacy->method('wasSubscribedToOneOf')->willReturn(true);
        $pharmacy->method('ownerOrFail')->willReturn($user);
        $pharmacy->method('hasAcceptedTerms')->willReturn(true);

        $pharmacy->vaccinations()->save(Vaccination::factory()->create());
        $pharmacyPolicy = new PharmacyPolicy;

        $this->assertTrue($pharmacyPolicy->dataAfterSubscription($user, $pharmacy));
    }

    public function test_activate_chat(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $this->createLocalStripeSubscription($pharmacy, TIMStripeProduct::make());
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        [$unassignedSubOwner, $unassignedPharmacy, $unassignedOwner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $this->acceptTermsForPharmacy($unassignedPharmacy);
        $this->createLocalStripeSubscription($unassignedPharmacy, TIMStripeProduct::make());
        $unassignedPharmacy->unassignUser($unassignedOwner);
        $unassignedPharmacy->unassignUser($unassignedSubOwner);

        $this->assertTrue($pharmacyPolicy->activateChat($owner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->activateChat($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->activateChat($employee, $pharmacy));
        $this->assertTrue($pharmacyPolicy->activateChat($unassignedOwner, $unassignedPharmacy));
        $this->assertFalse($pharmacyPolicy->activateChat($unassignedSubOwner, $unassignedPharmacy));

        $this->assertFalse($pharmacyPolicy->activateChat($owner));
        $this->assertFalse($pharmacyPolicy->activateChat($subOwner));
        $this->assertFalse($pharmacyPolicy->activateChat($employee));
        $this->assertFalse($pharmacyPolicy->activateChat($unassignedOwner));
        $this->assertFalse($pharmacyPolicy->activateChat($unassignedSubOwner));
    }

    public function test_access_telepharmacy(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employeeWithPermissionTelepharmacy = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::TELEPHARMACY]
        );
        $employeeWithPermissionTelepharmacyAdmin = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::TELEPHARMACY_ADMIN]
        );

        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($employeeWithPermissionTelepharmacy, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($employeeWithPermissionTelepharmacyAdmin, $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, CalendarStripeProduct::make());

        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($employeeWithPermissionTelepharmacy, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($employeeWithPermissionTelepharmacyAdmin, $pharmacy));

        $pharmacy->uses_telepharmacy = true;

        $this->assertTrue($pharmacyPolicy->accessTelepharmacy($owner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessTelepharmacy($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacy($employee, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessTelepharmacy($employeeWithPermissionTelepharmacy, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessTelepharmacy($employeeWithPermissionTelepharmacyAdmin, $pharmacy));
    }

    public function test_access_telepharmacy_as_admin(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $employeeWithPermissionTelepharmacy = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::TELEPHARMACY]
        );
        $employeeWithPermissionTelepharmacyAdmin = $this->createPharmacyEmployee(
            $pharmacy,
            PharmacyRoleEnum::EMPLOYEE,
            [PharmacyPermissionsEnum::TELEPHARMACY_ADMIN]
        );

        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employeeWithPermissionTelepharmacy, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employeeWithPermissionTelepharmacyAdmin, $pharmacy));

        $pharmacy->update(['uses_telepharmacy' => true]);
        $pharmacy->refresh();

        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employeeWithPermissionTelepharmacy, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employeeWithPermissionTelepharmacyAdmin, $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, CalendarStripeProduct::make());
        $pharmacy->refresh();

        $this->assertTrue($pharmacyPolicy->accessTelepharmacyAsAdmin($owner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessTelepharmacyAsAdmin($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employee, $pharmacy));
        $this->assertFalse($pharmacyPolicy->accessTelepharmacyAsAdmin($employeeWithPermissionTelepharmacy, $pharmacy));
        $this->assertTrue($pharmacyPolicy->accessTelepharmacyAsAdmin($employeeWithPermissionTelepharmacyAdmin, $pharmacy));
    }

    public function test_activate_telepharmacy(): void
    {
        $pharmacyPolicy = new PharmacyPolicy;
        [$subOwner, $pharmacy, $owner] = $this->createPharmacyUserWithRole(PharmacyRoleEnum::SUB_OWNER);
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);

        $this->assertFalse($pharmacyPolicy->activateTelepharmacy($owner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->activateTelepharmacy($subOwner, $pharmacy));
        $this->assertFalse($pharmacyPolicy->activateTelepharmacy($employee, $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, CalendarStripeProduct::make());
        $pharmacy->refresh();

        $this->assertTrue($pharmacyPolicy->activateTelepharmacy($owner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->activateTelepharmacy($subOwner, $pharmacy));
        $this->assertTrue($pharmacyPolicy->activateTelepharmacy($employee, $pharmacy));
    }

    public function test_support_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new PharmacyPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_support_has_read_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new PharmacyPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_support_has_write_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new PharmacyPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_support_has_no_delete_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new PharmacyPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_support_has_no_restore_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new PharmacyPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }

    public function test_editor_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::EDITOR]);

        $policy = new PharmacyPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_no_general_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new PharmacyPolicy;
        $this->assertFalse($policy->before($staff));
    }

    public function test_operations_has_read_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new PharmacyPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
    }

    public function test_operations_has_write_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new PharmacyPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
    }

    public function test_operations_has_delete_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new PharmacyPolicy;
        $this->assertTrue($policy->before($staff, PermissionEnum::DELETE));
    }

    public function test_operations_has_no_restore_access()
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new PharmacyPolicy;
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
    }

    public function test_cannot_delete_when_ordered_kim_address_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        KimAddress::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => KimAddressStatus::ORDERED,
        ]);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertFalse($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_cannot_delete_when_activated_kim_address_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        KimAddress::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => KimAddressStatus::ACTIVATED,
        ]);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertFalse($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_cannot_delete_when_scheduled_kim_address_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        KimAddress::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => KimAddressStatus::SCHEDULED,
        ]);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertFalse($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_cannot_delete_when_cancellation_scheduled_kim_address_exists(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        KimAddress::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => KimAddressStatus::CANCELLATION_SCHEDULED,
        ]);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertFalse($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_can_delete_when_reserved_and_deactivated_kim_address_exists(): void
    {
        $this->markTestSkipped('AP-2489 deletion is disabled globally');
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        KimAddress::factory()->createMany([
            [
                'pharmacy_id' => $pharmacy->id,
                'status' => KimAddressStatus::RESERVED,
            ], [
                'pharmacy_id' => $pharmacy->id,
                'status' => KimAddressStatus::CANCELED,
            ],
        ]);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertTrue($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_can_delete_when_no_kim_address_exists(): void
    {
        $this->markTestSkipped('AP-2489 deletion is disabled globally');
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertTrue($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_can_delete_when_cardlink_is_reserved(): void
    {
        $this->markTestSkipped('AP-2489 deletion is disabled globally');
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createCardLinkOrder($pharmacy, $user, [
            'status' => CardLinkOrderStatusEnum::Reserved->value,
            'ordered_at' => now(),
            'order_information' => [
                'package' => CardLinkPackageEnum::Unlimited->value,
                'show_in_apoguide' => 1,
                'activate_apo_guide_vendor' => 1,
            ],
        ]);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertTrue($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_can_not_delete_when_cardlink_is_ordered(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createCardLinkOrder($pharmacy, $user, [
            'status' => CardLinkOrderStatusEnum::Ordered->value,
            'ordered_at' => now(),
            'order_information' => [
                'package' => CardLinkPackageEnum::Unlimited->value,
                'show_in_apoguide' => 1,
                'activate_apo_guide_vendor' => 1,
            ],
        ]);

        $pharmacyPolicy = new PharmacyPolicy;
        $this->assertFalse($pharmacyPolicy->delete($user, $pharmacy));
    }

    public function test_only_correct_users_can_access_retax(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->acceptTermsForPharmacy($pharmacy);

        $user->pharmacyProfile()->update(['association_id' => $this->getAssociationWithContract(AssociationFrameworkContractEnum::PlusAssociationFrameworkContract)->id]);

        $this->assertFalse($user->can('index-retax', $pharmacy));

        app(RetaxSettings::class)->use_latest_paths = ['all'];
        $this->assertTrue($user->can('index-retax', $pharmacy));

        app(RetaxSettings::class)->use_latest_paths = [$user->id];
        $this->assertTrue($user->can('index-retax', $pharmacy));

        app(RetaxSettings::class)->use_latest_paths = ['fut'];
        $this->assertFalse($user->can('index-retax', $pharmacy));

        $user->update(['is_beta_tester' => true]);
        $user->fresh();
        $this->assertTrue($user->can('index-retax', $pharmacy));

        $user->pharmacyProfile()->update(['association_id' => $this->getAssociationWithContract(AssociationFrameworkContractEnum::BaseAssociationFrameworkContract)->id]);

        $pharmacy->refresh();
        $this->assertFalse($user->can('index-retax', $pharmacy));

        $user->pharmacyProfile()->update(['association_id' => null]);
        $this->assertFalse($user->can('index-retax', $pharmacy));
    }
}
