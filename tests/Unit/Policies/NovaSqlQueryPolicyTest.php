<?php

namespace Tests\Unit\Policies;

use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\NovaSqlQuery;
use App\Policies\NovaSqlQueryPolicy;
use App\Staff;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/** @group AP-1730 */
class NovaSqlQueryPolicyTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::ADMIN]);

        $policy = new NovaSqlQueryPolicy;
        $this->assertFalse($policy->before($staff));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
        $this->assertTrue($policy->before($staff, PermissionEnum::CREATE));
        $this->assertTrue($policy->before($staff, PermissionEnum::UPDATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
        $this->assertTrue($policy->before($staff, 'runAction'));
    }

    public function test_editor(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::EDITOR]);

        $policy = new NovaSqlQueryPolicy;
        $this->assertFalse($policy->before($staff));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW));
        $this->assertTrue($policy->before($staff, PermissionEnum::VIEW_ANY));
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
        $this->assertTrue($policy->before($staff, 'runAction'));
    }

    public function test_support(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::SUPPORT]);

        $policy = new NovaSqlQueryPolicy;
        $this->assertFalse($policy->before($staff));
        $this->assertFalse($policy->before($staff, PermissionEnum::VIEW));
        $this->assertFalse($policy->before($staff, PermissionEnum::VIEW_ANY));
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
        $this->assertFalse($policy->before($staff, 'runAction'));
    }

    public function test_operations(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new NovaSqlQueryPolicy;
        $this->assertFalse($policy->before($staff));
        $this->assertFalse($policy->before($staff, PermissionEnum::VIEW));
        $this->assertFalse($policy->before($staff, PermissionEnum::VIEW_ANY));
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
        $this->assertFalse($policy->before($staff, 'runAction'));
    }

    public function test_analyst(): void
    {
        $staff = Staff::factory()->create(['role' => StaffRoleEnum::OPERATIONS]);

        $policy = new NovaSqlQueryPolicy;
        $this->assertFalse($policy->before($staff));
        $this->assertFalse($policy->before($staff, PermissionEnum::VIEW));
        $this->assertFalse($policy->before($staff, PermissionEnum::VIEW_ANY));
        $this->assertFalse($policy->before($staff, PermissionEnum::CREATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::UPDATE));
        $this->assertFalse($policy->before($staff, PermissionEnum::DELETE));
        $this->assertFalse($policy->before($staff, PermissionEnum::RESTORE));
        $this->assertFalse($policy->before($staff, PermissionEnum::FORCE_DELETE));
        $this->assertFalse($policy->before($staff, 'runAction'));
    }

    public function test_user(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->assertFalse($user->can('view', NovaSqlQuery::class));
        $this->assertFalse($user->can('viewAny', NovaSqlQuery::class));
        $this->assertFalse($user->can('create', NovaSqlQuery::class));
        $this->assertFalse($user->can('update', NovaSqlQuery::class));
        $this->assertFalse($user->can('delete', NovaSqlQuery::class));
        $this->assertFalse($user->can('restore', NovaSqlQuery::class));
        $this->assertFalse($user->can('forceDelete', NovaSqlQuery::class));
        $this->assertFalse($user->can('runAction', NovaSqlQuery::class));

        $policy = new NovaSqlQueryPolicy;
        $this->assertNull($policy->before($user));
        $this->assertNull($policy->before($user, PermissionEnum::VIEW));
        $this->assertNull($policy->before($user, PermissionEnum::VIEW_ANY));
        $this->assertNull($policy->before($user, PermissionEnum::CREATE));
        $this->assertNull($policy->before($user, PermissionEnum::UPDATE));
        $this->assertNull($policy->before($user, PermissionEnum::DELETE));
        $this->assertNull($policy->before($user, PermissionEnum::RESTORE));
        $this->assertNull($policy->before($user, PermissionEnum::FORCE_DELETE));
        $this->assertNull($policy->before($user, 'runAction'));
    }
}
