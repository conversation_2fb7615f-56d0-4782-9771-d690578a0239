<?php

namespace Tests\Unit\Console\Commands;

use App\Association;
use App\Domains\Association\Domain\Enums\MemberImportStatus;
use App\MemberImport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RemoveFailedMemberImportsTest extends TestCase
{
    use RefreshDatabase;

    public function test_command_removes_all_failed_imports_older_than_3_months()
    {
        MemberImport::factory()->count(10)
            ->for(Association::factory())
            ->create([
                'status' => MemberImportStatus::Failed,
                'created_at' => now()->subMonthsNoOverflow(6),
            ]);

        $this->artisan('model:prune', ['--model' => MemberImport::class])->assertSuccessful();

        $this->assertDatabaseCount('member_imports', 0);
    }

    public function test_command_removes_only_failed_imports_older_than_3_months()
    {
        MemberImport::factory()->count(5)
            ->for(Association::factory())
            ->create([
                'status' => MemberImportStatus::Failed,
                'created_at' => now()->subMonthsNoOverflow(6),
            ]);

        MemberImport::factory()->count(5)
            ->for(Association::factory())
            ->create([
                'status' => MemberImportStatus::Importing,
                'created_at' => now()->subMonthsNoOverflow(6),
            ]);

        $this->artisan('model:prune', ['--model' => MemberImport::class])->assertSuccessful();

        $this->assertDatabaseCount('member_imports', 5);
    }

    public function test_command_does_not_remove_imports_younger_than_3_months()
    {
        MemberImport::factory()->count(5)
            ->for(Association::factory())
            ->create([
                'status' => MemberImportStatus::Failed,
                'created_at' => now()->subMonthsNoOverflow(2),
            ]);

        MemberImport::factory()->count(5)
            ->for(Association::factory())
            ->create([
                'status' => MemberImportStatus::Pending,
                'created_at' => now()->subMonthsNoOverflow(2),
            ]);

        $this->artisan('model:prune', ['--model' => MemberImport::class])->assertSuccessful();

        $this->assertDatabaseCount('member_imports', 10);
    }
}
