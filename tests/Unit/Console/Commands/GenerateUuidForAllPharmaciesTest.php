<?php

namespace Tests\Unit\Console\Commands;

use App\Pharmacy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GenerateUuidForAllPharmaciesTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_generates_uuids()
    {
        $this->createPharmacies(3);

        Pharmacy::query()->update([
            'uuid' => null,
        ]);

        Pharmacy::factory()->create([
            'uuid' => 'abc',
        ]);

        $first = Pharmacy::first();

        $this->travel(5)->minutes();

        $this->artisan('pharmacies:generate-uuid');

        $this->assertDatabaseHas('pharmacies', ['uuid' => 'abc']);
        $this->assertDatabaseMissing('pharmacies', ['uuid' => null]);
        $this->assertEquals(Pharmacy::find($first->id)->updated_at->format('U'), $first->updated_at->format('U'));
    }
}
