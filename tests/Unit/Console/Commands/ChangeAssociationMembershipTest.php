<?php

namespace Tests\Unit\Console\Commands;

use App\Association;
use App\AssociationMembershipChange;
use App\Enums\AssociationEnum;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Enums\Settings\PharmacySettingTypes;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ChangeAssociationMembershipTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_removes_membership_from_westphalia_to_non_and_back(): void
    {
        $association = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy2 = $this->createPharmacyForUser($owner);
        $pharmacy3 = $this->createPharmacyForUser($owner);

        $owner->pharmacyProfile->association_id = null;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = null;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => null]);
        $pharmacy2->update(['association_id' => null]);
        $pharmacy3->update(['association_id' => null]);
        $pharmacy->subscribeToPlan('extended');
        $pharmacy->refresh();
        $pharmacy2->refresh();
        $pharmacy3->refresh();

        $associationMembershipChange = AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => null,
            'association_id_after' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            'mode' => AssociationMembershipChangeModeEnum::ADD,
            'change_at' => now()->subMonthsNoOverflow(2),
        ]);

        $this->assertNotSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertNotSame($association->id, $owner->brochureCode->association_id);
        $this->assertNotSame($association->id, $pharmacy->association_id);
        $this->assertNotSame($association->id, $pharmacy2->association_id);
        $this->assertNotSame($association->id, $pharmacy3->association_id);
        $this->assertSame($association->id, $associationMembershipChange->association_id_after);

        $this->assertNull($associationMembershipChange->association_id_before);

        $this->artisan('user:change-association-membership');

        $this->assertSame($association->id, $owner->refresh()->pharmacyProfile->association_id);
        $this->assertSame($association->id, $owner->brochureCode->association_id);
        $this->assertSame($association->id, $pharmacy->refresh()->association_id);
        $this->assertSame($association->id, $pharmacy2->refresh()->association_id);
        $this->assertSame($association->id, $pharmacy3->refresh()->association_id);
        $this->assertSame($association->id, $associationMembershipChange->refresh()->association_id_after);

        $this->assertNull($associationMembershipChange->association_id_before);

        $this->assertNotNull($associationMembershipChange->change_done_at);

        $this->assertFalse(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value
        );
        $this->assertFalse(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value
        );
        $this->assertCount(0, $pharmacy->activeSubscriptions);

        $associationMembershipChange = AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => null,
            'association_id_after' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            'mode' => AssociationMembershipChangeModeEnum::ADD,
            'change_at' => now()->subMonthsNoOverflow(1),
        ]);

        $this->artisan('user:change-association-membership');

        $owner->refresh();
        $pharmacy->refresh();
        $pharmacy2->refresh();
        $pharmacy3->refresh();
        $associationMembershipChange->refresh();

        $this->assertSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertSame($association->id, $owner->brochureCode->association_id);
        $this->assertSame($association->id, $pharmacy->association_id);
        $this->assertSame($association->id, $pharmacy2->association_id);
        $this->assertSame($association->id, $pharmacy3->association_id);
        $this->assertNull($associationMembershipChange->association_id_before);
        $this->assertSame($association->id, $associationMembershipChange->association_id_after);

    }

    public function test_it_changes_membership_from_brandenburg_to_westphalia(): void
    {
        $westphalia = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);
        $brandenburg = Association::find(AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = $brandenburg->id;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = $brandenburg->id;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => $brandenburg->id]);
        $pharmacy->refresh();

        $associationMembershipChange = AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => $brandenburg->id,
            'association_id_after' => $westphalia->id,
            'mode' => AssociationMembershipChangeModeEnum::CHANGE,
            'change_at' => now()->subMonthsNoOverflow(),
        ]);

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->pharmacyProfile->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $owner->brochureCode->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $pharmacy->association_id
        );
        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_BRANDENBURG_E_V,
            $associationMembershipChange->association_id_before
        );

        $this->assertSame(
            AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V,
            $associationMembershipChange->association_id_after
        );

        $this->artisan('user:change-association-membership');

        $owner->refresh();
        $pharmacy->refresh();
        $associationMembershipChange->refresh();

        $this->assertSame($westphalia->id, $owner->pharmacyProfile->association_id);
        $this->assertSame($westphalia->id, $owner->brochureCode->association_id);
        $this->assertSame($westphalia->id, $pharmacy->association_id);
        $this->assertSame($westphalia->id, $associationMembershipChange->association_id_after);

        $this->assertSame($brandenburg->id, $associationMembershipChange->association_id_before);

        $this->assertNotNull($associationMembershipChange->change_done_at);

        $this->assertFalse(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::TERMS_OF_USE)->value
        );
        $this->assertFalse(
            (bool) $pharmacy->getGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)->value
        );
        $this->assertCount(0, $pharmacy->activeSubscriptions);
    }

    public function test_it_removes_membership_from_deleted_user(): void
    {
        $association = Association::find(AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V);
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $owner->pharmacyProfile->association_id = AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V;
        $owner->pharmacyProfile->save();
        $owner->brochureCode->association_id = AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V;
        $owner->brochureCode->save();
        $owner->refresh();

        $pharmacy->update(['association_id' => AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V]);
        $pharmacy->subscribeToPlan('extended');
        $pharmacy->refresh();

        $this->assertSame($association->id, $owner->pharmacyProfile->association_id);
        $this->assertSame($association->id, $owner->brochureCode->association_id);
        $this->assertSame($association->id, $pharmacy->association_id);

        AssociationMembershipChange::create([
            'user_id' => $owner->id,
            'association_id_before' => $association->id,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now()->subMonthsNoOverflow(),
        ]);

        $owner->deleted_at = now();
        $owner->save();

        $this->artisan('user:change-association-membership');

        $pharmacy->refresh();
        $owner->refresh();

        $this->assertNull($pharmacy->association_id);
        $this->assertEmpty($pharmacy->activeSubscriptions);
        $this->assertNull($owner->pharmacyProfile->association_id);
        $this->assertNull($owner->brochureCode->association_id);
    }
}
