<?php

namespace Tests\Unit\Http\Controller;

use Illuminate\Foundation\Testing\RefreshDatabase;
use OpenIDConnectClient\AccessToken;
use Tests\TestCase;

class UserControllerTest extends TestCase
{
    use RefreshDatabase;

    private const ID_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************.0Q_NCJzfbBzhcwXkcDkwNfjEr3pvENC7zPkmK-IDEGQ';

    private const DEFAULT_ARGUMENTS = [
        'access_token' => 'some access token',
        'resource_owner_id' => 'some resource_owner_id',
        'refresh_token' => 'some refresh_token',
        'expires_in' => 123,
        'id_token' => self::ID_TOKEN,
        'random_key_123' => 'some random value',
    ];

    public function test_a_user_confirmed_or_declined_the_changed_idp_email_address_usage(): void
    {
        $token = new AccessToken(self::DEFAULT_ARGUMENTS);

        [$user] = $this->createPharmacyUserWithPharmacy();

        $request = [
            'yes' => true,
        ];

        $this->actingAs($user)
            ->withSession(['oidc-auth.access_token' => $token])
            ->post(route('users.check-for-idp-email-change.set-address'), $request)
            ->assertRedirectToRoute('dashboard');

        $user->fresh();

        $this->assertSame('<EMAIL>', $user->email);
        $this->assertSame('<EMAIL>', $user->idp_email);

        [$user] = $this->createPharmacyUserWithPharmacy();

        $request = [
            'no' => true,
        ];

        $this->actingAs($user)
            ->withSession(['oidc-auth.access_token' => $token])
            ->post(route('users.check-for-idp-email-change.set-address'), $request)
            ->assertRedirectToRoute('dashboard');

        $user->fresh();

        $this->assertNotSame('<EMAIL>', $user->email);
        $this->assertSame('<EMAIL>', $user->idp_email);

    }
}
