<?php

namespace Tests\Unit\Http\Controller\Api;

use App\CalendarTopic;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\helper\ApiHelper;
use Tests\helper\ResourceTestHelper;
use Tests\TestCase;

class ApomondoTopicControllerTest extends TestCase
{
    use ApiHelper, RefreshDatabase, ResourceTestHelper;

    /** @test */
    public function it_can_fetch_all_apomondo_topics()
    {
        $count = CalendarTopic::count();

        $this
            ->json('GET', route('api.apomondo-topics.index'))
            ->assertUnauthorized();

        $this->actingAsClientWithScopes(['apomondo-topics']);

        $this->json('GET', route('api.apomondo-topics.index'))->assertJsonCount($count, 'data');

        $this->actingAsClientWithScopes(['languages']);

        $this
            ->json('GET', route('api.apomondo-topics.index'))
            ->assertStatus(403);
    }
}
