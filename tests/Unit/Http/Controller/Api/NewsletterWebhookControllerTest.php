<?php

namespace Tests\Unit\Http\Controller\Api;

use App\Enums\Newsletter\MailcoachList;
use App\Enums\Newsletter\WebhookEvent;
use App\Http\Integrations\Mailcoach\Requests\DeleteSubscriberRequest;
use App\Integrations\MailcoachIntegration;
use App\Settings\MailcoachSettings;
use Illuminate\Support\Str;
use Saloon\Http\Faking\MockResponse;
use Saloon\Laravel\Facades\Saloon;
use Tests\TestCase;

class NewsletterWebhookControllerTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        app(MailcoachSettings::class)->advertisingMailingListId = Str::uuid()->toString();
        app(MailcoachSettings::class)->systemMailingListId = Str::uuid()->toString();
    }

    public function test_it_returns_403_if_signature_is_missing(): void
    {
        $payload = [
            'event' => 'SubscribedEvent',
            'email_list_uuid' => app(MailcoachSettings::class)->advertisingMailingListId,
            'email' => '<EMAIL>',
        ];

        $this
            ->postJson(route('api.newsletter.webhook'), $payload)
            ->assertForbidden();
    }

    public function test_it_returns_403_if_signature_is_invalid(): void
    {
        $user = $this->createPharmacyUser([
            'email' => '<EMAIL>',
        ]);

        $user->setIntegration(new MailcoachIntegration(advertisingListId: 'test'));

        $payload = [
            'event' => 'SubscribedEvent',
            'email_list_uuid' => app(MailcoachSettings::class)->advertisingMailingListId,
            'email' => $user->email,
        ];

        $this
            ->withHeaders([
                'Signature' => 'invalid-signature',
            ])
            ->postJson(route('api.newsletter.webhook'), $payload)
            ->assertForbidden();

        $this->assertTrue($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
    }

    public function test_it_rejects_subscribed_events(): void
    {
        $user = $this->createPharmacyUser([
            'email' => '<EMAIL>',
        ]);

        $user->setIntegration(new MailcoachIntegration(advertisingListId: 'test'));

        $payload = [
            'event' => 'SubscribedEvent',
            'email_list_uuid' => app(MailcoachSettings::class)->advertisingMailingListId,
            'email' => $user->email,
        ];

        $key = hash_hmac('sha256', json_encode($payload), MailcoachSettings::webhookSecret());

        $this
            ->withHeaders([
                'Signature' => $key,
            ])
            ->postJson(route('api.newsletter.webhook'), $payload)
            ->assertUnprocessable();

        $this->assertTrue($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
    }

    public function test_it_returns_404_if_mailcoach_id_is_null_on_unsubscribed_events(): void
    {
        $user = $this->createPharmacyUser([
            'email' => '<EMAIL>',
        ]);

        $payload = [
            'event' => WebhookEvent::Unsubscribed->value,
            'email_list_uuid' => app(MailcoachSettings::class)->advertisingMailingListId,
            'email' => $user->email,
        ];

        $key = hash_hmac('sha256', json_encode($payload), MailcoachSettings::webhookSecret());

        $this
            ->withHeaders([
                'Signature' => $key,
            ])
            ->postJson(route('api.newsletter.webhook'), $payload)
            ->assertNotFound();
    }

    public function test_it_returns_404_if_email_is_not_equal_on_unsubscribed_events(): void
    {
        $this->createPharmacyUser([
            'email' => '<EMAIL>',
        ]);

        $payload = [
            'event' => WebhookEvent::Unsubscribed->value,
            'email_list_uuid' => app(MailcoachSettings::class)->advertisingMailingListId,
            'email' => '<EMAIL>',
        ];

        $key = hash_hmac('sha256', json_encode($payload), MailcoachSettings::webhookSecret());

        $this
            ->withHeaders([
                'Signature' => $key,
            ])
            ->postJson(route('api.newsletter.webhook'), $payload)
            ->assertNotFound();
    }

    public function test_it_processes_unsubscribed_events(): void
    {
        Saloon::fake([
            DeleteSubscriberRequest::class => MockResponse::make(),
        ]);

        $user = $this->createPharmacyUser([
            'email' => '<EMAIL>',
        ]);

        $user->setIntegration(new MailcoachIntegration(advertisingListId: 'test'));

        $this->assertTrue($user->hasMailcoachSubscriptions(MailcoachList::Advertising));

        $payload = [
            'event' => WebhookEvent::Unsubscribed->value,
            'email_list_uuid' => app(MailcoachSettings::class)->advertisingMailingListId,
            'email' => $user->email,
        ];

        $key = hash_hmac('sha256', json_encode($payload), MailcoachSettings::webhookSecret());

        $this
            ->withHeaders([
                'Signature' => $key,
            ])
            ->postJson(route('api.newsletter.webhook'), $payload)
            ->assertOk();

        $this->assertFalse($user->hasMailcoachSubscriptions(MailcoachList::Advertising));
    }
}
