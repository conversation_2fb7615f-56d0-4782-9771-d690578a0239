<?php

namespace Tests\Unit\Http\Controller\PharmacyInfluenzaVaccination;

use App\Enums\PharmaceuticalsTypeEnum;
use App\Pharmaceutical;
use App\Vaccination;
use Tests\TestCase;

class PersonalDataControllerTest extends TestCase
{
    public function test_shows_all_pharmaceutials_for_bottleneck_vaccination(): void
    {
        $pharmaceutials = Pharmaceutical::query()
            ->where('active', true)
            ->where('type', [PharmaceuticalsTypeEnum::VACCINE_INFLUENZA, PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE]);

        $vaccination = Vaccination::factory()->create();
        $vaccination->influenzaVaccination()->create([
            'use_high_dose_vaccine' => false,
            'high_dose_vaccine_bottleneck' => true,
        ]);

        // Assert
        $this->assertSame($pharmaceutials->count(), Pharmaceutical::getPharmaceuticalsForVaccination($vaccination)->count());
        $this->assertEquals($pharmaceutials->get(), Pharmaceutical::getPharmaceuticalsForVaccination($vaccination));
    }

    public function test_shows_only_high_dose_pharmaceutials_for_high_dose_vaccination(): void
    {
        $pharmaceutials = Pharmaceutical::query()
            ->where('active', true)
            ->where('type', [PharmaceuticalsTypeEnum::VACCINE_INFLUENZA_HIGH_DOSE]);

        $vaccination = Vaccination::factory()->create();
        $vaccination->influenzaVaccination()->create([
            'use_high_dose_vaccine' => true,
            'high_dose_vaccine_bottleneck' => false,
        ]);

        // Assert
        $this->assertSame($pharmaceutials->count(), Pharmaceutical::getPharmaceuticalsForVaccination($vaccination)->count());
        $this->assertEquals($pharmaceutials->get(), Pharmaceutical::getPharmaceuticalsForVaccination($vaccination));
    }

    public function test_shows_only_normal_dose_pharmaceutials_for_normal_dose_vaccination(): void
    {
        $pharmaceutials = Pharmaceutical::query()
            ->where('active', true)
            ->where('type', [PharmaceuticalsTypeEnum::VACCINE_INFLUENZA]);

        $vaccination = Vaccination::factory()->create();
        $vaccination->influenzaVaccination()->create([
            'use_high_dose_vaccine' => false,
            'high_dose_vaccine_bottleneck' => false,
        ]);

        // Assert
        $this->assertSame($pharmaceutials->count(), Pharmaceutical::getPharmaceuticalsForVaccination($vaccination)->count());
        $this->assertEquals($pharmaceutials->get(), Pharmaceutical::getPharmaceuticalsForVaccination($vaccination));
    }
}
