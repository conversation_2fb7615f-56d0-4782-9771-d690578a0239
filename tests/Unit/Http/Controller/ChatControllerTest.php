<?php

namespace Tests\Unit\Http\Controller;

use App\Association;
use App\Domains\Subscription\Application\StripeProducts\AddOns\TIMStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\AssociationRoleEnum;
use App\UserAssociationProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ChatControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_a_user_cannot_access_the_chat(): void
    {
        $user = $this->createPharmacyUserWithPharmacy()[0];

        $statusCode = $this->actingAs($user)
            ->get(route('pharmacies.chat'))
            ->getStatusCode();

        $this->assertNotSame(200, $statusCode);
    }

    public function test_an_owner_cannot_access_the_chat_without_subscription_if_needed(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $statusCode = $this->actingAs($user)
            ->get(route('pharmacies.chat'))
            ->getStatusCode();

        $this->assertNotSame(200, $statusCode);
    }

    public function test_an_owner_cannot_access_the_chat_without_accepted_terms(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        if (! $pharmacy->hasSubscribedIfNeeded()) {
            $pharmacy->subscribeToPlan('base');
        }

        $statusCode = $this->actingAs($user)
            ->get(route('pharmacies.chat'))
            ->getStatusCode();

        $this->assertNotSame(200, $statusCode);
    }

    public function test_a_user_cannot_access_the_chat_when_no_pharmacy(): void
    {
        $user = $this->createPharmacyUser();

        $this->actingAs($user)
            ->get(route('pharmacies.chat'))
            ->assertForbidden();
    }

    public function test_all_owners_get_redirected_to_enable_the_chat(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, [BaseStripeProduct::make(), TIMStripeProduct::make()]);

        $this->actingAs($user)
            ->get(route('pharmacies.chat'))
            ->assertRedirect(route('pharmacies.edit', ['pharmacy' => $pharmacy]).'?#activate_chat');
    }

    public function test_all_owners_can_access_the_chat(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, [BaseStripeProduct::make(), TIMStripeProduct::make()]);

        Http::fake(fn () => Http::response([
            'alreadyExists' => [
                $user->uuid,
            ],
        ]));

        $pharmacy->update([
            'uses_chat' => true,
        ]);

        $this->actingAs($user)
            ->get(route('pharmacies.chat'))
            ->assertStatus(200);
    }

    public function test_a_user_with_association_can_access_the_chat(): void
    {
        $association = Association::factory()->create();
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        Http::fake(static fn () => Http::response([
            'alreadyExists' => [
                $user->uuid,
            ],
        ]));

        $pharmacy->update([
            'uses_chat' => true,
        ]);

        UserAssociationProfile::create([
            'user_id' => $user->id,
        ]);
        $user->pharmacyProfile->update([
            'association_id' => $association->id,
            'permissions' => [],
        ]);
        $association->assignUser($user, AssociationRoleEnum::ADMIN);

        $this->createLocalStripeSubscription($pharmacy, TIMStripeProduct::make());

        $this->actingAs($user)
            ->get(route('pharmacies.chat'))
            ->assertStatus(200);
    }

    public function test_webchat_app_proxy_works(): void
    {
        Storage::fake('webchat')->put('stable/index.html', '<base href="/">');

        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->actingAs($user)
            ->get(route('messenger.app'))
            ->assertOk()
            ->assertSee('<base href="/messenger/app/">', false);
    }

    public function test_webchat_app_proxy_caches_for_one_day(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        Carbon::setTestNow('2024-10-10 10:08:00');
        Storage::fake('webchat')->put('stable/index.html', '<base href="/">');

        $this->actingAs($user)
            ->get(route('messenger.app'))
            ->assertOk()
            ->assertSee('<base href="/messenger/app/">', false);

        Carbon::setTestNow('2024-10-10 10:38:00');
        Storage::fake('webchat')->put('stable/index.html', '<chat />');

        $this->actingAs($user)
            ->get(route('messenger.app'))
            ->assertOk()
            ->assertSee('<base href="/messenger/app/">', false)
            ->assertDontSee('<chat />', false);

        Carbon::setTestNow('2024-10-11 11:18:00');

        $this->actingAs($user)
            ->get(route('messenger.app'))
            ->assertOk()
            ->assertSee('<chat />', false)
            ->assertDontSee('<base href="/messenger/app/">', false);
    }

    public function test_webchat_app_proxy_caches_for_one_hour_grouped(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        Carbon::setTestNow('2024-10-11 13:00:00');
        Storage::persistentFake('webchat')->put('stable/index.html', '<base href="/">');
        Storage::persistentFake('webchat')->put('stable/app.js', 'import { app } from "mein-apothekenportal.de"');

        $this->actingAs($user)
            ->get(route('messenger.app'))
            ->assertOk()
            ->assertSee('<base href="/messenger/app/">', false);

        Carbon::setTestNow('2024-10-11 13:30:00');

        $this->actingAs($user)
            ->get(route('messenger.app'))
            ->assertOk()
            ->assertSee('<base href="/messenger/app/">', false);

        $this->actingAs($user)
            ->get(route('messenger.app', 'app.js'))
            ->assertOk()
            ->assertSee('import { app } from "mein-apothekenportal.de"', false);

        Storage::persistentFake('webchat')->put('stable/index.html', '<chat />');
        Storage::persistentFake('webchat')->put('stable/app.js', 'import { app, fun } from "mein-apothekenportal.de"');

        Carbon::setTestNow('2024-10-12 15:10:00');

        $this->actingAs($user)
            ->get(route('messenger.app'))
            ->assertOk()
            ->assertSee('<chat />', false);

        $this->actingAs($user)
            ->get(route('messenger.app', 'app.js'))
            ->assertOk()
            ->assertSee('import { app, fun } from "mein-apothekenportal.de"', false);
    }
}
