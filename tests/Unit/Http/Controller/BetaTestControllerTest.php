<?php

namespace Tests\Unit\Http\Controller;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BetaTestControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_the_request_to_register_route_is_successful(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->actingAs($user)
            ->get(route('beta-test.registration'))
            ->assertStatus(200);
    }

    public function test_the_user_can_become_a_beta_tester(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();

        $this->assertFalse((bool) $user->is_beta_tester);

        $this->actingAs($user)
            ->post(route('beta-test.store-registration'))
            ->assertRedirect(route('dashboard'));

        $this->assertTrue($user->is_beta_tester);
    }

    public function test_the_request_to_show_route_is_successful(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();
        $user->update(['is_beta_tester' => true]);

        $this->actingAs($user)
            ->get(route('beta-test.show'))
            ->assertStatus(200);
    }

    public function test_the_user_can_sign_off_as_beta_tester(): void
    {
        [$user] = $this->createPharmacyUserWithPharmacy();
        $user->update(['is_beta_tester' => true]);

        $this->assertTrue($user->is_beta_tester);

        $this->actingAs($user)
            ->post(route('beta-test.sign-off'))
            ->assertRedirect(route('dashboard'));

        $this->assertFalse($user->is_beta_tester);
    }
}
