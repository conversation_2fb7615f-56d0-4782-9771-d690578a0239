<?php

namespace Tests\Unit\Http\Middleware;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\PharmacyRoleEnum;
use App\Enums\Settings\PharmacySettingTypes;
use App\Http\Middleware\EnsureTermsOfUseAreAccepted;
use App\Livewire\Terms\TermsDeadlineWarning;
use App\Setting;
use App\Settings\AppSettings;
use App\Settings\TermsOfServiceSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route;
use Tests\TestCase;

class EnsureTermsOfUseAreAcceptedTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Route::get('/dummy-test-route', static fn () => 'nice')
            ->middleware([EnsureTermsOfUseAreAccepted::class])
            ->name('dummy-test-route');

        $this->travelTo(app(AppSettings::class)->terms_of_use_deadline);
    }

    public function test_guest_user_can_access_route()
    {
        $response = $this->get('/dummy-test-route');

        $response->assertOk();
        $response->assertSee('nice');
    }

    public function test_user_with_pharmacy_that_accepted_terms_and_processing_contract_can_access_route()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->addDay());
        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);
        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');

        $response->assertOk();
        $response->assertSee('nice');
    }

    public function test_user_without_accepted_terms_is_redirected()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, false);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');

        $response->assertRedirect(route('pharmacies.termsOfUse', currentPharmacy()));
    }

    public function test_user_without_accepted_processing_contract_is_redirected()
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, false);

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');

        $response->assertRedirect(route('pharmacies.termsOfUse', currentPharmacy()));
    }

    public function test_association_user_can_access_route()
    {
        [$user] = $this->createAssociationUser();

        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');

        $response->assertOk();
        $response->assertSee('nice');
    }

    public function test_user_with_formerly_accepted_tos_before_terms_of_use_deadline_can_access_route()
    {
        $this->travelBack();
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');

        $response->assertOk();
        $response->assertSee('nice');
    }

    public function test_user_with_formerly_rejected_tos_before_terms_of_use_deadline_is_redirected()
    {
        $this->travelBack();
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->generalSettings()->forceDelete();

        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, false);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, false);
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->assertSame(2, Setting::count());

        $this->artisan('pharmacy:set-all-terms-of-service-to-not-accepted')
            ->assertExitCode(0);

        $this->assertSame(0, Setting::count());
        $this->assertSame(2, Setting::withoutGlobalScopes()->count());

        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');

        $response->assertRedirect(route('pharmacies.termsOfUse', $pharmacy));
    }

    public function test_reject_tos_will_create_a_new_entry_and_delete_the_old_one()
    {
        $this->travelBack();
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->assertSame(2, Setting::withoutGlobalScopes()->count());
        $this->assertSame(
            PharmacySettingTypes::TERMS_OF_USE_ACCEPTED,
            $pharmacy
                ->generalSettings()
                ->withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::TERMS_OF_USE)
                ->latest()
                ->first()
                ->value
        );
        $this->assertSame(
            PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED,
            $pharmacy
                ->generalSettings()
                ->withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)
                ->latest()
                ->first()
                ->value
        );

        // because latest() will order by created_at
        sleep(1);

        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, false);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, false);

        $this->assertSame(2, Setting::count());
        $this->assertSame(4, Setting::withoutGlobalScopes()->count());

        $this->assertSame(
            '0',
            $pharmacy
                ->generalSettings()
                ->withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::TERMS_OF_USE)
                ->latest()
                ->first()
                ->value
        );
        $this->assertSame(
            '0',
            $pharmacy
                ->generalSettings()
                ->withoutGlobalScopes()
                ->where('type', PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT)
                ->latest()
                ->first()
                ->value
        );
    }

    public function test_owner_when_stripe_enabled(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay());
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);
        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');
        $response->assertRedirect(route('pharmacies.subscription', $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());
        $response = $this->get('/dummy-test-route');
        $response->assertRedirect(route('pharmacies.termsOfUse', $pharmacy));

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());
        $response = $this->get('/dummy-test-route');
        $response->assertRedirect(route('pharmacies.termsOfUse', $pharmacy));

        $pharmacy->generalSettings()->forceDelete();
        $pharmacy->acceptTerms(true);

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());
        $response = $this->get('/dummy-test-route');
        $response->assertOk();

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());
        $response = $this->get('/dummy-test-route');
        $response->assertOk();
    }

    public function test_subowner_when_stripe_enabled(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay());
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);
        $user = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::SUB_OWNER);
        $this->actingAs($user);

        $response = $this->get('/dummy-test-route');
        $response->assertRedirect(route('pharmacies.subscription', $pharmacy));

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());
        $response = $this->get('/dummy-test-route');
        $response->assertRedirect(route('pharmacies.termsOfUse', $pharmacy));

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());
        $response = $this->get('/dummy-test-route');
        $response->assertRedirect(route('pharmacies.termsOfUse', $pharmacy));

        $this->acceptTermsForPharmacy($pharmacy);

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());
        $response = $this->get('/dummy-test-route');
        $response->assertOk();

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());
        $response = $this->get('/dummy-test-route');
        $response->assertOk();
    }

    public function test_employee_when_stripe_enabled(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay());
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, true);
        $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, true);
        $user = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $this->actingAs($user);

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());
        session()->remove(TermsDeadlineWarning::$key);
        $response = $this->get('/dummy-test-route');
        $response->assertRedirectToRoute('terms-deadline-warning');

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());
        session()->remove(TermsDeadlineWarning::$key);
        $response = $this->get('/dummy-test-route');
        $response->assertRedirect(route('pharmacies.termsOfUseDeadline', $pharmacy));

        $this->acceptTermsForPharmacy($pharmacy);

        session()->remove(TermsDeadlineWarning::$key);
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());
        $response = $this->get('/dummy-test-route');
        $response->assertOk();

        session()->remove(TermsDeadlineWarning::$key);
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());
        $response = $this->get('/dummy-test-route');
        $response->assertOk();
    }

    public function test_user_gets_redirected_to_terms_deadline_warning_if_pharmacy_did_not_accept_new_terms(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay());

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $user = $this->createPharmacyEmployee($pharmacy);

        $pharmacy->acceptTerms();

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());

        session()->remove(TermsDeadlineWarning::$key);
        $this->actingAs($user)
            ->get('dashboard')
            ->assertRedirectToRoute('terms-deadline-warning');
    }

    public function test_user_can_use_if_one_pharmacy_accepted_terms(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->addDay());

        [$owner1, $pharmacy1] = $this->createPharmacyUserWithPharmacy();
        $user = $this->createPharmacyEmployee($pharmacy1);

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());

        session()->remove(TermsDeadlineWarning::$key);
        $this->actingAs($user)
            ->get('dashboard')
            ->assertOk();
    }

    public function test_owner_when_another_pharmacy_accepted_old_terms(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->addDay());
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        $pharmacy2 = $this->createPharmacyForUser($owner);
        $pharmacy2->generalSettings()->update(['created_at' => app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay()]);

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());

        // pharmacies are ordered by name and the first one is auto selected in the PharmacySessionHelper
        $pharmacy->update(['name' => 'A']);
        $pharmacy2->update(['name' => 'B']);
        $this->actingAs($owner)
            ->get('dashboard')
            ->assertOk();
    }

    public function test_user_does_not_get_redirected_to_terms_deadline_warning_if_pharmacy_accepted_new_terms(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay());

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $user = $this->createPharmacyEmployee($pharmacy);

        $pharmacy->acceptTerms();

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());

        $pharmacy->acceptTerms();

        session()->remove(TermsDeadlineWarning::$key);
        $this->actingAs($user)
            ->get('dashboard')
            ->assertOk();
    }

    public function test_user_does_not_get_redirected_to_terms_deadline_warning_if_user_ignored_warning(): void
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_active_since->clone()->subDay());

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $user = $this->createPharmacyEmployee($pharmacy);

        $pharmacy->acceptTerms();

        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->subDay());

        $pharmacy->acceptTerms();

        $this->actingAs($user)
            ->session([TermsDeadlineWarning::$key => true])
            ->get('dashboard')
            ->assertOk();
    }

    public function test_employee_gets_redirected_to_info_page_if_new_tos_are_not_accepted_after_deadline()
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $user = $this->createPharmacyEmployee($pharmacy);

        $this->declineTermsForPharmacy($pharmacy);

        $this->actingAs($user)
            ->get('dashboard')
            ->assertRedirectToRoute('pharmacies.termsOfUseDeadline', $pharmacy);
    }

    public function test_owner_gets_redirected_to_subscription_if_new_tos_are_not_accepted_after_deadline()
    {
        $this->travelTo(app(TermsOfServiceSettings::class)->new_terms_of_service_deadline->clone()->addDay());

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $this->declineTermsForPharmacy($pharmacy);

        $this->actingAs($owner)
            ->get('dashboard')
            ->assertRedirectToRoute('pharmacies.subscription', $pharmacy);
    }
}
