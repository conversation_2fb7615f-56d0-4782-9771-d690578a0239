<?php

namespace Tests\Unit\Http\Middleware;

use App\Http\Middleware\CheckForMaintenanceMode;
use App\PharmacyImage;
use Illuminate\Contracts\Foundation\MaintenanceMode;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Mockery;
use Tests\TestCase;

class CheckForMaintenanceModeTest extends TestCase
{
    private $maintenanceModeMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->maintenanceModeMock = Mockery::mock(MaintenanceMode::class);

        App::instance(MaintenanceMode::class, $this->maintenanceModeMock);

        $this->maintenanceModeMock->allows('active')->andReturns(false)->byDefault();
    }

    public function test_can_enable_maintenance_mode(): void
    {
        $this->maintenanceModeMock->expects('active')->andReturns(false);
        $this->assertFalse(app()->isDownForMaintenance());

        $this->maintenanceModeMock->expects('active')->andReturns(true);
        $this->assertTrue(app()->isDownForMaintenance());
    }

    public function test_routes_exist_and_are_reachable_in_maintenance_mode(): void
    {
        $excludedPaths = app(CheckForMaintenanceMode::class)->getExcludedPaths();
        $this->assertIsArray($excludedPaths);
        $this->assertNotEmpty($excludedPaths);

        // Test routes when maintenance mode is off
        $this->maintenanceModeMock->expects('active')->andReturns(false);
        $this->assertFalse(app()->isDownForMaintenance());

        foreach ($excludedPaths as $path) {
            $path = $this->prepareExceptionalPath($path);

            $this->assertNotSame(503, $this->get($path)->status());
            $this->assertNotSame(404, $this->get(url($path))->status(), 'did you change the route definition for '.$path.'?');
        }

        $this->maintenanceModeMock->expects('active')->andReturns(true);
        $this->assertTrue(app()->isDownForMaintenance());

        foreach ($excludedPaths as $path) {
            $this->assertNotSame(503, $this->get($path)->status());
        }
    }

    private function prepareExceptionalPath(string $path): string
    {
        if ($path === 'public/pharmacy/*') {
            $image = PharmacyImage::factory()->create();

            return Str::replace('*', $image->id, $path);
        }

        return $path;
    }
}
