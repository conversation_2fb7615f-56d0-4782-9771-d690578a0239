<?php

namespace Processes\Tasks\ProcessAssociationMembership;

use App\AssociationMembershipChange;
use App\Domains\Subscription\Domain\Actions\Subscription\RemoveDiscountFromSubscription;
use App\Domains\Subscription\Domain\Actions\Subscription\SendOneTimeInvoiceForNotProRatedProducts;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Processes\Payloads\ProcessAssociationMembershipChangePayload;
use App\Processes\Tasks\ProcessAssociationMembershipChange\ChangeSubscriptionDeleteMode;
use Illuminate\Support\Carbon;
use Tests\TestCase;

class ChangeSubscriptionDeleteModeTest extends TestCase
{
    public function test_needs_one_time_invoice_when_change_at_is_today_and_not_end_of_quarter(): void
    {
        if (now()->endOfQuarter()->isToday()) {
            $this->travelTo(now()->subDay());
        }
        $changeAt = now();
        $payload = new ProcessAssociationMembershipChangePayload(
            AssociationMembershipChange::factory()->create(
                [
                    'mode' => AssociationMembershipChangeModeEnum::DELETE,
                    'change_at' => $changeAt,
                ]
            )
        );

        $result = self::callProtectedMethod(app(ChangeSubscriptionDeleteMode::class), 'needsOneTimeInvoice', [
            $payload,
            static fn (ProcessAssociationMembershipChangePayload $payload) => $payload]
        );

        $this->assertTrue($result);
    }

    public function test_does_not_need_one_time_invoice_when_change_at_is_in_the_past(): void
    {
        $changeAt = now()->subDays(2);
        $payload = new ProcessAssociationMembershipChangePayload(
            AssociationMembershipChange::factory()->create(
                [
                    'mode' => AssociationMembershipChangeModeEnum::DELETE,
                    'change_at' => $changeAt,
                ]
            )
        );

        $result = self::callProtectedMethod(app(ChangeSubscriptionDeleteMode::class), 'needsOneTimeInvoice', [
            $payload,
            static fn (ProcessAssociationMembershipChangePayload $payload) => $payload]
        );

        $this->assertFalse($result);
    }

    public function test_does_not_need_one_time_invoice_when_change_at_is_end_of_quarter(): void
    {
        $this->travelTo(now()->endOfQuarter());
        $changeAt = now();
        $payload = new ProcessAssociationMembershipChangePayload(
            AssociationMembershipChange::factory()->create(
                [
                    'mode' => AssociationMembershipChangeModeEnum::DELETE,
                    'change_at' => $changeAt,
                ]
            )
        );

        $result = self::callProtectedMethod(app(ChangeSubscriptionDeleteMode::class), 'needsOneTimeInvoice', [
            $payload,
            static fn (ProcessAssociationMembershipChangePayload $payload) => $payload]
        );

        $this->assertFalse($result);
    }

    /**
     * 30.04. -> 1. Mai, weil Mai & Juni noch nicht abgerechnet wurden
     * 31.05. -> 1. Juni, weil Juni noch nicht abgerechnet wurde
     */
    public function test_calculate_one_off_invoice_start_time(): void
    {
        $this->travelTo(Carbon::create(2025, 4, 15));
        $payload = new ProcessAssociationMembershipChangePayload(
            AssociationMembershipChange::factory()->create()
        );

        $result = self::callProtectedMethod(app(ChangeSubscriptionDeleteMode::class), 'calculateOneOffInvoiceStartTime', [
            $payload]
        );

        $this->assertTrue($result->isSameDay(Carbon::create(2025, 5, 1)));

        $this->travelTo(Carbon::create(2025, 5, 15));

        $result = self::callProtectedMethod(app(ChangeSubscriptionDeleteMode::class), 'calculateOneOffInvoiceStartTime', [
            $payload]
        );

        $this->assertTrue($result->isSameDay(Carbon::create(2025, 6, 1)));
    }

    public function test_only_discount_is_removed(): void
    {
        $changeAt = now()->subMonthNoOverflow();
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy);

        $change = AssociationMembershipChange::factory()->create([
            'user_id' => $owner->id,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => $changeAt,
        ]);

        $payload = new ProcessAssociationMembershipChangePayload(
            $change
        );

        $this->mock(RemoveDiscountFromSubscription::class)
            ->expects('execute')->once();

        $this->mock(SendOneTimeInvoiceForNotProRatedProducts::class)
            ->expects('execute')->never();
        app(ChangeSubscriptionDeleteMode::class)->__invoke($payload, static fn (ProcessAssociationMembershipChangePayload $payload) => $payload);
    }

    public function test_one_time_invoice_is_sent(): void
    {
        $this->travelTo(now()->endOfQuarter()->subMonthNoOverflow());
        $changeAt = now();
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->createLocalStripeSubscription($pharmacy);

        $change = AssociationMembershipChange::factory()->create([
            'user_id' => $owner->id,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => $changeAt,
        ]);

        $payload = new ProcessAssociationMembershipChangePayload(
            $change
        );

        $this->mock(RemoveDiscountFromSubscription::class)
            ->expects('execute')->once();

        $this->mock(SendOneTimeInvoiceForNotProRatedProducts::class)
            ->expects('execute')->once();
        app(ChangeSubscriptionDeleteMode::class)->__invoke($payload, static fn (ProcessAssociationMembershipChangePayload $payload) => $payload);
    }
}
