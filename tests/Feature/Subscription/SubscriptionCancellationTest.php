<?php

namespace Tests\Feature\Subscription;

use App\Actions\Subscription\RequestSubscriptionCancellationAction;
use App\Actions\Subscription\RevokeSubscriptionCancellationAction;
use App\Domains\Subscription\Application\StripeProducts\BaseStripeProduct;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\SubscriptionCancellation;
use App\SubscriptionChangeHistory;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SubscriptionCancellationTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_request_cancellation_with_valid_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        // Simuliere eine Basismitgliedschaft
        $this->mockSubscription($pharmacy, BaseStripeProduct::class);

        $action = app(RequestSubscriptionCancellationAction::class);
        $cancellation = $action->execute($pharmacy, $user, 'Test reason');

        $this->assertInstanceOf(SubscriptionCancellation::class, $cancellation);
        $this->assertEquals(SubscriptionCancellationStatusEnum::PENDING, $cancellation->status);
        $this->assertEquals($pharmacy->id, $cancellation->pharmacy_id);
        $this->assertEquals($user->id, $cancellation->user_id);
        $this->assertEquals('Test reason', $cancellation->reason);

        // Prüfe dass Change History erstellt wurde
        $this->assertDatabaseHas('subscription_change_history', [
            'pharmacy_id' => $pharmacy->id,
            'user_id' => $user->id,
            'action' => 'cancellation_requested',
        ]);
    }

    public function test_cannot_request_cancellation_without_subscription(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $action = app(RequestSubscriptionCancellationAction::class);

        $this->expectException(SubscriptionCancellationException::class);
        $this->expectExceptionMessage('Es ist keine Basismitgliedschaft vorhanden');

        $action->execute($pharmacy, $user);
    }

    public function test_cannot_request_cancellation_with_existing_active_cancellation(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $this->mockSubscription($pharmacy, BaseStripeProduct::class);

        // Erstelle bereits eine ausstehende Kündigung
        SubscriptionCancellation::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'user_id' => $user->id,
            'status' => SubscriptionCancellationStatusEnum::PENDING,
        ]);

        $action = app(RequestSubscriptionCancellationAction::class);

        $this->expectException(SubscriptionCancellationException::class);
        $this->expectExceptionMessage('Es existiert bereits eine aktive Kündigung');

        $action->execute($pharmacy, $user);
    }

    public function test_can_revoke_pending_cancellation(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $cancellation = SubscriptionCancellation::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'user_id' => $user->id,
            'status' => SubscriptionCancellationStatusEnum::PENDING,
            'effective_date' => now()->addMonth(),
        ]);

        $action = app(RevokeSubscriptionCancellationAction::class);
        $revokedCancellation = $action->execute($pharmacy, $user, 'Changed mind');

        $this->assertEquals(SubscriptionCancellationStatusEnum::REVOKED, $revokedCancellation->status);
        $this->assertNotNull($revokedCancellation->revoked_at);

        // Prüfe dass Change History erstellt wurde
        $this->assertDatabaseHas('subscription_change_history', [
            'pharmacy_id' => $pharmacy->id,
            'user_id' => $user->id,
            'action' => 'cancellation_revoked',
        ]);
    }

    public function test_cannot_revoke_executed_cancellation(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $cancellation = SubscriptionCancellation::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'user_id' => $user->id,
            'status' => SubscriptionCancellationStatusEnum::EXECUTED,
            'effective_date' => now()->subDay(),
        ]);

        $action = app(RevokeSubscriptionCancellationAction::class);

        $this->expectException(SubscriptionCancellationException::class);
        $this->expectExceptionMessage('Es wurde keine aktive Kündigung gefunden');

        $action->execute($pharmacy, $user);
    }

    public function test_calculates_next_quarter_end_correctly(): void
    {
        // Test für verschiedene Daten
        $testCases = [
            ['2024-01-15', '2024-03-31'], // Mitte Q1 -> Ende Q1
            ['2024-03-31', '2024-06-30'], // Ende Q1 -> Ende Q2
            ['2024-07-01', '2024-09-30'], // Anfang Q3 -> Ende Q3
            ['2024-12-15', '2025-03-31'], // Mitte Q4 -> Ende Q1 nächstes Jahr
        ];

        foreach ($testCases as [$input, $expected]) {
            $inputDate = Carbon::parse($input);
            $expectedDate = Carbon::parse($expected);
            $result = SubscriptionCancellation::calculateNextQuarterEnd($inputDate);

            $this->assertEquals(
                $expectedDate->toDateString(),
                $result->toDateString(),
                "Failed for input date: {$input}"
            );
        }
    }

    public function test_pharmacy_has_active_cancellation_method(): void
    {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        // Keine Kündigung
        $this->assertFalse($pharmacy->hasActiveCancellation());

        // Ausstehende Kündigung
        SubscriptionCancellation::factory()->create([
            'pharmacy_id' => $pharmacy->id,
            'status' => SubscriptionCancellationStatusEnum::PENDING,
        ]);
        $pharmacy->refresh();
        $this->assertTrue($pharmacy->hasActiveCancellation());

        // Widerrufene Kündigung
        $pharmacy->subscriptionCancellation->update([
            'status' => SubscriptionCancellationStatusEnum::REVOKED
        ]);
        $pharmacy->refresh();
        $this->assertFalse($pharmacy->hasActiveCancellation());
    }

    private function mockSubscription($pharmacy, $productClass): void
    {
        // Mock für isSubscribedToProduct
        $pharmacy->shouldReceive('isSubscribedToProduct')
            ->with($productClass)
            ->andReturn(true);

        $pharmacy->shouldReceive('subscription')
            ->andReturn((object) ['created_at' => now()->subMonths(4)]);
    }
}
