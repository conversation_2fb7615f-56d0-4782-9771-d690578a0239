<?php

namespace Tests\Feature\Excel;

use App\Excel\Imports\AssociationMemberImport;
use App\Jobs\InstantRegistrationJob;
use App\User;
use Http;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use Tests\TestCase;

/**
 * @group AP-727
 */
class AssociationMemberImportTest extends TestCase
{
    use RefreshDatabase;

    public function test_it_can_import_association_members(): void
    {
        Excel::fake();
        Mail::fake();
        Http::fake()->preventStrayRequests();
        $this->actingAs(User::factory()->make());

        $testFile = 'tests/storage/association-member-import.xlsx';

        Excel::import(new AssociationMemberImport(1), $testFile);

        Excel::assertImported($testFile, function (AssociationMemberImport $import) {
            return true;
        });
    }

    public function test_it_maps_rows_correctly(): void
    {
        Mail::fake();
        Http::fake()->preventStrayRequests();
        $this->actingAs(User::factory()->make());

        $testFile = 'tests/storage/association-member-import.xlsx';

        $collection = Excel::toCollection(new AssociationMemberImport(1), $testFile);

        $rows = $collection->first();
        self::assertCount(2, $rows);

        $firstRow = $rows[0];

        self::assertSame([
            'anrede_auswahlfeld' => 'Frau',
            'akadem_grad' => null,
            'vorname_inhaber' => 'Vorname1',
            'nachname_inhaber' => 'Nachname 1',
            'e_mail_adresse_inhaber' => '<EMAIL>',
            'ohg_name' => null,
            'name_der_apotheke' => 'Apotheke1',
            'adresszusatz' => null,
            'strasse_hausnr' => 'Beispielweg 1',
            'postleitzahl' => 97638,
            'ort' => 'Mellrichstadt',
            'datum_format_ttmmyyyy' => '01.08.2023',
        ], $firstRow->toArray());

        $secondRow = $rows[1];

        self::assertSame([
            'anrede_auswahlfeld' => 'Frau',
            'akadem_grad' => null,
            'vorname_inhaber' => 'Vorname2',
            'nachname_inhaber' => 'Nachname2',
            'e_mail_adresse_inhaber' => '<EMAIL>',
            'ohg_name' => null,
            'name_der_apotheke' => 'Apotheke2',
            'adresszusatz' => null,
            'strasse_hausnr' => 'Beispielweg 2',
            'postleitzahl' => 91171,
            'ort' => 'Greding',
            'datum_format_ttmmyyyy' => '01.08.2023',
        ], $secondRow->toArray());
    }

    public function test_it_dispatches_registration_job_correctly(): void
    {
        Bus::fake();
        Mail::fake();
        Http::fake()->preventStrayRequests();
        $this->actingAs(User::factory()->make());

        $testFile = 'tests/storage/association-member-import.xlsx';

        Excel::import(new AssociationMemberImport(1), $testFile);

        Bus::assertDispatched(InstantRegistrationJob::class);
        Bus::assertDispatchedTimes(InstantRegistrationJob::class, 2);
    }

    public function test_jobs_receive_expected_parameters(): void
    {
        Bus::fake();
        Mail::fake();
        Http::fake()->preventStrayRequests();
        $this->actingAs(User::factory()->make());

        $testFile = 'tests/storage/association-member-import.xlsx';

        Excel::import(new AssociationMemberImport(1), $testFile);

        Bus::assertDispatched(InstantRegistrationJob::class, function (InstantRegistrationJob $job) {

            $data = self::getProtectedProperty($job, 'data');
            if ($data['email'] === '<EMAIL>') {
                self::assertSame([
                    'association_id' => 1,
                    'salutation' => 'ms',
                    'title' => null,
                    'first_name' => 'Vorname1',
                    'last_name' => 'Nachname 1',
                    'email' => '<EMAIL>',
                    'is_company' => false,
                    'company_name' => null,
                    'pharmacy_name' => 'Apotheke1',
                    'optional_address_line' => null,
                    'street' => 'Beispielweg 1',
                    'postcode' => 97638,
                    'city' => 'Mellrichstadt',
                ], $data);
            }

            if ($data['email'] === '<EMAIL>') {
                self::assertSame([
                    'association_id' => 1,
                    'salutation' => 'ms',
                    'title' => null,
                    'first_name' => 'Vorname2',
                    'last_name' => 'Nachname2',
                    'email' => '<EMAIL>',
                    'is_company' => false,
                    'company_name' => null,
                    'pharmacy_name' => 'Apotheke2',
                    'optional_address_line' => null,
                    'street' => 'Beispielweg 2',
                    'postcode' => 91171,
                    'city' => 'Greding',
                ], $data);
            }

            return true;
        });
    }
}
