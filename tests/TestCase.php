<?php

namespace Tests;

use App\Apomail;
use App\Association;
use App\CardLinkOrder;
use App\CompanyUser;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\ApomailStatus;
use App\Enums\AssociationRoleEnum;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Enums\DocSpaceDeleteModes;
use App\Enums\DocSpaceRetentionDuration;
use App\Enums\PaymentMethod;
use App\Enums\PharmacyRoleEnum;
use App\Enums\StaffRoleEnum;
use App\Http\Integrations\CardLinkService\Requests\DeleteCardLinkSettingsRequest;
use App\Http\Integrations\CardLinkService\Requests\GetCardLinkSettingsRequest;
use App\Http\Integrations\CardLinkService\Requests\GetChannelsRequest;
use App\Http\Integrations\CardLinkService\Requests\GetTransactionUsageRequest;
use App\Http\Integrations\CardLinkService\Requests\SetCardLinkSettingsRequest;
use App\Http\Integrations\CardLinkService\Requests\VendorActivationRequest;
use App\Http\Integrations\Ia\PartnerApi\Requests\GetPharmacyRequest;
use App\Http\Integrations\Ia\PartnerApi\Requests\GetWebComponentAuthTokenRequest;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\GetOpeningTimesRequest;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\UpdateLogoRequest;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\UpdateOpeningTimesRequest;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\UpdatePharmacyAddressRequest;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\UpdatePharmacyDetailsRequest;
use App\Http\Integrations\Ia\UpdatePharmacyDataApi\Requests\UpdatePharmacyRequest;
use App\Pharmacy;
use App\Settings\CardLinkSettings;
use App\Settings\SubscriptionSettings;
use App\Settings\TermsOfServiceSettings;
use App\Staff;
use App\User;
use App\UserAssociationProfile;
use App\UserPharmacyProfile;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Testing\LazilyRefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use ReflectionObject;
use Saloon\Http\Faking\MockClient;
use Saloon\Http\Faking\MockResponse;
use Stripe\Exception\ApiErrorException;
use Stripe\StripeClient;
use Tests\helper\AccessHelper;
use Tests\helper\AuthHelper;
use Tests\helper\InvoiceHelper;
use Tests\helper\PharmacyHelper;
use Tests\helper\SubscriptionHelper;
use Tests\helper\TermsHelper;

abstract class TestCase extends BaseTestCase
{
    use AccessHelper,
        AuthHelper,
        CreatesApplication,
        InvoiceHelper,
        LazilyRefreshDatabase,
        PharmacyHelper,
        SubscriptionHelper,
        TermsHelper,
        WithFaker;

    private StripeClient $stripe;

    private string $localStripeIdPrefix = 'local_';

    protected function setUp(): void
    {
        parent::setUp();

        $this->stripe = new StripeClient(config('cashier.secret'));

        Storage::fake('media');
        Storage::fake('local');

        Http::fake([
            config('services.token-service.url').'/*' => Http::response(json_encode([])),
        ]);
        Queue::fake();

        MockClient::destroyGlobal();

        MockClient::global([
            GetWebComponentAuthTokenRequest::class => MockResponse::make(
                body: [
                    'token' => '2dcaa82cae3e9ed91e34b2b7ff118e05b7cd19aff55b62a8ead09eebf0c9fe28',
                    'zpa_customer' => true,
                    'additional-components' => [],
                ]
            ),
            UpdatePharmacyDetailsRequest::class => MockResponse::make(),
            UpdatePharmacyRequest::class => MockResponse::make(),
            UpdateLogoRequest::class => MockResponse::make(),
            UpdatePharmacyAddressRequest::class => MockResponse::make(),
            UpdateOpeningTimesRequest::class => MockResponse::make(),
            GetPharmacyRequest::class => MockResponse::make(body: $this->getPharmacyRequestData()),
            GetOpeningTimesRequest::class => MockResponse::make(body: $this->getOpeningTimeRequestData()),
            CardLinkSettings::url('/oauth/token') => MockResponse::make([
                'token_type' => 'Bearer',
                'expires_in' => now()->addSeconds(60)->getTimestamp(),
                'access_token' => Str::random(32),
            ]),
            SetCardLinkSettingsRequest::class => MockResponse::make(),
            DeleteCardLinkSettingsRequest::class => MockResponse::make(),
            GetCardLinkSettingsRequest::class => MockResponse::make($this->getCardLinkSettingsRequestData()),
            GetChannelsRequest::class => MockResponse::make(body: $this->getChannelRequestData()),
            GetTransactionUsageRequest::class => MockResponse::make(body: $this->getTransactionUsageRequestData()),
            VendorActivationRequest::class => MockResponse::make(),
        ]);

        $settings = app(SubscriptionSettings::class);
        $settings->stripeEnabledAt = Carbon::parse('2022-01-01');
        $settings->save();

        $termsSettings = app(TermsOfServiceSettings::class);
        $termsSettings->new_terms_of_service_deadline = Carbon::now()->subDay();
        $termsSettings->save();
    }

    protected function makeLocalStripeId(): string
    {
        return $this->localStripeIdPrefix.Str::uuid();
    }

    protected function tearDown(): void
    {
        $attempts = 0;
        $maxAttempts = 3;
        while ($attempts < $maxAttempts) {
            try {
                $pharmacies = Pharmacy::whereNot('stripe_id', 'like', $this->localStripeIdPrefix.'%')->whereNotNull('stripe_id')->get();
                foreach ($pharmacies as $pharmacy) {
                    if (str_contains($pharmacy->stripeId(), 'test')) {
                        continue;
                    }
                    $this->stripe->customers->delete($pharmacy->stripeId());
                }
                break;
            } catch (ApiErrorException $e) {
                $attempts++;

                if ($attempts < $maxAttempts && str_contains($e->getMessage(), 'Request rate limit exceeded.')) {
                    sleep($attempts * 2);

                    continue;
                }

                throw new Exception('Failed to delete test customers. If the reason is rate limit, consider increasing backoff or retry attempts. If the customer could not be found, you should use the makeLocalStripeId() method. Exception message: '.$e->getMessage(), 0, $e);
            }
        }

        parent::tearDown();
    }

    /**
     * @return array{0: User, 1: Pharmacy}
     */
    public function createPharmacyUserWithPharmacy(): array
    {
        $user = $this->createPharmacyUser();
        $pharmacy = $this->createPharmacy();

        $pharmacy->assignUser($user, PharmacyRoleEnum::OWNER);
        $this->acceptTermsForPharmacy($pharmacy);

        return [$user, $pharmacy];
    }

    public function createPharmacyEmployee(
        Pharmacy $pharmacy,
        $role = PharmacyRoleEnum::BRANCH_MANAGER,
        array $permissions = []
    ): User {
        $user = User::factory()->create();

        $user->pharmacyProfile()->create([
            'association_id' => null,
        ]);

        $pharmacy->assignUser($user, $role, $permissions);

        $user->refresh();

        return $user;
    }

    public function createPharmacyUser($userData = [], $isCompanyUser = false, $isOwner = true, ?Association $association = null): User
    {
        $user = User::factory()->when($isOwner, fn ($factory) => $factory->owner())->create($userData);

        if ($isOwner) {
            $user->brochureCode()->create();
        }

        $user->pharmacyProfile()->create([
            'association_id' => $association?->id,
            'company_user_id' => $isCompanyUser ? $user->id : null,
        ]);

        $user->refresh();

        return $user;
    }

    /**
     * @return array{0: User, 1: Association}
     */
    public function createAssociationUser(
        ?Association $association = null,
        $role = AssociationRoleEnum::ADMIN,
        $permissions = []
    ): array {
        $association = $association ?? Association::factory()->create();
        $user = User::factory()->create();

        UserAssociationProfile::create([
            'user_id' => $user->id,
        ]);

        $association->assignUser(
            $user,
            $role,
            $permissions
        );

        return [$user, $association];
    }

    public function createCompanyUser(): User
    {
        $user = User::factory()->create();

        CompanyUser::create([
            'user_id' => $user->id,
            'name' => 'Testapotheke OHG',
        ]);

        UserPharmacyProfile::create([
            'user_id' => $user->id,
            'company_user_id' => $user->id,
        ]);

        return $user;
    }

    public function createStaff(?string $role = null): Staff
    {
        return Staff::factory()->create([
            'role' => $role ?? StaffRoleEnum::ADMIN,
        ]);
    }

    /**
     * @return array{0: User, 1: Pharmacy, 2: User}
     */
    public function createPharmacyUserWithRole(string $role): array
    {
        if ($role === PharmacyRoleEnum::OWNER) {
            return $this->createPharmacyUserWithPharmacy();
        }

        $user = User::factory()->create();

        $user->pharmacyProfile()->create([
            'association_id' => null,
        ]);

        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $pharmacy->assignUser($user, $role);

        $user->refresh();

        return [$user, $pharmacy, $owner];
    }

    /**
     * @return array{0: User, 1: Pharmacy}
     *
     * @throws Exception
     */
    public function createEmployeeWithApomailUser(Pharmacy $pharmacy, ?string $email = null): array
    {
        $employee = $this->createPharmacyEmployee($pharmacy, PharmacyRoleEnum::EMPLOYEE);
        $users = collect([$employee]);

        $apomailCreationArray = [
            'owner_id' => $pharmacy->owner()->id,
        ];

        if (isset($email)) {
            $apomailCreationArray['email'] = $email;
        }

        $apomail = Apomail::factory([
            'status' => ApomailStatus::RESERVED,
        ])->create($apomailCreationArray);

        Apomail::all()->each(function ($apomail) use ($users) {
            $usersFromOwner = $users->filter(function (User $user) use ($apomail) {
                return $user->owner() && $apomail->owner_id === $user->owner()->id;
            });
            if ($usersFromOwner->count()) {
                $apomail->users()->attach(
                    $users->random(1)->pluck('id')->toArray()
                );
            }
        });

        return [$apomail, $employee];
    }

    public function getPharmacyRequestData(): array
    {
        return [
            'pharmacyId' => 2163,
            'partnerId' => '2',
            'pharmacyBaseData' => [
                'data' => [
                    'getPharmacyDetails' => [
                        'company' => [
                            'name' => '',
                            'legalEntity' => 'none',
                            'owners' => [
                            ],
                        ],
                        'services' => [
                            [
                                'title' => 'Schwerpunkt Tiergesundheit',
                                'name' => 'animalHealth',
                                'emphasized' => false,
                                'descriptions' => [
                                    'Hund',
                                ],
                            ],
                            [
                                'title' => 'Bargeldlose Zahlung',
                                'name' => 'cashlessPayment',
                                'emphasized' => false,
                                'descriptions' => [
                                    'PayPal',
                                    'EC-Karte',
                                ],
                            ],
                        ],
                        'additionalInformation' => [
                            'apodeId' => 32263,
                            'name' => 'Testapotheke ihreapotheken.de',
                            'logo' => 'https://imgqa.ihreapotheken.de/qa/pharmacy-images/2163/logo/2163_phpmobsi5.png',
                            'slug' => 'testapotheke-ihreapothekende-stadt-12345-2163',
                            'url' => 'https://qa.ihreapotheken.de/apotheke/testapotheke-ihreapothekende-stadt-12345-2163',
                            'allowPayments' => false,
                        ],
                        'contact' => [
                            'phone' => '0201-802 4000 11',
                            'email' => '<EMAIL>',
                            'fax' => '0201-802 4040',
                            'websiteUrl' => 'https://qa.ihreapotheken.de/',
                        ],
                        'businessHours' => [
                            'days' => [
                                [
                                    'day' => 'Montag',
                                    'text' => '07:00 bis 07:30 und 08:30 bis 23:15 Uhr',
                                    'isToday' => false,
                                ],
                                [
                                    'day' => 'Dienstag',
                                    'text' => '08:00 bis 12:30 Uhr',
                                    'isToday' => false,
                                ],
                                [
                                    'day' => 'Mittwoch',
                                    'text' => '08:00 bis 13:45 Uhr',
                                    'isToday' => false,
                                ],
                                [
                                    'day' => 'Donnerstag',
                                    'text' => '08:00 bis 10:00 und 12:00 bis 13:00 Uhr',
                                    'isToday' => false,
                                ],
                                [
                                    'day' => 'Freitag',
                                    'text' => '08:00 bis 09:00 und 12:45 bis 20:00 Uhr',
                                    'isToday' => true,
                                ],
                                [
                                    'day' => 'Samstag',
                                    'text' => '08:00 bis 15:00 Uhr',
                                    'isToday' => false,
                                ],
                                [
                                    'day' => 'Sonntag',
                                    'text' => '08:00 bis 20:00 Uhr',
                                    'isToday' => false,
                                ],
                            ],
                        ],
                        'location' => [
                            'city' => 'Stadt',
                            'postcode' => '12345',
                            'region' => '',
                            'street' => 'Straße 1',
                        ],
                        'id' => '2163',
                    ],
                ],
            ],
            'zpa_customer' => true,
        ];
    }

    public function getOpeningTimeRequestData(): array
    {
        return [
            'mon' => [
                'morningStart' => '08:00',
                'morningEnd' => null,
                'afternoonStart' => null,
                'afternoonEnd' => '17:00',
            ],
            'tue' => [
                'morningStart' => '08:00',
                'morningEnd' => null,
                'afternoonStart' => null,
                'afternoonEnd' => '17:00',
            ],
            'wed' => [
                'morningStart' => '08:00',
                'morningEnd' => '12:00',
                'afternoonStart' => '13:00',
                'afternoonEnd' => '17:00',
            ],
            'thu' => [
                'morningStart' => '08:00',
                'morningEnd' => null,
                'afternoonStart' => null,
                'afternoonEnd' => '17:00',
            ],
            'fri' => [
                'morningStart' => '08:00',
                'morningEnd' => null,
                'afternoonStart' => null,
                'afternoonEnd' => '17:00',
            ],
            'sat' => [
                'morningStart' => '08:00',
                'morningEnd' => null,
                'afternoonStart' => null,
                'afternoonEnd' => '13:00',
            ],
            'sun' => null,
        ];
    }

    public function getChannelRequestData(): array
    {
        return [
            'gedisa_id' => 'ABC12345',
            'channels' => [
                [
                    'channel_external_id' => '1',
                    'channel_name' => 'apoguide-web',
                    'vendor_external_id' => '1',
                    'vendor_name' => 'GEDISA',
                    'website' => 'http://localhost',
                    'is_active' => true,
                ],
                [
                    'channel_external_id' => '2',
                    'channel_name' => 'ihre-apotheken-web',
                    'vendor_external_id' => '2',
                    'vendor_name' => 'IhreApotheken',
                    'website' => 'http://localhost',
                    'is_active' => true,
                ],
                [
                    'channel_external_id' => '3',
                    'channel_name' => 'gesund-de-web',
                    'vendor_external_id' => '3',
                    'vendor_name' => 'gesund.de',
                    'website' => 'http://localhost',
                    'is_active' => false,
                ],
                [
                    'channel_external_id' => '4',
                    'channel_name' => 'wave-web',
                    'vendor_external_id' => '4',
                    'vendor_name' => 'Wave',
                    'website' => 'http://localhost',
                    'is_active' => false,
                ],
            ],
        ];
    }

    public function getSubscriberRequestData(): array
    {
        return [
            'total' => 1,
            'data' => [
                [
                    'id' => 1,
                    'name' => '[Test] Subscriber',
                    'email' => '<EMAIL>',
                    'other_details' => [],
                    'created_at' => '2024-08-02T09:49:50.943Z',
                    'updated_at' => '2024-08-02T09:49:50.943Z',
                ],
            ],
        ];
    }

    public function createSubscriberRequestData(): array
    {
        return [
            'id' => 1,
            'name' => '[Test] Subscriber',
            'email' => '<EMAIL>',
            'other_details' => [],
            'created_at' => '2024-08-02T09:49:50.943Z',
            'updated_at' => '2024-08-02T09:49:50.943Z',
        ];
    }

    public function getOrganizationRequestData(): array
    {
        return [
            'total' => 1,
            'data' => [
                [
                    'id' => 1,
                    'name' => '[Test] Organization',
                    'street' => '[Test] Straße 1',
                    'city' => '[Test] Ort',
                    'state' => '[Test] Bundesland',
                    'country_code' => 'de',
                    'postal_code' => '12345',
                    'optional_address_line' => null,
                    'payment_method' => PaymentMethod::Invoice->value,
                    'created_at' => '2024-08-02T09:52:00.115Z',
                    'updated_at' => '2024-08-02T09:52:00.115Z',
                ],
            ],
        ];
    }

    public function createOrganizationRequestData(): array
    {
        return [
            'id' => 1,
            'name' => '[Test] Organization',
            'street' => '[Test] Straße 1',
            'city' => '[Test] Ort',
            'state' => '[Test] Bundesland',
            'country_code' => 'de',
            'postal_code' => '12345',
            'optional_address_line' => null,
            'payment_method' => PaymentMethod::Invoice->value,
            'created_at' => '2024-08-02T09:52:00.115Z',
            'updated_at' => '2024-08-02T09:52:00.115Z',
        ];
    }

    public function createOrderRequestData(): array
    {
        return [
            'id' => 1,
            'subscriber' => $this->createSubscriberRequestData(),
            'organization' => $this->createOrganizationRequestData(),
            'transaction_id' => 'ABC-123',
            'number' => '123456',
            'total_price' => 49.00,
            'created_at' => '2024-08-02T10:26:55.064Z',
            'updated_at' => '2024-08-02T10:26:55.064Z',
        ];
    }

    public function createOrderItemRequestData(): array
    {
        return [
            'id' => 0,
            'order' => $this->createOrderRequestData(),
            'subscription_plan' => [
                'id' => 1,
                'parent' => null,
                'name' => 'CardLink S',
                'slug' => 'card-link-s',
                'description' => '...',
                'duration' => 12,
                'renewal_period' => 'yearly',
                'termination_notice_duration' => 0,
                'termination_notice_period' => 'day',
                'is_trial' => false,
                'trial_days' => 0,
                'created_at' => '2024-08-02T10:29:00.043Z',
                'updated_at' => '2024-08-02T10:29:00.043Z',
            ],
            'type' => 'one-time-purchase',
            'quantity' => 1,
            'purchase_price' => 49.00,
            'adjusted_price' => 49.00,
            'created_at' => '2024-08-02T10:29:00.043Z',
            'updated_at' => '2024-08-02T10:29:00.043Z',
        ];
    }

    public function getOrderItemRequestData(): array
    {
        return [
            'total' => 1,
            'data' => [
                [
                    'order' => [
                        'subscriber' => [
                            'name' => 'dolore dolore et',
                            'email' => 'cupidatat',
                            'id' => -61272323,
                            'other_details' => [],
                            'custom_identifier' => 'urn:uuid:9e4d656f-9017-dcef-385c-64c94ff93a56',
                            'created_at' => '1970-10-28T05:03:45.336Z',
                            'updated_at' => '1987-11-30T08:28:45.807Z',
                        ],
                        'id' => 73633526,
                        'organization' => [
                            'name' => 'sed nulla Ut',
                            'street' => 'esse elit officia',
                            'city' => 'consequat ut ipsum labore',
                            'postal_code' => 'proident irure',
                            'id' => 42486756,
                            'state' => 'aute laborum sint veniam laboris',
                            'country_code' => 'de',
                            'optional_address_line' => 'Lorem et dolor',
                            'payment_method' => 'invoice',
                            'custom_identifier' => '9df2f65d-4462-fb42-7588-f20de6a9bd06',
                            'created_at' => '1947-03-08T17:52:49.851Z',
                            'updated_at' => '1991-12-20T20:01:14.989Z',
                        ],
                        'transaction_id' => 'eu anim',
                        'number' => 'ea l',
                        'total_price' => 81558315.9636406,
                        'ordered_at' => '1962-10-02T12:05:42.678Z',
                        'canceled_at' => '1997-07-29T06:57:57.916Z',
                        'created_at' => '1983-11-29T05:00:48.502Z',
                        'updated_at' => '2019-12-13T06:09:19.631Z',
                    ],
                    'subscription_plan' => [
                        'name' => 'dolor aliquip enim',
                        'description' => 'ex in minim Excepteur',
                        'termination_notice_duration' => 55222341,
                        'termination_notice_period' => 'year',
                        'id' => -86049557,
                        'parent' => [
                            'value' => '<Circular reference to #/components/schemas/SubscriptionPlan detected>',
                        ],
                        'slug' => 'in mollit',
                        'duration' => 27071611,
                        'renewal_period' => 'day',
                        'is_trial' => false,
                        'trial_days' => 85834682,
                        'is_paid_in_advance' => true,
                        'created_at' => '1958-02-23T21:46:10.781Z',
                        'updated_at' => '1956-03-30T13:25:18.579Z',
                    ],
                    'type' => 'one-time-purchase',
                    'id' => -79253164,
                    'discount' => [
                        'value' => 65177332.73638168,
                        'name' => 'amet ullamco cillum',
                        'type' => 'fixed',
                        'starts_at' => '1969-10-28T00:39:59.335Z',
                        'ends_at' => '1992-03-29T02:34:30.286Z',
                        'id' => 36646643,
                        'subscription_plan' => [
                            'name' => 'dolor minim incididunt sunt',
                            'description' => 'ad reprehenderit',
                            'termination_notice_duration' => -36554129,
                            'termination_notice_period' => 'year',
                            'id' => 2779201,
                            'parent' => [
                                'value' => '<Circular reference to #/components/schemas/SubscriptionPlan detected>',
                            ],
                            'slug' => 'veniam ut magna in',
                            'duration' => -4931780,
                            'renewal_period' => 'week',
                            'is_trial' => false,
                            'trial_days' => -25793961,
                            'is_paid_in_advance' => true,
                            'created_at' => '2010-03-28T16:20:22.595Z',
                            'updated_at' => '2004-10-12T21:44:12.998Z',
                        ],
                        'voucher' => [
                            'code' => 'consequat',
                            'amount' => 88629974.06440347,
                            'id' => 93349954,
                            'subscriber' => [
                                'name' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'email' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'id' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'other_details' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'custom_identifier' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'created_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'updated_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                            ],
                            'organization' => [
                                'name' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'street' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'city' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'postal_code' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'id' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'state' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'country_code' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'optional_address_line' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'payment_method' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'custom_identifier' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'created_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'updated_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                            ],
                            'expires_at' => '2024-07-06T18:53:01.523Z',
                            'created_at' => '1949-03-14T15:51:30.158Z',
                            'updated_at' => '2009-09-11T02:32:27.630Z',
                        ],
                        'description' => 'tempor',
                        'is_applied_to_plan' => false,
                        'created_at' => '1966-12-19T15:40:41.302Z',
                        'updated_at' => '1956-02-02T16:34:45.159Z',
                    ],
                    'quantity' => 53551022,
                    'purchase_price' => 81442985.15776822,
                    'adjusted_price' => -1848857.2065263242,
                    'created_at' => '1967-03-25T00:22:07.585Z',
                    'updated_at' => '1972-07-27T00:43:20.982Z',
                ],
            ],
        ];
    }

    public function getSubscriptionAvailabilitiesRequestData(): array
    {
        return [
            'total' => 1,
            'data' => [
                [
                    'subscription_plan' => [
                        'name' => 'incididunt culpa',
                        'description' => 'do id aute',
                        'termination_notice_duration' => -62044953,
                        'termination_notice_period' => 'day',
                        'id' => 31412995,
                        'parent' => [
                            'value' => '<Circular reference to #/components/schemas/SubscriptionPlan detected>',
                        ],
                        'slug' => 'ut esse Duis',
                        'duration' => -45773760,
                        'renewal_period' => 'quarter',
                        'is_trial' => false,
                        'trial_days' => 3074088,
                        'is_paid_in_advance' => true,
                        'created_at' => '1984-07-20T08:01:42.313Z',
                        'updated_at' => '1959-07-17T16:04:02.543Z',
                    ],
                    'order_item' => [
                        'order' => [
                            'subscriber' => [
                                'name' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'email' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'id' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'other_details' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'custom_identifier' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'created_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'updated_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                            ],
                            'id' => 3974066,
                            'organization' => [
                                'name' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'street' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'city' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'postal_code' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'id' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'state' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'country_code' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'optional_address_line' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'payment_method' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'custom_identifier' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'created_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'updated_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                            ],
                            'transaction_id' => 'do nisi',
                            'number' => 'incididunt in',
                            'total_price' => 51354109.32404661,
                            'ordered_at' => '1970-10-03T01:27:54.687Z',
                            'canceled_at' => '1946-10-23T16:53:15.948Z',
                            'created_at' => '1978-07-26T23:31:40.487Z',
                            'updated_at' => '1968-01-02T11:04:51.433Z',
                        ],
                        'subscription_plan' => [
                            'name' => 'occaecat',
                            'description' => 'sunt dolor',
                            'termination_notice_duration' => 34435295,
                            'termination_notice_period' => 'day',
                            'id' => 86444510,
                            'parent' => [
                                'value' => '<Circular reference to #/components/schemas/SubscriptionPlan detected>',
                            ],
                            'slug' => 'irure n',
                            'duration' => -55273980,
                            'renewal_period' => 'day',
                            'is_trial' => false,
                            'trial_days' => 14180445,
                            'is_paid_in_advance' => true,
                            'created_at' => '1968-04-27T16:34:29.755Z',
                            'updated_at' => '1991-05-05T16:52:22.255Z',
                        ],
                        'type' => 'one-time-purchase',
                        'id' => -65133231,
                        'discount' => [
                            'value' => 75061793.14601424,
                            'name' => 'Duis cillum',
                            'type' => 'percentage',
                            'starts_at' => '1950-02-06T03:23:53.695Z',
                            'ends_at' => '1973-09-23T17:48:26.784Z',
                            'id' => 59306492,
                            'subscription_plan' => [
                                'name' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'description' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'termination_notice_duration' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'termination_notice_period' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'id' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'parent' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'slug' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'duration' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'renewal_period' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'is_trial' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'trial_days' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'is_paid_in_advance' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'created_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'updated_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                            ],
                            'voucher' => [
                                'code' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'amount' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'id' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'subscriber' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'organization' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'expires_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'created_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                                'updated_at' => [
                                    'value' => '<Error=> Too many levels of nesting to fake this schema>',
                                ],
                            ],
                            'description' => 'consequat mollit dolore officia nisi',
                            'is_applied_to_plan' => true,
                            'created_at' => '1975-07-01T02:00:30.233Z',
                            'updated_at' => '1988-03-12T10:33:00.194Z',
                        ],
                        'quantity' => -99835898,
                        'purchase_price' => 17458893.735353038,
                        'adjusted_price' => 57158171.218765706,
                        'created_at' => '1969-08-21T17:01:43.537Z',
                        'updated_at' => '1988-01-30T09:47:47.486Z',
                    ],
                    'starts_at' => '1984-01-30T16:53:03.650Z',
                    'id' => 62579796,
                    'is_active' => true,
                    'ends_at' => '1953-01-28T04:35:39.974Z',
                    'renews_at' => '2022-10-19T12:45:04.479Z',
                    'created_at' => '1974-06-03T00:21:11.669Z',
                    'updated_at' => '1962-04-06T20:32:47.078Z',
                ],
            ],
        ];
    }

    public function getSubscriptionPlanRequestData(): array
    {
        return [
            'total' => 1,
            'data' => [
                [
                    'id' => 1,
                    'parent' => null,
                    'name' => 'CardLink Package S',
                    'slug' => 'card-link-package-s',
                    'description' => 'ABC',
                    'duration' => 1,
                    'renewal_period' => 'year',
                    'termination_notice_duration' => 1,
                    'termination_notice_period' => 'month',
                    'is_trial' => false,
                    'trial_days' => 0,
                    'created_at' => '2024-08-05T07:31:10.918Z',
                    'updated_at' => '2024-08-05T07:31:10.918Z',
                ],
            ],
        ];
    }

    public function getTransactionUsageRequestData(): array
    {
        return [
            'usage' => 75,
            'vendors' => [
                [
                    'id' => 'apoguide',
                    'usage' => 75,
                    'channels' => [
                        [
                            'id' => 'apoguide-web',
                            'usage' => 50,
                        ],
                        [
                            'id' => 'apoguide-app',
                            'usage' => 25,
                        ],
                    ],
                ],
            ],
        ];
    }

    protected function clearExistingHttpFakes(): void
    {
        $reflection = new ReflectionObject(Http::getFacadeRoot());
        $property = $reflection->getProperty('stubCallbacks');
        $property->setAccessible(true);
        $property->setValue(Http::getFacadeRoot(), collect());
    }

    public function createOwnerForSDR(
        bool $uses_sdr = true,
        bool $hasExtendedSubscriptionPlan = false,
        bool $has2faEnabled = false,
        bool $withAssociation = true,
        bool $isFriendlyUserTest = true
    ): array {
        [$user, $pharmacy] = $this->createPharmacyUserWithPharmacy();

        $association = Association::factory()->create();

        if ($withAssociation) {
            $user->pharmacyProfile->association_id = $association->id;
            $user->pharmacyProfile->save();
        }

        $this->createLocalStripeSubscription($pharmacy, BaseStripeProduct::make());

        if ($has2faEnabled) {
            session()->put('2fa-activated', 'some-data');
        }

        if ($isFriendlyUserTest) {
            $user->update(['is_beta_tester' => true]);
        }

        $this->actingAs($user); // For currentPharmacy()

        $pharmacy->uses_sdr = $uses_sdr;
        $pharmacy->save();

        return [$user->refresh(), $pharmacy->refresh()];
    }

    protected function createCardLinkOrder(Pharmacy|int $pharmacy, User|int $user, ?array $data = null): CardLinkOrder
    {
        if (is_int($pharmacy) === false) {
            $pharmacy = $pharmacy->id;
        }

        if (is_int($user) === false) {
            $user = $user->id;
        }

        if ($data === null) {
            $data = [
                'status' => CardLinkOrderStatusEnum::Ordered->value,
                'ordered_at' => now(),
                'order_information' => [
                    'package' => CardLinkPackageEnum::cases()[array_rand(CardLinkPackageEnum::cases())]->value,
                    'show_in_apoguide' => (bool) random_int(0, 1),
                    'activate_apo_guide_vendor' => (bool) random_int(0, 1),
                ],
            ];
        }

        return CardLinkOrder::create([
            'pharmacy_id' => $pharmacy,
            'user_id' => $user,
            ...$data,
        ]);
    }

    public function fakeSDRHttpWithOIDCAuth(): array
    {
        $response = [
            'name' => 'TEST',
            'description' => null,
            'schemaId' => config('sdr.schemaId'),
            'quota' => [
                'soft' => config('sdr.createGuided.hardQuotaFixed') - (config(
                    'sdr.createGuided.hardQuotaFixed'
                ) / 100 * config('sdr.quota.softFactorInPercent')),
                'hard' => config('sdr.createGuided.hardQuotaFixed'),
            ],
            'maxUploadFileSize' => config('sdr.maxUploadFileSizeInMB'),
            'allowedDeleteModes' => [DocSpaceDeleteModes::PHYSICAL],
            'allowedFileTypes' => config('sdr.allowedFileTypes'),
            'auditProof' => false,
            'encrypted' => config('sdr.encrypted'),
            'retentionDuration' => DocSpaceRetentionDuration::NONE->value,
            'id' => $this->faker->uuid,
            'status' => 'ACTIVE',
            'lastUsage' => 0,
        ];

        Http::fake([
            config('oidc-auth.provider.baseUrl').'/*' => Http::response([
                'access_token' => 'token-123',
            ]),
            // get docspace by name
            config('sdr.apiUrl').'/docSpace/v1/name/*' => Http::response(json_encode($response)),
            // create group
            config('sdr.apiUrl').'/group/v1' => Http::response(
                json_encode([
                    'id' => $this->faker->uuid,
                ])
            ),
            // search group
            config('sdr.apiUrl').'/group/v1?*' => Http::response(
                json_encode([
                    'totalCount' => 0,
                    'content' => [],
                ])
            ),
            // delete group
            config('sdr.apiUrl').'/group/v1/*' => Http::response(''),
            // create user
            config('sdr.apiUrl').'/user/v1' => Http::response(
                json_encode([
                    'id' => $this->faker->uuid,
                ])
            ),
            // create docspace
            config('sdr.apiUrl').'/docSpace/v1' => Http::response(json_encode($response)),
            // assign group to docspace
            config('sdr.apiUrl').'/docSpace/v1/*/groups/*' => Http::response(''),
        ])->preventStrayRequests();

        return $response;
    }

    protected function swapQueue(): void
    {
        $this->app->forgetInstance('queue');
        $this->app->forgetInstance('queue.connection');
        $this->app->forgetInstance('queue.failer');
        $this->app->make('queue');
    }

    /**
     * @return array<string, string|int>
     */
    public function getCardLinkSettingsRequestData(int $currentLimit = 500, int $currentUsage = 251): array
    {
        return [
            'gedisa_id' => 'ABC12345',
            'limit' => 250,
            'current_limit' => $currentLimit, // current limit > limit possible after downgrading
            'current_usage' => $currentUsage,
            'subscription_plan_id' => 1,
            'subscriber_id' => 1,
            'organization_id' => 1,
        ];
    }
}
