<?php

namespace Tests\routes;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RouteAvailabilityTest extends TestCase
{
    use RefreshDatabase;

    public function test_public_routes_are_accessible(): void
    {
        $routes = [
            route('home') => 200,
            route('imprint') => 200,
            route('privacy') => 200,
            route('terms-of-use') => 200,
            route('support') => 200,
            route('login-faq') => 302,
            route('projects') => 200,
            route('association-member-import') => 200,
        ];

        foreach ($routes as $route => $status) {
            $response = $this->get($route);
            $response->assertStatus($status);
        }
    }
}
