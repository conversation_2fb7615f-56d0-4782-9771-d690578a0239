<?php

return [

    /*
    |--------------------------------------------------------------------------
    | OIDC Provider Config
    |--------------------------------------------------------------------------
    |
    | This options to pass to OpenIDConnectClient\OpenIDConnectProvider.
    | `redirectUri` will be determined automatically by `callback_route` below.
    | `urlResourceOwnerDetails` is unused by us.
    | `publicKey` is in PEM format, either the content or `file://` to read
    |  from file.
    |
     */
    'use_rise_idp' => env('USE_RISE_IDP', false),

    'provider' => [
        'clientId' => env('USE_RISE_IDP', false) ? env('RISE_CLIENT_ID') : env('KEYCLOAK_CLIENT_ID'),
        'clientSecret' => env('USE_RISE_IDP', false) ? env('RISE_CLIENT_SECRET') : env('KEYCLOAK_CLIENT_SECRET'),
        'clientIdRealmManagement' => env('USE_RISE_IDP', false) ? env('RISE_USER_MANAGEMENT_CLIENT_ID') : env('KEYCLOAK_CLIENT_ID'),
        'clientSecretRealmManagement' => env('USE_RISE_IDP', false) ? env('RISE_USER_MANAGEMENT_CLIENT_SECRET') : env('KEYCLOAK_CLIENT_SECRET'),
        'idTokenIssuer' => env('USE_RISE_IDP', false) ? env('RISE_ID_TOKEN_ISSUER') : env('KEYCLOAK_ID_TOKEN_ISSUER'),
        'adminUrl' => env('USE_RISE_IDP', false) ? env('RISE_ADMIN_URL') : env('KEYCLOAK_ADMIN_URL'),
        'urlAuthorize' => env('USE_RISE_IDP', false) ? env('RISE_AUTHORIZE_URL') : env('KEYCLOAK_AUTHORIZE_URL'),
        'baseUrl' => env('USE_RISE_IDP', false) ? env('RISE_ID_TOKEN_ISSUER') : env('KEYCLOAK_ID_TOKEN_ISSUER'),
        'accountConsoleUrl' => (env('USE_RISE_IDP', false) ? env('RISE_ID_TOKEN_ISSUER') : env('KEYCLOAK_ID_TOKEN_ISSUER')).'/account',
        'urlAccessToken' => env('USE_RISE_IDP', false) ? env('RISE_ACCESS_TOKEN_URL') : env('KEYCLOAK_ACCESS_TOKEN_URL'),
        'urlLogout' => env('USE_RISE_IDP', false) ? env('RISE_LOGOUT_URL') : env('KEYCLOAK_LOGOUT_URL'),
        'urlResourceOwnerDetails' => env('USE_RISE_IDP', false) ? env('RISE_USER_INFO_URL') : env('KEYCLOAK_USER_INFO_URL'),
        'scopes' => ['openid'],
        'publicKey' => 'file://'.(env('USE_RISE_IDP', false) ? env('RISE_KEY_FILE_NAME', 'rise.pem') : storage_path(env('KEYCLOAK_KEY_FILE_NAME', 'keycloak.pem'))),
    ],

    'management' => [
        'userProfileUrl' => env('RISE_USER_PROFILE_URL'),
    ],

    'public_key' => [
        'rise' => [
            'certs_url' => env('RISE_CERTS_URL'),
            'file_path' => env('RISE_KEY_FILE_NAME'),
        ],
        'keycloak' => [
            'certs_url' => env('KEYCLOAK_CERTS_URL'),
            'file_path' => storage_path(env('KEYCLOAK_KEY_FILE_NAME')),
        ],
    ],

    'user-uuids' => [
        'user' => 'c238f58b-56e6-4820-bc45-d2ee761c1cbf',
        'assoc' => '4c563fce-2d21-4611-9de7-a991b1a913b1',
        'qa+10' => '51c5a65e-e4a3-43b6-a2fc-c20a95b766c7',
        'qa+10-mitarbeiter-1' => '25ca7178-ac90-4ec2-bf97-d3d81500c1ac',
        'qa+10-mitarbeiter-2' => 'f9ceb033-e563-474f-95ec-948a026a4eb2',
        'qa+10-mitarbeiter-3' => '774cac87-4584-42ee-b2a2-733eab17883d',
        'qa+11' => '57ca2a64-1fee-4ed2-abaf-a9576b04c02a',
        'qa+12' => '55d9ae4c-6d34-4505-99fb-9b3f5b7f9328',
        'qa+13' => '56a4834f-14d7-4d10-b4f6-b5a0e390137a',
        'qa+14' => '3e3a234d-bd59-45a0-b300-abb31074606c',
        'qa+15' => '7a7fdf65-b900-4c30-aa20-0dee86cd687e',
        'qa+16' => '3baa0cbd-ecb0-4105-b228-35430fc4da6d',
        'qa+17' => 'ec050ee2-1b25-4ca8-8985-ccb788df425b',
        'qa+18' => '89e3187f-1fee-4a44-b2b5-a008036ee7aa',
        'qa+20' => '41e3d0c3-6881-49a4-8b36-09dbe2f4046d',
        'qa+21' => '96a64a90-ad08-4e32-bd3a-76aeb1126210',
        'qa+22' => 'f7a7974b-2e8b-4a3c-8198-a3145278954d',
        'qa+24' => 'a2a1e133-bc93-4d53-b310-357909368aab',
        'qa+25' => 'e10a6d26-887b-11ee-b9d1-0242ac120002',
        'qa+MitarbeiterNichtVerband' => 'c627fa57-4969-446c-ad88-2d66f0bb5861',
    ],

    /*
     * @see ModifyUserErrorCodesEnum
     */
    'createUserPossibleErrorCodes' => [
        '100' => ':attribute fehlt.',
        '101' => ':attribute ist zu lang.',
        '102' => ':attribute ist ungültig.',
        '103' => ':attribute ist keine gültige E-Mail-Adresse.',
        '104' => ':attribute ist keine gültige Passwortverschlüsselung.',
        '105' => ':attribute ist keine gültige Gruppe.',
        '106-email' => 'Mail-Adresse als GEDISA Account bereits vorhanden.',
        '106-uuid' => 'Es existiert bereits ein Konto mit dieser UUID.',
        '107' => ':attribute ist keine gültige UUID.',
        '108' => ':attribute ist kein gültiges Datum (ISO 8601).',
        '109' => ':attribute besteht nur aus Leerzeichen.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Callback Route
    |--------------------------------------------------------------------------
    |
    | Callback route used by Authorization Code flow.
    |
     */
    'callback_route' => '/oidc/callback',

    /*
    |--------------------------------------------------------------------------
    | Authenticatable Factory
    |--------------------------------------------------------------------------
    |
    | Factory to get a Illuminate\Contracts\Auth\Authenticatable to use, see
    | LaravelOIDCAuth\UserFactoryInterface.
    | For example, you can use a Eloquent model as Authenticatable to store
    | user information in DB.
    | A OpenIDConnectClient\AccessToken will be passed to authenticable()
    |
     */
    'authenticatable_factory' => \LaravelOIDCAuth\UserFactory::class,

    /*
    |--------------------------------------------------------------------------
    | Required Claims
    |--------------------------------------------------------------------------
    |
    | JWT claims in id_token required to authenticate. Arrays set required
    | elements in an array. Other values are matched exactly.
    |
    | This can also be a Closure to check id_token. id_token will be passed as
    | the first parameter. Return true to indicate a pass or false for a fail.
    |
     */
    'required_claims' => [
        // 'name' => 'value',
        // 'array' => ['required', 'elements'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Redirect URL after Authentication
    |--------------------------------------------------------------------------
    |
    | The callback route, after successful authentication, redirects user to
    | previously intended location. This is set when user is redirected from
    | Authenticate middleware of Laravel and this library.
    |
    | This config sets the default URL when the intended URL was not set, which
    | may happen, like, when you, instead of using Authenticate middleware of
    | this library, build an intermediate login page that leads the user to
    | OIDC Authorization Endpoint, and user manually visit that login page.
    |
     */
    'default_redirect_after_auth' => '/dashboard',
];
