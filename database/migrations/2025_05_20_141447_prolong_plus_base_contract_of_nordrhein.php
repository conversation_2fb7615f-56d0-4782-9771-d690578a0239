<?php

use App\Association;
use App\Enums\AssociationEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $association = Association::findOrFail(AssociationEnum::APOTHEKERVERBAND_NORDRHEIN_E_V);

        $association->associationFrameworkContractHistories()
            ->where('ends_at', Carbon::parse('31.12.2025'))
            ->update([
                'ends_at' => Carbon::parse('31.12.2026'),
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
