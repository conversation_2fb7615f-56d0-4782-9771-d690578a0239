<?php

use App\Console\Commands\CustomMigrationRepository;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        // TODO: There is a shouldRun() method in laravel 12.
        // Refactor this because it's a much nicer solution.
        if ($this->thereAreAlreadyMigratedMigrations()) {
            return;
        }

        $sqliteDumpPath = database_path('dumps/database-sqlite.sql');
        $defaultDumpPath = database_path('dumps/database-mysql.sql');

        $isTesting = App::environment('testing');
        $driver = DB::getDriverName();

        $dumpPath = ($isTesting && $driver === 'sqlite')
            ? $sqliteDumpPath
            : $defaultDumpPath;

        if (! file_exists($dumpPath)) {
            throw new RuntimeException("SQL-Dump-File not found: $dumpPath");
        }

        $sql = file_get_contents($dumpPath);

        if (! $sql) {
            throw new RuntimeException("SQL-Dump-File not readable: $dumpPath");
        }

        if ($isTesting && $driver === 'sqlite') {
            DB::statement('PRAGMA foreign_keys = OFF;');
        }

        DB::unprepared($sql);

        if ($isTesting && $driver === 'sqlite') {
            DB::statement('PRAGMA foreign_keys = ON;');
        }
    }

    private function thereAreAlreadyMigratedMigrations(): bool
    {
        return DB::table('migrations')
            ->whereNotIn('migration', CustomMigrationRepository::$migrationsConsideredAsRun)
            ->exists();
    }
};
