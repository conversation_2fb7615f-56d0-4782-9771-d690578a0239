<?php

namespace Database\Factories;

use App\Domains\Association\Domain\Enums\MemberImportStatus;
use App\MemberImport;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<MemberImport>
 */
class MemberImportFactory extends Factory
{
    protected $model = MemberImport::class;

    public function definition(): array
    {
        return [
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'email' => fake()->safeEmail(),
            'data' => [],
            'status' => fake()->randomElement(MemberImportStatus::cases()),
        ];
    }
}
