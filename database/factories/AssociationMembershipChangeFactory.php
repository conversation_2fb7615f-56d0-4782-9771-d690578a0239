<?php

namespace Database\Factories;

use App\AssociationMembershipChange;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class AssociationMembershipChangeFactory extends Factory
{
    protected $model = AssociationMembershipChange::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'association_id_before' => 1,
            'association_id_after' => null,
            'mode' => AssociationMembershipChangeModeEnum::DELETE,
            'change_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
