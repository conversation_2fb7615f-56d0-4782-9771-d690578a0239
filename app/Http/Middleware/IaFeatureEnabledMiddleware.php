<?php

namespace App\Http\Middleware;

use App\Features\IhreApotheken;
use Closure;
use Illuminate\Http\Request;
use Laravel\Pennant\Feature;

class IaFeatureEnabledMiddleware
{
    // @phpstan-ignore-next-line
    public function handle(Request $request, Closure $next)
    {
        if (! Feature::for('app')->active(IhreApotheken::class)) {
            abort(404);
        }

        return $next($request);
    }
}
