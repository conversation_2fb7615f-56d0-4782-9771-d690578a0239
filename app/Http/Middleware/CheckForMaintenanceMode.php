<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode as Middleware;

class CheckForMaintenanceMode extends Middleware
{
    /**
     * The URIs that should be reachable while maintenance mode is enabled.
     *
     * @var array
     */
    protected $except = [
        'api/v1/users/chat-token',
        'api/v1/users/pharmacies-token',
        'api/v1/services/token/chat',
        'api/v1/services/token/pharmacies',
        'api/kim/status-update',
        'stripe/webhook',
        'public/pharmacy/*',
    ];
}
