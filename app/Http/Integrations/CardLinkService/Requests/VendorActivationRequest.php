<?php

namespace App\Http\Integrations\CardLinkService\Requests;

use App\Pharmacy;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class VendorActivationRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(
        protected string $id,
        protected Pharmacy $pharmacy,
        protected bool $active = false,
    ) {}

    public function resolveEndpoint(): string
    {
        return sprintf(
            '/api/v1/channels/%s/pharmacies/%s/activation',
            $this->id,
            $this->pharmacy->gedisaId?->id,
        );
    }

    /**
     * @return array<string, bool>
     */
    protected function defaultBody(): array
    {
        return [
            'active' => $this->active,
        ];
    }
}
