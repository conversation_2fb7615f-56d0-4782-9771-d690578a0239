<?php

namespace App\Http\Integrations\NNF\Requests;

use App\Data\NNF\ConfigData;
use App\Enums\NNF\NNFType;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use <PERSON>oon\Traits\Body\HasJsonBody;

class UpdatePharmacyConfigRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    protected string $version = 'v1.2.0';

    public function __construct(
        protected string $id,
        protected ConfigData $data,
    ) {}

    public function resolveEndpoint(): string
    {
        return sprintf(
            '/%s/client/%s/configuration/comtype',
            $this->version,
            $this->id
        );
    }

    /**
     * @return array<string, array<string, mixed>>
     */
    protected function defaultBody(): array
    {
        return [
            'meta' => $this->data->meta?->toArray() ?? [],
            'data' => array_merge($this->data->data?->toArray() ?? [], [
                'type' => NNFType::CCT->value,
            ]),
        ];
    }
}
