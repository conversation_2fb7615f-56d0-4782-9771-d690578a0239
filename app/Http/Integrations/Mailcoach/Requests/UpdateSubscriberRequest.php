<?php

namespace App\Http\Integrations\Mailcoach\Requests;

use App\Http\Integrations\Mailcoach\Dto\SubscriberPayload;
use App\User;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use <PERSON>oon\Traits\Body\HasJsonBody;

// TODO Alex: sync or append tags?
// https://www.mailcoach.app/api-documentation/endpoints/subscribers/#content-update-a-subscriber
class UpdateSubscriberRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::PATCH;

    public function __construct(
        protected User $user,
        protected string $subscriberId,
    ) {}

    public function resolveEndpoint(): string
    {
        return sprintf('/subscribers/%s', $this->subscriberId);
    }

    /**
     * @return array<string, mixed>
     */
    protected function defaultBody(): array
    {
        return SubscriberPayload::fromUser($this->user)
            ->toArray();
    }
}
