<?php

namespace App\Http\Integrations\Mailcoach\Requests;

use Saloon\Enums\Method;
use Saloon\Http\Request;

class DeleteSubscriberRequest extends Request
{
    protected Method $method = Method::DELETE;

    public function __construct(
        public string $subscriberId,
    ) {}

    public function resolveEndpoint(): string
    {
        return sprintf('/subscribers/%s', $this->subscriberId);
    }
}
