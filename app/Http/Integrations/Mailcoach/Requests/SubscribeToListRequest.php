<?php

namespace App\Http\Integrations\Mailcoach\Requests;

use App\Http\Integrations\Mailcoach\Dto\SubscriberPayload;
use App\User;
use Saloon\Contracts\Body\HasBody;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Traits\Body\HasJsonBody;

class SubscribeToListRequest extends Request implements HasBody
{
    use HasJsonBody;

    protected Method $method = Method::POST;

    public function __construct(
        protected User $user,
        protected string $list,
    ) {}

    public function resolveEndpoint(): string
    {
        return sprintf('/email-lists/%s/subscribers', $this->list);
    }

    /**
     * @return array<string, mixed>
     */
    protected function defaultBody(): array
    {
        return SubscriberPayload::fromUser($this->user)
            ->toArray();
    }
}
