<?php

namespace App\Http\Integrations\Mailcoach\Requests;

use App\Data\Mailcoach\SubscriberFilterData;
use App\Settings\MailcoachSettings;
use <PERSON>oon\Enums\Method;
use Saloon\Http\Request;

class GetSubscriberRequest extends Request
{
    protected Method $method = Method::GET;

    public function __construct(
        protected ?SubscriberFilterData $data = null,
    ) {}

    public function resolveEndpoint(): string
    {
        return sprintf('/email-list/%s/subscribers', MailcoachSettings::list());
    }

    protected function defaultQuery(): array
    {
        return $this->data?->toFilter() ?? [];
    }
}
