<?php

namespace App\Http\Integrations\Mailcoach\Dto;

use App\User;
use <PERSON><PERSON>\LaravelData\Attributes\MapName;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class SubscriberPayload extends Data
{
    public function __construct(
        public ?string $email,
        public ?string $firstName,
        public ?string $lastName,
        /** @var array<string, mixed> */
        public array $extraAttributes,
        /** @var array<string> */
        public array $tags,
        public bool $SkipConfirmation = true,
        public bool $skipWelcomeMail = true,
    ) {}

    public static function fromUser(User $user): self
    {
        return new self(
            email: $user->email,
            firstName: $user->first_name,
            lastName: $user->last_name,
            extraAttributes: [
                'gender' => $user->salutation,
                'title' => $user->title,
            ],
            tags: self::getTagsForUser($user),
        );
    }

    // TODO: How to handle tags correctly? sync vs. append!?
    /** @return list<string> */
    private static function getTagsForUser(User $user): array
    {
        $tags = [];

        if ($user->isPharmacyUser()) {
            $tags[] = 'Apothekenaccount';

            if ($user->isOwner()) {
                $tags[] = 'Inhaber';
                if ($user->pharmacyProfile?->association) {
                    $tags[] = $user->pharmacyProfile->association->name;
                } else {
                    $tags[] = 'Verbandslos';
                }
            } else {
                if ($user->pharmacies->count()) {
                    $tags[] = 'Mitarbeiter';
                    $pharmacy = $user->pharmacies->first();

                    if ($pharmacy) {
                        if ($pharmacy->owner()?->pharmacyProfile?->association) {
                            $tags[] = $pharmacy->owner()->pharmacyProfile->association->name;
                        } else {
                            $tags[] = 'Verbandslos';
                        }
                    }
                }
            }
        }

        if ($user->isAssociationUser()) {
            $tags[] = 'Verbandsaccount';
            if ($user->association()) {
                $tags[] = $user->association()->name;
                $tags[] = $user->associations->first()?->pivot->role_name == 'admin' ? 'Verbandsadministrator' : 'Verbandsmitarbeiter';
            }
        }

        return $tags;
    }
}
