<?php

namespace App\Http\Requests\Newsletter;

use App\Enums\Newsletter\WebhookEvent;
use App\Settings\MailcoachSettings;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;
use InvalidArgumentException;

class WebhookRequest extends FormRequest
{
    public function authorize(): bool
    {
        $signature = $this->header('Signature');

        $payload = json_encode($this->input());

        if ($payload === false) {
            throw new InvalidArgumentException('Payload is malformed.');
        }

        $secret = MailcoachSettings::webhookSecret();

        return $signature === hash_hmac('sha256', $payload, $secret);
    }

    /**
     * @return array<string, array<string|Enum>>
     */
    public function rules(): array
    {
        return [
            'event' => ['required', 'string', new Enum(WebhookEvent::class)],
            'email_list_uuid' => ['required', 'string'],
            'email' => ['required', 'email'],
        ];
    }
}
