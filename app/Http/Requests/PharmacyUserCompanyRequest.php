<?php

namespace App\Http\Requests;

use App\Actions\Users\CreateDatabaseUserAction;
use App\Actions\Users\CreateUserAction;
use App\Actions\Users\UpdateUserAction;
use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\Rules\GedisaMail;
use App\User;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use RuntimeException;

class PharmacyUserCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $user = $this->route('user') ?? new User;

        assert($user instanceof User);

        $rules = [
            'salutation' => [
                'required',
                'string',
            ],
            'title' => [
                'nullable',
                'string',
                'max:100',
            ],
            'first_name' => [
                'required',
                'string',
                'max:250',
            ],
            'last_name' => [
                'required',
                'string',
                'max:250',
            ],
            'differing_notifications_email_enabled' => [
                'nullable',
                'boolean',
            ],
            'login_email' => [
                Rule::requiredIf(! $user->exists),
                app(GedisaMail::class),
            ],
            'phone' => [
                'required',
                'string',
                'max:100',
            ],
            'pharmacies' => [
                'array',
                'nullable',
            ],
        ];

        if (($user->exists && $user->email) || $this->input('differing_notifications_email_enabled')) {
            $rules['notifications_email'] = [
                'required',
                app(GedisaMail::class),
            ];
        }

        return $rules;
    }

    public function createCompanyUser(): void
    {
        $validated = $this->validated();

        /** @var CreateUserAction $createUserAction */
        $createUserAction = app(CreateUserAction::class);

        $createUserAction
            ->setSalutation($validated['salutation'])
            ->setTitle($validated['title'])
            ->setFirstName($validated['first_name'])
            ->setLastName($validated['last_name'])
            ->setLoginEmail($validated['login_email'])
            ->setNotificationsEmail($validated['notifications_email'] ?? $validated['login_email'])
            ->setPhone($validated['phone'])
            ->setPharmacyUser(['company_user_id' => user()->id])
            ->setPharmacies(Pharmacy::whereIn('id', $validated['pharmacies'] ?? [])->get(), PharmacyRoleEnum::SUB_OWNER)
            ->createUser();
    }

    public function updateCompanyUser(User $user): void
    {
        $validated = $this->validated();
        /** @var CreateDatabaseUserAction $userCreatorAction */
        $userCreatorAction = app(CreateDatabaseUserAction::class);
        $updateUserAction = app(UpdateUserAction::class)->setUser($user);
        $loginEmail = $validated['login_email'] ?? null;
        $notificationEmail = $validated['notifications_email'] ?? null;

        unset($validated['login_email'], $validated['notifications_email']);

        $updateUserAction->setData(
            [
                'email' => $notificationEmail ?? $loginEmail,
            ] + $validated
        );

        if ($loginEmail && ! $user->email) {
            $updateUserAction->shouldBeCreatedAtIdp(true, $loginEmail);
        }

        DB::beginTransaction();

        try {
            $updateUserAction->execute();

            if (! (user() instanceof User)) {
                throw new RuntimeException('User not found');
            }

            /** @var array<int> $validatedPharmacies */
            $validatedPharmacies = $validated['pharmacies'] ?? [];

            $userCreatorAction->setUser($user)
                ->syncCompanyUserPharmacies(
                    user(),
                    $validatedPharmacies
                );
        } catch (\Exception $e) {
            DB::rollBack();

            throw $e;
        }
        DB::commit();
    }

    protected function failedValidation(Validator $validator): void
    {
        notify(__('notifications.company-user.validation_error'), 'error');
        parent::failedValidation($validator);
    }
}
