<?php

namespace App\Http\Requests;

use App\Actions\Users\CreateUserAction;
use App\Actions\Users\UpdateUserAction;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Rules\GedisaMail;
use App\User;
use Exception;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\MessageBag;
use Illuminate\Validation\Rule;

class PharmacyUserRequest extends FormRequest
{
    private ?MessageBag $errors = null;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): true
    {
        return true;
    }

    public function rules(): array
    {
        $user = $this->route('user') ?? new User;
        $pharmacy = $this->route('pharmacy');

        assert($user instanceof User);
        assert($pharmacy instanceof \App\Pharmacy);

        $rules = [
            'salutation' => [
                'required',
                'string',
            ],
            'title' => [
                'nullable',
                'string',
                'max:100',
            ],
            'first_name' => [
                'required',
                'string',
                'max:250',
            ],
            'last_name' => [
                'required',
                'string',
                'max:250',
            ],
            'differing_notifications_email_enabled' => [
                'nullable',
                'boolean',
            ],
            'chooseApomail' => [
                'nullable',
                'boolean',
            ],
            'login_email' => [
                Rule::requiredIf(! $user->exists || ! $user->idp_email),
                app(GedisaMail::class),
            ],
            'phone' => [
                'required',
                'string',
                'max:100',
            ],
            'weekly_working_hours' => [
                'nullable',
                'numeric',
                'min:0',
                'max:168',
            ],
            'permissions' => [
                'nullable',
                'array',
            ],
            'role' => [
                Rule::requiredIf(count(PharmacyRoleEnum::getAssignable($pharmacy)) > 1),
            ],
        ];

        if (($user->exists && $user->email) || $this->boolean('differing_notifications_email_enabled')) {
            $rules['notifications_email'] = [
                'required',
                app(GedisaMail::class)->allowApomail(),
            ];
        }
        if (! $user->exists && $this->boolean('choose_apomail_enabled')) {
            $rules['login_email'] = [
                'required',
                app(GedisaMail::class)->allowApomail(),
            ];
        }

        return $rules;
    }

    public function createPharmacyUser(\App\Pharmacy $pharmacy): ?User
    {
        $validated = $this->validated();
        [$role, $permissions] = $this->getSelectedRoleAndPermissions($validated);

        /** @var CreateUserAction $createUserAction */
        $createUserAction = app(CreateUserAction::class);

        $user = $createUserAction
            ->setSalutation($validated['salutation'])
            ->setTitle($validated['title'])
            ->setFirstName($validated['first_name'])
            ->setLastName($validated['last_name'])
            ->setLoginEmail($validated['login_email'])
            ->setNotificationsEmail($validated['notifications_email'] ?? $validated['login_email'])
            ->setPhone($validated['phone'])
            ->setWeeklyWorkingMinutes($this->weeklyWorkingHoursToMinutes($validated['weekly_working_hours'] ?? null))
            ->setPharmacyUser()
            ->setPharmacies(collect([$pharmacy]), $role, $permissions ?? [])
            ->createUser();

        return $user;
    }

    public function updatePharmacyUser(\App\Pharmacy $pharmacy, User $user): void
    {
        $validated = $this->validated();
        [$role, $permissions] = $this->getSelectedRoleAndPermissions($validated);
        $validated['email'] = $validated['notifications_email'] ?? $validated['login_email'];

        $action = app(UpdateUserAction::class)->setUser($user);
        $loginEmail = $validated['login_email'] ?? null;
        $notificationEmail = $validated['notifications_email'] ?? null;
        $weeklyWorkingMinutes = $this->weeklyWorkingHoursToMinutes($validated['weekly_working_hours'] ?? null);

        unset($validated['login_email']);
        unset($validated['notifications_email']);
        unset($validated['weekly_working_hours']);

        $action->setData([
            'email' => $notificationEmail ?? $loginEmail,
            'weekly_working_minutes' => $weeklyWorkingMinutes,
        ] + $validated);

        if ($loginEmail && ! $user->email) {
            $action->shouldBeCreatedAtIdp(true, $loginEmail);
        }

        DB::beginTransaction();

        try {
            $action->execute();

        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
        DB::commit();

        $validated['username'] = null;
        $user->update($validated);

        $pharmacy->reassignUser($user, $role, $permissions);
    }

    private function getSelectedRoleAndPermissions($validated): array
    {
        $permissions = $validated['permissions'] ?? [];

        $role = $validated['role'] ?? PharmacyRoleEnum::EMPLOYEE;

        if ($role === PharmacyRoleEnum::EMPLOYEE) {
            $permissions = array_intersect($permissions, PharmacyPermissionsEnum::getAllForEmployee());
        }

        return [$role, $permissions];
    }

    protected function failedValidation(Validator $validator)
    {
        notify(__('notifications.employee.validation_error'), 'error');
        parent::failedValidation($validator);
    }

    protected function weeklyWorkingHoursToMinutes(mixed $weeklyWorkingHours): ?int
    {
        if (is_string($weeklyWorkingHours)) {
            $weeklyWorkingHours = floatval(str_replace(',', '.', $weeklyWorkingHours));
        }

        if (! is_numeric($weeklyWorkingHours)) {
            return null;
        }

        return intval($weeklyWorkingHours * 60);
    }
}
