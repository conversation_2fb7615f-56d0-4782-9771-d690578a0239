<?php

namespace App\Http\Controllers;

use App\News;
use Artesaos\SEOTools\Facades\JsonLd;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\TwitterCard;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;

class PageController extends Controller
{
    public function home(): View
    {
        // SEOMeta::setTitle('');
        SEOMeta::setDescription('Registrieren Sie sich mit Ihrer Apotheke jetzt, um teilzunehmen.');

        OpenGraph::setTitle(SEOMeta::getTitle());
        OpenGraph::setDescription(SEOMeta::getDescription());
        OpenGraph::addImage(asset('assets/img/mein-apothekenportal.jpg'));

        TwitterCard::setTitle(SEOMeta::getTitle());
        TwitterCard::setDescription(SEOMeta::getDescription());
        TwitterCard::setImage(asset('assets/img/mein-apothekenportal.jpg'));

        JsonLd::setTitle(SEOMeta::getTitle());
        JsonLd::setDescription(SEOMeta::getDescription());
        JsonLd::setImage(asset('assets/img/mein-apothekenportal.jpg'));

        return view('home', [
            'news' => News::public()->orderByDesc('release_date')->paginate(4),
        ]);
    }

    public function imprint(): View
    {
        SEOMeta::setTitle('Impressum');
        SEOMeta::setRobots('noindex,noarchive');

        OpenGraph::setTitle(SEOMeta::getTitle());

        TwitterCard::setTitle(SEOMeta::getTitle());

        JsonLd::setTitle(SEOMeta::getTitle());

        return view('imprint');
    }

    public function privacy(): View
    {
        SEOMeta::setTitle('Datenschutzerklärung');
        SEOMeta::setRobots('noindex,noarchive');

        OpenGraph::setTitle(SEOMeta::getTitle());

        TwitterCard::setTitle(SEOMeta::getTitle());

        JsonLd::setTitle(SEOMeta::getTitle());

        return view('privacy');
    }

    public function support(): View
    {
        SEOMeta::setTitle('Support');

        OpenGraph::setTitle(SEOMeta::getTitle());

        TwitterCard::setTitle(SEOMeta::getTitle());

        JsonLd::setTitle(SEOMeta::getTitle());

        return view('support');
    }

    public function LoginFaq()
    {
        return redirect()->route('support');
    }

    public function projects(): View
    {
        SEOMeta::setTitle('Projekte');

        OpenGraph::setTitle(SEOMeta::getTitle());

        TwitterCard::setTitle(SEOMeta::getTitle());

        JsonLd::setTitle(SEOMeta::getTitle());

        return view('projects');
    }

    public function termsOfUse(): View
    {
        SEOMeta::setTitle('Nutzungsbedingungen');

        OpenGraph::setTitle(SEOMeta::getTitle());

        TwitterCard::setTitle(SEOMeta::getTitle());

        JsonLd::setTitle(SEOMeta::getTitle());

        return view('terms-of-use');
    }

    public function associationMemberImport()
    {
        return response()->file(Storage::disk('intern')->path('association-member-import.xlsx'));
    }

    public function covidCertificateInformation(): View
    {
        SEOMeta::setTitle(trans('messages.covid-certificate-information'));

        OpenGraph::setTitle(SEOMeta::getTitle());

        TwitterCard::setTitle(SEOMeta::getTitle());

        JsonLd::setTitle(SEOMeta::getTitle());

        return view('covid-certificate-information');
    }
}
