<?php

namespace App\Http\Controllers;

use App\Enums\FlashMessageTypeEnum;
use App\Enums\SalutationEnum;
use App\Helper\AssociationsHelper;
use App\Http\Requests\UpdateUser;
use App\User;
use Artesaos\SEOTools\Facades\SEOMeta;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;

class UserController extends Controller
{
    public function edit(): View
    {
        $user = \user();

        SEOMeta::setTitle('Account Einstellungen');

        $association = $user->pharmacyProfile ? $user->pharmacyProfile->association : null;

        $view = $user->isCompany() ? 'user.editCompany' : 'user.edit';

        return view($view, [
            'user' => $user,
            'association' => $association,
            'allAssociations' => AssociationsHelper::generateDropdownArray(),
            'currentlyChangingEmail' => Cache::get('emailChanges.user.'.\user()->id),
        ]);
    }

    /**
     * @param  UpdateUser  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws ValidationException|Exception
     */
    public function ensureCompanyDataUpdate(Request $request)
    {
        $user = user();
        $data = $this->validate($request, [
            'salutation' => ['nullable', Rule::in(SalutationEnum::getKeys())],
            'title' => ['nullable', 'string', 'max:255'],
            'name' => ['required', 'string', 'max:255'],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:255'],
        ]);

        DB::beginTransaction();

        try {
            $user->update($data);
            $user->companyUser->update([
                'name' => $data['name'],
            ]);

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            throw $exception;
        }

        notify(__('notifications.account.updated'));

        return redirect()->route('ensure-company-data');
    }

    public function idpEmailChange()
    {
        $claims = session()->get('oidc-auth.access_token')->getIdToken()->claims();

        if ($claims->get('email') === user()->idp_email) {
            return redirect('dashboard');
        }

        return view('user.check-for-idp-email-change', [
            'oidcIdTokenClaims' => $claims,
        ]);
    }

    public function setIdpEmailAddress(Request $request)
    {
        $claims = session()->get('oidc-auth.access_token')->getIdToken()->claims();

        $idpMail = $claims->get('email');

        $user = user();
        assert($user instanceof User);

        $user->update(['idp_email' => $idpMail]);

        if ($request->has('yes')) {
            try {
                $user->update(['email' => $idpMail]);
            } catch (Exception $e) {
                report($e);
                notify('Ihre neue E-Mail-Adresse für Benachrichtigungen konnte aufgrund eines Fehlers nicht gespeichert werden. Versuchen Sie es später erneut.', FlashMessageTypeEnum::ERROR->value, seconds: 10);

                return redirect(route('dashboard'));
            }
        }

        notify('Ihre Auswahl wurde gespeichert.');

        return redirect(route('dashboard'));
    }

    public function contracts(): View
    {
        $user = user();

        SEOMeta::setTitle('Vertragsunterlagen');

        return view('user.contracts', [
            'user' => $user,
        ]);
    }
}
