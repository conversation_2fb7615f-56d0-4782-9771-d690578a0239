<?php

namespace App\Http\Controllers\Api;

use App\Domains\Subscription\Application\FeatureAccess\DigitalRepresentationFeatureAccess;
use App\Enums\PharmacyAddressTypeEnum;
use App\Enums\PharmacyRoleEnum;
use App\Http\Resources\PharmacyAddressResource;
use App\PharmacyAddress;
use App\QueryBuilder\BusinessHourOpenFilter;
use App\QueryBuilder\DistanceFilter;
use App\QueryBuilder\FocusAreaFilter;
use App\QueryBuilder\LanguageFilter;
use App\QueryBuilder\PharmaceuticalServiceFilter;
use App\Settings\AppSettings;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class PharmacyAddressController extends ClientCredentialsController
{
    public function index(Request $request, AppSettings $appSettings)
    {
        $addresses = QueryBuilder::for(PharmacyAddress::class);

        if ($this->getBooleanFilterValue('corona_rapid_test') && $this->getBooleanFilterValue('vaccination_import')) {
            $addresses = $addresses->whereHas('pharmacy', function (Builder $q) {
                return $q->where('corona_rapid_test', true)
                    ->where('vaccination_import', true);
            });
        } elseif ($this->getBooleanFilterValue('corona_rapid_test')) {
            $addresses = $addresses
                ->where('type', PharmacyAddressTypeEnum::HOME)
                ->whereHas('pharmacy', fn (Builder $q) => $q->where('corona_rapid_test', true));
        } elseif ($this->getBooleanFilterValue('vaccination_import')) {
            $addresses = $addresses
                ->whereHas('pharmacy', function (Builder $q) {
                    return $q->where('vaccination_import', true);
                })
                ->where(function (Builder $q) {
                    return $q
                        ->where('type', PharmacyAddressTypeEnum::VACCINATION_IMPORT)
                        ->orWhereHas('pharmacy', function (Builder $q) {
                            return $q->whereDoesntHave('pharmacyAddresses', function (Builder $q) {
                                return $q->where('type', PharmacyAddressTypeEnum::VACCINATION_IMPORT);
                            });
                        });
                });
        }

        $addresses = $addresses->allowedFilters([ // @phpstan-ignore-line
            AllowedFilter::exact('pharmacy.has_near_parking_space'),
            AllowedFilter::exact('pharmacy.courier_service'),
            AllowedFilter::exact('pharmacy.does_influenza_vaccination'),
            AllowedFilter::exact('pharmacy.does_covid_vaccination'),
            AllowedFilter::exact('pharmacy.accepts_e_prescription'),
            AllowedFilter::exact('pharmacy.calendarTopics.id'),
            AllowedFilter::custom('pharmacy.near', new DistanceFilter),
            AllowedFilter::custom('focus_areas', new FocusAreaFilter),
            AllowedFilter::custom('pharmaceuticalServiceTypes', new PharmaceuticalServiceFilter),
            AllowedFilter::custom('open', new BusinessHourOpenFilter),
            AllowedFilter::custom('language', new LanguageFilter),
        ])
            ->whereHas(
                'pharmacy',
                fn (Builder $q) => $q->where('show_in_apoguide', true)
                    ->when(Carbon::now() > $appSettings->terms_of_use_deadline, fn (Builder $q) => $q->withFeatureAccess(DigitalRepresentationFeatureAccess::class)
                    )
            )
            ->allowedSorts(['distance', 'name'])
            ->with($this->getLazyLoadedAttributes())
            ->paginate($this->getPerPage());

        return PharmacyAddressResource::collection($addresses);
    }

    private function getBooleanFilterValue($name, bool $value = true)
    {
        return is_array(request()->get('filter')) && array_key_exists($name, request()->get('filter')) && request(
        )->get('filter')[$name] == $value;
    }

    private function getLazyLoadedAttributes(): array
    {
        return [
            'pharmacy',
            'pharmacy.businessHours',
            'pharmacy.languages',
            'pharmacy.pharmacyType',
            'pharmacy.publicTransportStations',
            'pharmacy.users' => function ($q) {
                return $q->wherePivot('role_name', '=', PharmacyRoleEnum::OWNER)->with('pharmacies');
            },
            'pharmacy.pharmacyAddresses',
            'pharmacy.pharmacyImages.pharmacy',
        ];
    }
}
