<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\OIDCUserNotFoundException;
use App\Helper\OIDCHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Scout\Jobs\MakeSearchable;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;

class ChatController extends Controller
{
    public function matrixBootstrapDone(Request $request, OIDCHelper $OIDCHelper)
    {
        $token = Configuration::forUnsecuredSigner()
            ->parser()
            ->parse($request->bearerToken());

        $request->validate([
            'matrix_bootstrap_done' => 'required',
        ]);

        try {
            $user = $OIDCHelper->getUser(
                claims: $token->claims(),
                withTrashed: true
            );
        } catch (OIDCUserNotFoundException $e) {
            return response()->json([
                'message' => 'User not found.',
            ], 404);
        }

        if ($user->deleted_at) {
            return response()->json([
                'message' => 'User is deleted.',
            ], 404);
        }

        if ($user->matrix_bootstrap_done === $request->boolean('matrix_bootstrap_done')) {
            return response()->json([
                'message' => 'success',
            ]);
        }

        $user->update([
            'matrix_bootstrap_done' => $request->boolean('matrix_bootstrap_done'),
        ]);

        dispatch(new MakeSearchable($user->pharmacies));

        return response()->json([
            'message' => 'success',
        ]);
    }
}
