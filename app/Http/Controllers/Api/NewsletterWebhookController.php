<?php

namespace App\Http\Controllers\Api;

use App\Actions\Newsletter\DeleteSubscriber;
use App\Enums\Newsletter\MailcoachList;
use App\Enums\Newsletter\WebhookEvent;
use App\Http\Controllers\Controller;
use App\Http\Requests\Newsletter\WebhookRequest;
use App\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class NewsletterWebhookController extends Controller
{
    public function __invoke(WebhookRequest $request): JsonResponse
    {
        $event = $request->enum('event', WebhookEvent::class);

        return match ($event) {
            WebhookEvent::Unsubscribed => $this->unsubscribed($request),
            default => response()->json(null, 400),
        };
    }

    protected function unsubscribed(WebhookRequest $request): JsonResponse
    {
        $email = $request->get('email');
        $emailListUuid = $request->get('email_list_uuid');

        assert(is_string($emailListUuid));

        $user = User::query()
            ->where('email', $email)
            ->firstOrFail();

        if ($user->hasMailcoachSubscriptions(MailcoachList::fromUuid($emailListUuid)) === false) {
            return response()->json(
                data: [
                    'message' => 'User is not subscribed to this list.',
                ], status: 404
            );
        }

        DB::beginTransaction();

        try {
            DeleteSubscriber::execute($user, MailcoachList::fromUuid($emailListUuid));

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

            report($exception);

            return response()->json(status: 500);
        }

        return response()->json([
            'message' => 'User has been deleted.',
        ]);
    }
}
