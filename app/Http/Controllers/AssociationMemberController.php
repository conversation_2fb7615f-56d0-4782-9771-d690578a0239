<?php

namespace App\Http\Controllers;

use App\Association;
use App\Exports\MembersExport;
use Artesaos\SEOTools\Facades\SEOMeta;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class AssociationMemberController extends Controller
{
    public function index()
    {
        $association = user()->associations->first();

        $this->authorize('administrateMembers', $association);

        SEOMeta::setTitle('Mitgliederverwaltung');

        return view('association.members.index', [
            'association' => $association,
        ]);
    }

    public function export(): BinaryFileResponse
    {
        $association = user()->associations->first();

        assert($association instanceof Association);

        return Excel::download(new MembersExport($association), str_replace(' ', '_', $association->name).'_'.now()->format('d.m.Y').'_mitglieder.xlsx');
    }
}
