<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Class Language
 *
 * @mixin IdeHelperLanguage
 */
class Language extends Model
{
    use HasFactory;

    protected $hidden = [
        'pivot',
        'created_at',
        'updated_at',
    ];

    public function pharmacies(): BelongsToMany
    {
        return $this->belongsToMany(Pharmacy::class)->using(LanguagePharmacy::class)->withTimestamps();
    }
}
