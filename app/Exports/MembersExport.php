<?php

namespace App\Exports;

use App\Association;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class MembersExport implements FromCollection, WithHeadings
{
    public function __construct(protected Association $association) {}

    public function collection(): Collection
    {
        $members = $this->association->members()
            ->withCount(['pharmacies' => function (Builder $q) {
                $q->whereHas('subscriptions', function (Builder $q) {
                    $q->active();
                });
            }])
            ->get();

        /** @phpstan-ignore-next-line */
        return $members->map(function ($member) {
            return [
                'first_name' => $member->first_name,
                'last_name' => $member->last_name,
                'email' => $member->email,
                'pharmacies_count' => $member->pharmacies_count ?? '0', // @phpstan-ignore-line
                'created_at' => $member->created_at?->format('d.m.Y'),
            ];
        });
    }

    /**
     * @return array<string>
     */
    public function headings(): array
    {
        return [
            'Vorname',
            'Nachname',
            'E-Mail',
            'Apotheken',
            'Erstellt',
        ];
    }
}
