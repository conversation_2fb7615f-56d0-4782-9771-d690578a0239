<?php

namespace App;

use App\Enums\Vaccinate\CovidVaccinationInvoiceStatus;
use App\Mail\CovidVaccinationInvoiceFinishedMail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class CovidVaccinationInvoice extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('invoice')
            ->singleFile()
            ->useDisk('covid-vaccinations-invocies');
    }

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isFinished()
    {
        return $this->status == CovidVaccinationInvoiceStatus::GENERATED;
    }

    public function finished(bool $notify = true)
    {
        $this->update([
            'status' => CovidVaccinationInvoiceStatus::GENERATED,
        ]);

        if ($notify) {
            Mail::to($this->user->routeEmailsTo)->send(new CovidVaccinationInvoiceFinishedMail($this->user, $this));
        }
    }

    public function scopeStatus(Builder $query, $status)
    {
        return $query->where('status', $status);
    }
}
