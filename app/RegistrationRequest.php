<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Nova\Actions\Actionable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class RegistrationRequest extends Model implements HasMedia
{
    use Actionable, HasFactory, InteractsWithMedia;

    private const MEDIA_DISK = 'non-association-registration-request-documents';

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('operating-licenses')
            ->useDisk(self::MEDIA_DISK);

        $this->addMediaCollection('activity-certificates')
            ->useDisk(self::MEDIA_DISK);

        $this->addMediaCollection('association-proofs')
            ->useDisk(self::MEDIA_DISK);
    }

    public function association()
    {
        return $this->belongsTo(Association::class);
    }

    public function staff()
    {
        return $this->belongsTo(Staff::class);
    }

    public function getNameAttribute()
    {
        return trim($this->title.' '.$this->first_name.' '.$this->last_name);
    }
}
