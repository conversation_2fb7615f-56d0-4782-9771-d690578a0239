<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Nova\Actions\Actionable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property int $id
 * @property string $salutation
 * @property string|null $title
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $phone
 * @property string|null $company_name
 * @property string $pharmacy_name
 * @property string $street
 * @property string $house_number
 * @property string $postcode
 * @property string $city
 * @property string $telematics_id
 * @property int|null $association_id
 * @property string $status
 * @property string|null $rejected_reason
 * @property int|null $staff_id
 * @property string|null $date_accepted
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Nova\Actions\ActionEvent> $actions
 * @property-read int|null $actions_count
 * @property-read \App\Association|null $association
 * @property-read mixed $name
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \App\Staff|null $staff
 *
 * @method static \Database\Factories\RegistrationRequestFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereAssociationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereDateAccepted($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereHouseNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest wherePharmacyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest wherePostcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereRejectedReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereSalutation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereStaffId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereStreet($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereTelematicsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RegistrationRequest whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class RegistrationRequest extends Model implements HasMedia
{
    use Actionable, HasFactory, InteractsWithMedia;

    private const MEDIA_DISK = 'non-association-registration-request-documents';

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('operating-licenses')
            ->useDisk(self::MEDIA_DISK);

        $this->addMediaCollection('activity-certificates')
            ->useDisk(self::MEDIA_DISK);

        $this->addMediaCollection('association-proofs')
            ->useDisk(self::MEDIA_DISK);
    }

    public function association()
    {
        return $this->belongsTo(Association::class);
    }

    public function staff()
    {
        return $this->belongsTo(Staff::class);
    }

    public function getNameAttribute()
    {
        return trim($this->title.' '.$this->first_name.' '.$this->last_name);
    }
}
