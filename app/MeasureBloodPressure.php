<?php

namespace App;

use App\Enums\PharmaceuticalService\MeasureBloodPressureStatus;
use App\Traits\HasLocalizedTimestamp;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

class MeasureBloodPressure extends Model
{
    use HasFactory, HasLocalizedTimestamp;

    protected $casts = [
        'reasons_to_abort' => 'array',
        'date' => 'datetime',
    ];

    protected $guarded = [];

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    protected static function booted()
    {
        static::creating(function ($measureBloodPressure) {
            $measureBloodPressure->uuid = Str::uuid();
        });
    }

    public function getLocalizedDateAttribute($value): Carbon
    {
        return $this->getLocalizedCarbonInstance($this->date);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function healthInsuranceCompany(): BelongsTo
    {
        return $this->belongsTo(HealthInsuranceCompany::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getUserNameAttribute()
    {
        $user = self::user()->first();

        return ! empty($user) ? trim($user->first_name.' '.$user->last_name) : null;
    }

    public function pharmaceutical(): BelongsTo
    {
        return $this->belongsTo(Pharmaceutical::class);
    }

    public function scopeSuccessfullyFinished(Builder $query): Builder
    {
        return $query->whereNull('reasons_to_abort')->where('status', MeasureBloodPressureStatus::FINISHED);
    }

    public function measureBloodPressurePatient(): HasOne
    {
        return $this->hasOne(MeasureBloodPressurePatient::class, 'mbp_id', 'id');
    }
}
