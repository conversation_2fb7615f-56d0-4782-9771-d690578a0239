<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserAssociationProfile
 *
 * @mixin IdeHelperUserAssociationProfile
 *
 * @property int $user_id
 * @property int|null $association_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Association|null $association
 * @property-read \App\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAssociationProfile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAssociationProfile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAssociationProfile query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAssociationProfile whereAssociationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAssociationProfile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAssociationProfile whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserAssociationProfile whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserAssociationProfile extends Model
{
    protected $primaryKey = 'user_id';

    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'association_id',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }
}
