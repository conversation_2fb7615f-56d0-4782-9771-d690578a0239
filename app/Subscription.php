<?php

namespace App;

use App\Enums\CardLink\CardLinkOrderStatusEnum;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @deprecated
 */
class Subscription extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $table = 'subscriptions_old';

    protected $casts = [
        'cycle_started_at' => 'date',
        'cycle_ends_at' => 'date',
        'started_at' => 'date',
        'ends_at' => 'date',
        'next_cancel_date' => 'date',
    ];

    /**
     * @deprecated
     */
    public array $possiblePlans = ['wl_base', 'wl_extended', 'base', 'extended', 'extern_base'];

    /**
     * @deprecated
     */
    public function subscribable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @deprecated
     */
    public function subscriptionOrders(): HasMany
    {
        return $this->hasMany(SubscriptionOrder::class);
    }

    /**
     * @deprecated
     */
    public function getTitleAttribute(): string
    {
        return config('subscription.plans.'.$this->plan.'.title');
    }

    /**
     * @deprecated
     * Returns the period of the subscription ("y" for yearly, "m" for monthly)
     */
    public function getPeriodAttribute(): string
    {
        return config('subscription.plans.'.$this->plan.'.period');
    }

    /**
     * @deprecated
     */
    public function getPricePerMonthAttribute(): int
    {
        return config('subscription.plans.'.$this->plan.'.price_per_month');
    }

    /**
     * @deprecated
     */
    public function getPricePerPeriodAttribute(): int
    {
        return config('subscription.plans.'.$this->plan.'.price');
    }

    /**
     * @deprecated
     */
    public function getNoticePeriodAttribute(): ?string
    {
        return config('subscription.plans.'.$this->plan.'.notice_period');
    }

    /**
     * @deprecated
     */
    public function getContentAttribute(): string
    {
        return config('subscription.plans.'.$this->plan.'.content');
    }

    /**
     * @deprecated
     */
    public function getActiveFromAttribute(): string
    {
        return config('subscription.plans.'.$this->plan.'.active_from');
    }

    /**
     * @deprecated
     */
    public function getActiveTillAttribute(): string
    {
        return config('subscription.plans.'.$this->plan.'.active_till');
    }

    /**
     * @deprecated
     */
    public function getNextCancelDateAttribute()
    {
        if ($this->isBelowNoticePeriod()) {
            return $this->addACycle()->cycle_ends_at;
        }

        return $this->cycle_ends_at;
    }

    /**
     * @deprecated
     */
    public function getRemainingFullMonthsAttribute(): int
    {
        $start = $this->cycle_ends_at
            ->startOfMonth();

        $end = now()
            ->addMonthNoOverflow()
            ->startOfMonth();

        return $start->lt($end) ? 0 :
            min((int) abs($start->diffInMonths($end)), 12);
    }

    /**
     * @deprecated
     */
    public function getMonthsInCurrentCycle(): int
    {
        $start = $this->cycle_ends_at
            ->addMonthNoOverflow()
            ->startOfMonth();

        $end = $this->cycle_started_at;

        return $start->lt($end) ? 0 :
            min((int) abs($start->diffInMonths($end)), 12);
    }

    /**
     * @deprecated
     * The subscription is canceled but the paid period is not over yet.
     */
    public function onGracePeriod(): bool
    {
        return $this->ends_at !== null && $this->ends_at->isFuture();
    }

    /**
     * @deprecated
     * The subscription is not canceled
     */
    public function isRecurring(): bool
    {
        return $this->ends_at === null;
    }

    /**
     * @deprecated
     * Cancels the subscription but keeps access until paid period is over
     */
    public function cancel(): void
    {
        $this->forceFill([
            'ends_at' => $this->next_cancel_date,
        ])->save();
    }

    /**
     * @deprecated
     * Cancels the subscription now and revokes access to paid products instant
     */
    public function cancelNow(): void
    {
        $this->forceFill([
            'ends_at' => now(),
        ])->save();
    }

    /**
     * @deprecated
     */
    public function addACycle(): static
    {
        $clone = clone $this;

        if ($this->next_plan) {
            $clone->forceFill([
                'next_plan' => null,
                'plan' => $this->next_plan,
            ]);
        }

        return $clone->start($this->cycle_ends_at->addDay()->startOfDay());
    }

    /**
     * @deprecated
     * reactivates the subscription
     *
     * @throws Exception
     */
    public function reactivate(): void
    {
        if (! $this->onGracePeriod()) {
            throw new Exception('Cannot reactivate a subscription that is not in grace period');
        }

        $this->forceFill([
            'ends_at' => null,
        ])->save();
    }

    /**
     * @deprecated
     */
    public function cancelDowngrade(): static
    {
        $this->update([
            'next_plan' => null,
        ]);

        return $this;
    }

    /**
     * @deprecated
     */
    public function isOnDowngradeGracePeriod(): bool
    {
        return $this->next_plan !== null;
    }

    /**
     * @deprecated
     */
    public function isBelowNoticePeriod(): bool
    {
        if (! $this->noticePeriod) {
            return false;
        }

        $length = strtolower($this->noticePeriod[strlen($this->noticePeriod) - 1]);

        if ($length === 'm') {
            return now()->gt($this->cycle_ends_at->subMonthNoOverflow()->endOfMonth());
        }

        return (int) abs($this->cycle_ends_at->diffInDays(now())) < 30;
    }

    /**
     * @deprecated
     */
    public function start(?CarbonInterface $date = null): self
    {
        $date = $date ?? now();

        if ($date->year === 2022 && in_array($this->plan, $this->possiblePlans)) {
            $start = clone $date;
            $end = clone $date;
            $this->forceFill([
                'started_at' => $this->started_at && $this->exists ? $this->started_at : $date,
                'cycle_started_at' => $start->setMonth(07)->startOfMonth(),
                'cycle_ends_at' => $end->endOfYear(),
            ]);

            return $this;
        }

        $end = clone $date;

        $end = match ($this->period) {
            'y' => $end->addMonthNoOverflow()->endOfYear(),
            default => $this->isStartOfSubscription() ? $end->addMonthNoOverflow()->endOfMonth() : $end->endOfMonth(),
        };

        $this->forceFill([
            'started_at' => $this->started_at && $this->exists ? $this->started_at : $date->startOfDay(),
            'cycle_started_at' => $date->startOfDay(),
            'cycle_ends_at' => $end,
        ]);

        return $this;
    }

    /**
     * @deprecated
     */
    public function calcPriceForSubscribable($subscribable, $isUpgrade = false, $isStart = false)
    {
        $orderable = $subscribable->getOrderableItemsForSubscription($this);

        return $this->makeOrderForCurrentCycle($orderable, $isUpgrade)?->calcPrice($isStart);
    }

    /**
     * @deprecated
     */
    public function makeOrderForCurrentCycle($orderable, $isUpgrade = false)
    {
        if (in_array($this->plan, $this->possiblePlans)) {
            /** @var SubscriptionOrder $order */
            $order = $orderable->subscriptionOrders()->make([
                'ended_at' => $this->cycle_ends_at,
                'plan' => $this->plan,
                'is_upgrade' => $isUpgrade,
            ]);

            if ($this->cycle_ends_at->year !== 2022) {
                return $order->forceFill(['started_at' => $this->cycle_started_at]);
            }

            if ($orderable->created_at->gt(Carbon::parse(config('subscription.backtrack_payments_until')))) {
                return $order->forceFill(['started_at' => $orderable->created_at]);
            }

            return $order->forceFill(['started_at' => $this->cycle_started_at]);
        }
    }

    /**
     * @deprecated
     */
    public function makeOrderForCycleBeginningNow($orderable, $isUpgrade = false)
    {
        if (in_array($this->plan, $this->possiblePlans)) {
            /** @var SubscriptionOrder $order */
            return $orderable->subscriptionOrders()->make([
                'ended_at' => $this->cycle_ends_at,
                'started_at' => now(),
                'plan' => $this->plan,
                'is_upgrade' => $isUpgrade,
            ]);
        }
    }

    /**
     * @deprecated
     */
    public function isActive(): bool
    {
        return $this->ends_at === null || $this->ends_at->isFuture();
    }

    /**
     * @deprecated
     */
    public function oldestSubscriptionOrder(): HasOne
    {
        return $this->hasOne(SubscriptionOrder::class)->oldestOfMany('started_at');
    }

    /**
     * @deprecated
     */
    public function isStartOfSubscription(): bool
    {
        return $this->subscriptionOrders->isEmpty();
    }

    /**
     * @deprecated
     */
    public function isUpgrade(): bool
    {
        return $this->subscriptionOrders()->where('total_price', '<', $this->price_per_month)->exists();
    }

    /**
     * @deprecated
     */
    public function isCancelable(): bool
    {
        return ! $this->subscribable->iaOrder
               && (
                   ! $this->subscribable->cardLinkOrder
                   || $this->subscribable->cardLinkOrder->status === CardLinkOrderStatusEnum::Reserved
               );
    }
}
