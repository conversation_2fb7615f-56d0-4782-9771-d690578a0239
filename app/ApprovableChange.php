<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

/**
 * @mixin IdeHelperApprovableChange
 */
class ApprovableChange extends Model
{
    protected $guarded = [];

    protected $casts = [
        'change' => 'array',
    ];

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public static function booted()
    {
        static::created(function ($item) {
            $exitingItems = self::query()
                ->where('pharmacy_id', $item->pharmacy_id)
                ->where('attribute', $item->attribute)
                ->where('id', '!=', $item->id)
                ->get();

            foreach ($exitingItems as $exitingItem) {
                $exitingItem->delete();
            }
        });

        static::deleted(function ($item) {
            if ($item->type == 'image') {
                Storage::disk('approval')->delete($item->change['path']);
            }
        });
    }
}
