<?php

namespace App;

use App\Enums\Vaccinate\VaccinationStatus;
use App\Support\RKIVaccinationSurveillanceApi;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class InfluenzaVaccination
 *
 * @mixin IdeHelperInfluenzaVaccination
 */
class InfluenzaVaccination extends Model
{
    use HasFactory;

    protected $primaryKey = 'vaccination_id';

    public $incrementing = false;

    protected $guarded = [];

    protected $casts = [
        'severe_vaccination_reactions' => 'array',
        'poll_vaccinated_before' => 'array',
        'poll_where_found_out' => 'array',
        'poll_why_pharmacy' => 'array',
        'choices' => 'array',
        'belated_reactions' => 'array',
        'psn' => 'array',
    ];

    public function vaccination(): BelongsTo
    {
        return $this->belongsTo(Vaccination::class);
    }

    public function isDoable()
    {
        if (
            $this->patient_has_illness === null ||
            $this->patient_has_allergy === null ||
            $this->patient_had_reaction === null ||
            $this->patient_has_operation === null ||

            $this->patient_takes_marcumar === null ||
            $this->patient_is_pregnant === null ||

            ($this->patient_has_illness && ! $this->patient_has_illness_vaccination_possible) ||
            ($this->patient_has_allergy && ! $this->patient_has_allergy_vaccination_possible) ||
            ($this->patient_had_reaction && ! $this->patient_had_reaction_vaccination_possible) ||
            ($this->patient_has_operation && ! $this->patient_has_operation_vaccination_possible) ||
            ($this->patient_takes_marcumar && ! $this->patient_takes_marcumar_vaccination_possible) ||
            ($this->patient_is_pregnant && ! $this->patient_is_pregnant_vaccination_possible)
        ) {
            return false;
        }

        return true;
    }

    public function scopeSuccessfullyFinished(Builder $query): Builder
    {
        return $query->whereHas('vaccination', function ($query) {
            return $query->whereNull('reasons_to_abort')->where('status', VaccinationStatus::FINISHED);
        });
    }

    public function isPseudomizedCorrectly(): bool
    {
        return RKIVaccinationSurveillanceApi::validatePseudomizationResponseContent($this->psn);
    }
}
