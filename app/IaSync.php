<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property string|null $trigger
 * @property int $successful
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Pharmacy|null $pharmacy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\IaRequest> $requests
 * @property-read int|null $requests_count
 *
 * @method static \Database\Factories\IaSyncFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync whereSuccessful($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync whereTrigger($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaSync whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class IaSync extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'trigger',
        'successful',
    ];

    public function requests(): HasMany
    {
        return $this->hasMany(IaRequest::class);
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
