<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;

/**
 * @property int $ps_id
 * @property string|null $birthdate
 * @property string|null $age
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $cost_unit_identification
 * @property string|null $health_insurance
 * @property string|null $insurance_number
 * @property string|null $optional_address_line
 * @property string|null $street
 * @property string|null $house_number
 * @property string|null $postcode
 * @property string|null $city
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $gender
 * @property \Illuminate\Support\Carbon|null $datetime
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\PharmaceuticalService $pharmaceuticalService
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereAge($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereBirthdate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereCostUnitIdentification($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereDatetime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereHealthInsurance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereHouseNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereInsuranceNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereOptionalAddressLine($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient wherePostcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient wherePsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereStreet($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PharmaceuticalServicePatient whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PharmaceuticalServicePatient extends Model
{
    use HasFactory;

    protected $primaryKey = 'ps_id';

    public $incrementing = false;

    protected $casts = [
        'datetime' => 'datetime',
    ];

    protected $encrypted = [
        'birthdate',
        'first_name',
        'last_name',
        'cost_unit_identification',
        'health_insurance',
        'insurance_number',
        'optional_address_line',
        'street',
        'house_number',
        'postcode',
        'city',
        'email',
        'phone',
        'age',
    ];

    protected $guarded = [];

    public function pharmaceuticalService(): BelongsTo
    {
        return $this->belongsTo(PharmaceuticalService::class, 'ps_id');
    }

    public function getBirthdateAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setBirthdateAttribute($value)
    {
        if (! $value) {
            $this->attributes['birthdate'] = null;

            return;
        }
        $this->attributes['birthdate'] = Crypt::encrypt($value);
    }

    public function getFirstNameAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setFirstNameAttribute($value)
    {
        if (! $value) {
            $this->attributes['first_name'] = null;

            return;
        }
        $this->attributes['first_name'] = Crypt::encrypt($value);
    }

    public function getLastNameAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setLastNameAttribute($value)
    {
        if (! $value) {
            $this->attributes['last_name'] = null;

            return;
        }
        $this->attributes['last_name'] = Crypt::encrypt($value);
    }

    public function getCostUnitIdentificationAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setCostUnitIdentificationAttribute($value)
    {
        if (! $value) {
            $this->attributes['cost_unit_identification'] = null;

            return;
        }
        $this->attributes['cost_unit_identification'] = Crypt::encrypt($value);
    }

    public function getHealthInsuranceAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setHealthInsuranceAttribute($value)
    {
        if (! $value) {
            $this->attributes['health_insurance'] = null;

            return;
        }
        $this->attributes['health_insurance'] = Crypt::encrypt($value);
    }

    public function getInsuranceNumberAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setInsuranceNumberAttribute($value)
    {
        if (! $value) {
            $this->attributes['insurance_number'] = null;

            return;
        }
        $this->attributes['insurance_number'] = Crypt::encrypt($value);
    }

    public function getStreetAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setStreetAttribute($value)
    {
        if (! $value) {
            $this->attributes['street'] = null;

            return;
        }
        $this->attributes['street'] = Crypt::encrypt($value);
    }

    public function getHouseNumberAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setHouseNumberAttribute($value)
    {
        if (! $value) {
            $this->attributes['house_number'] = null;

            return;
        }
        $this->attributes['house_number'] = Crypt::encrypt($value);
    }

    public function getOptionalAddressLineAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setOptionalAddressLineAttribute($value)
    {
        if (! $value) {
            $this->attributes['optional_address_line'] = null;

            return;
        }
        $this->attributes['optional_address_line'] = Crypt::encrypt($value);
    }

    public function getPostcodeAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setPostcodeAttribute($value)
    {
        $this->attributes['postcode'] = $value == null ? null : Crypt::encrypt($value);
    }

    public function getCityAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setCityAttribute($value)
    {
        $this->attributes['city'] = $value == null ? null : Crypt::encrypt($value);
    }

    public function getEmailAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setEmailAttribute($value)
    {
        if (! $value) {
            $this->attributes['email'] = null;

            return;
        }
        $this->attributes['email'] = Crypt::encrypt($value);
    }

    public function getPhoneAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setPhoneAttribute($value)
    {
        if (! $value) {
            $this->attributes['phone'] = null;

            return;
        }
        $this->attributes['phone'] = Crypt::encrypt($value);
    }

    public function getAgeAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setAgeAttribute($value)
    {
        $this->attributes['age'] = $value == null ? null : Crypt::encrypt($value);
    }

    public function getGenderAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setGenderAttribute($value)
    {
        $this->attributes['gender'] = $value == null ? null : Crypt::encrypt($value);
    }
}
