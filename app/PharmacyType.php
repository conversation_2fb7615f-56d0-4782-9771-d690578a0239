<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class PharmacyType
 *
 * @mixin IdeHelperPharmacyType
 */
class PharmacyType extends Model
{
    use HasFactory;

    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    public function pharmacies(): HasMany
    {
        return $this->hasMany(Pharmacy::class, 'pharmacy_type');
    }
}
