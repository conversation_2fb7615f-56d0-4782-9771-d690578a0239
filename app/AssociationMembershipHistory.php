<?php

namespace App;

use App\Enums\AssociationMembershipChangeModeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int|null $user_id
 * @property int|null $association_id
 * @property \Illuminate\Support\Carbon $started_at
 * @property \Illuminate\Support\Carbon|null $canceled_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property AssociationMembershipChangeModeEnum|null $mode
 * @property-read \App\Association|null $association
 * @property-read \App\User|null $user
 *
 * @method static \Database\Factories\AssociationMembershipHistoryFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereAssociationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereCanceledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipHistory withoutTrashed()
 *
 * @mixin \Eloquent
 */
class AssociationMembershipHistory extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = ['id'];

    protected $casts = [
        'started_at' => 'datetime',
        'canceled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'mode' => AssociationMembershipChangeModeEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }
}
