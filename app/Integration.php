<?php

namespace App;

use App\Casts\IntegrationSettingCast;
use App\Integrations\IntegrationTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Integration extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $casts = [
        'integration_type' => IntegrationTypeEnum::class,
        'settings' => IntegrationSettingCast::class,
    ];

    protected $fillable = [
        'integration_type',
        'settings',
    ];

    public function integratable(): MorphTo
    {
        return $this->morphTo();
    }
}
