<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @mixin IdeHelperAssociationRoleUser
 *
 * @property int $user_id
 * @property int $association_id
 * @property string $role_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property array<array-key, mixed>|null $permissions
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser whereAssociationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser wherePermissions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser whereRoleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationRoleUser whereUserId($value)
 *
 * @mixin \Eloquent
 */
class AssociationRoleUser extends Pivot
{
    protected $casts = [
        'permissions' => 'array',
    ];
}
