<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class BusinessHour
 *
 * @mixin IdeHelperBusinessHour
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $pharmacy_id
 * @property int $day_of_week
 * @property string $opens
 * @property string $closes
 * @property-read \App\Pharmacy $pharmacy
 *
 * @method static \Database\Factories\BusinessHourFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour whereCloses($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour whereDayOfWeek($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour whereOpens($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessHour whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class BusinessHour extends Model
{
    use HasFactory;

    protected $touches = ['pharmacy'];

    protected $fillable = [
        'day_of_week',
        'opens',
        'closes',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
