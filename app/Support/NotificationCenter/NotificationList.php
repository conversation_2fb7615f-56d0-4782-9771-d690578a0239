<?php

namespace App\Support\NotificationCenter;

use App\Enums\FlashMessageTypeEnum;
use App\User;
use Illuminate\Support\Collection;
use RuntimeException;

class NotificationList
{
    /** @var array<class-string<Notification>> */
    private static array $notifications = [
        DatabaseNotification::class,
        MatrixBootstrapNotification::class,
        ConnectNIDNotification::class,
        ApoGuideQRCodeNotification::class,
        OrderCardLinkNotification::class,
        ActivateApoGuideCardLinkVendorNotification::class,
        ShiftPlanFeatureNotification::class,
        NewAssociationNewsNotification::class,
        MissingPaymentMethodNotification::class,
        EmployeeStripeGracePeriodNotification::class,
        DeactivatedPharmacyNotification::class,
        NewsletterNotification::class,
    ];

    /** @return Collection<Notification> */
    public static function all(): Collection
    {
        $user = user();
        if (! $user instanceof User) {
            throw new RuntimeException('User not found');
        }

        $allNotifications = collect();
        foreach (self::$notifications as $notificationClass) {
            $allNotifications = $allNotifications->merge($notificationClass::getNotifications());
        }

        return $allNotifications;
    }

    public static function count(): int
    {
        return self::all()->count();
    }

    public static function isVisible(): bool
    {
        if (! user() instanceof User) {
            return false;
        }

        return true;
    }

    public static function notify(): void
    {
        /** @phpstan-ignore-next-line Closure == callable */
        self::all()->each(fn (Notification $notification) => notify($notification->getTitle(), FlashMessageTypeEnum::INFO->value, 33));
    }
}
