<?php

namespace App\Support\NotificationCenter;

use App\ClosedNotification;
use App\Enums\Newsletter\MailcoachList;
use App\User;
use Illuminate\Support\Collection;

final class NewsletterNotification extends Notification
{
    protected function __construct(protected User $user)
    {
        parent::__construct();
    }

    public function isVisible(): bool
    {
        if ($this->user->hasMailcoachSubscriptions(MailcoachList::Advertising)) {
            return false;
        }

        return $this->closedNotifications->doesntContain(function (ClosedNotification $closedNotification) {
            return $closedNotification->type === self::class
                && $closedNotification->closeable_type === $this->user->getMorphClass()
                && $closedNotification->closeable_id === $this->user->id;
        });
    }

    public static function getNotifications(): Collection
    {
        $notifications = collect();
        $user = user();

        assert($user instanceof User);

        $notification = new self($user);

        if ($notification->isVisible()) {
            $notifications->push($notification);
        }

        return $notifications;
    }

    public static function count(): int
    {
        return self::getNotifications()->count();
    }

    public static function close(): void
    {
        $user = user();

        assert($user instanceof User);

        (new self($user))->markAsClosed();
    }

    public function getTitle(): string
    {
        return 'Der neue GEDISA Newsletter ist da';
    }

    /**
     * @return array<string>
     */
    public function getContent(): array
    {
        return [
            'Erhalten Sie exklusive Einblicke in Produktneuheiten, spannende Features, Schulungen und Entwicklungen rund um die Digitalisierung in der Apotheke.',
        ];
    }

    public function getLinkText(): string
    {
        return 'Newsletter abonnieren';
    }

    public function getUrl(): string
    {
        return route('users.edit', ['tab' => 'notifications']);
    }

    /**
     * @return array<string, mixed>
     */
    public function toLivewire(): array
    {
        return [
            'userId' => $this->user->id,
        ];
    }

    public static function fromLivewire(mixed $value): self
    {
        /** @var array<mixed> $value */
        /** @var User $user */
        $user = User::find($value['userId']);

        return new self($user);
    }

    public function markAsClosed(): void
    {
        $this->closeNotification($this->user->getMorphClass(), $this->user->id);
    }

    public function getKey(): string
    {
        return md5(self::class.'-'.$this->user->id);
    }
}
