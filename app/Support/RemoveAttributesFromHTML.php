<?php

namespace App\Support;

use DOMAttr;
use DOMDocument;
use DOMElement;
use DOMNode;

class RemoveAttributesFromHTML
{
    /**
     * @param  array<string>  $whitelist
     */
    public function __construct(
        protected string $html,
        protected array $whitelist = [],
        protected DOMDocument $dom = new DOMDocument,
    ) {}

    /**
     * @param  array<string>|string  $whitelist
     */
    public static function make(string $html, array|string $whitelist = []): RemoveAttributesFromHTML
    {
        if (is_string($whitelist)) {
            $whitelist = [$whitelist];
        }

        return new RemoveAttributesFromHTML($html, $whitelist);
    }

    public function run(): string
    {
        $html = mb_convert_encoding($this->html, 'HTML-ENTITIES', 'UTF-8');

        assert(is_string($html));

        $this->dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        if ($this->dom->documentElement) {
            $this->remove($this->dom->documentElement);
        }

        foreach ($this->dom->childNodes as $childNode) {
            $this->remove($childNode);
        }

        if ($result = $this->dom->saveHTML()) {
            return str_replace(["\n"], [null], $result);
        }

        return $this->html;
    }

    protected function remove(DOMElement|DOMNode $node): void
    {
        if ($node->hasChildNodes()) {
            foreach ($node->childNodes as $childNode) {
                $this->remove($childNode);
            }
        }

        if ($node->hasAttributes()) {
            /** @var \DOMNamedNodeMap<DOMAttr> $attributes */
            $attributes = $node->attributes;

            foreach ($attributes as $attribute) {
                if (! in_array($attribute->nodeName, $this->whitelist, true)) {
                    $node->removeAttribute($attribute->nodeName); // @phpstan-ignore-line
                }
            }
        }
    }
}
