<?php

namespace App;

use App\Enums\PharmaceuticalService\PharmaceuticalServiceStatus;
use App\Enums\PharmaceuticalService\PharmaceuticalServiceTypeEnum;
use App\Traits\HasLocalizedTimestamp;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;

/**
 * @mixin IdeHelperPharmaceuticalService
 */
class PharmaceuticalService extends Model
{
    use HasFactory, HasLocalizedTimestamp, Prunable;

    protected $casts = [
        'reasons_to_abort' => 'array',
        'date' => 'datetime',
        'further_information_allowed' => 'boolean',
    ];

    protected $guarded = [];

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    protected static function booted()
    {
        static::creating(function ($measureBloodPressure) {
            $measureBloodPressure->uuid = Str::uuid();
        });
    }

    /**
     * Get the prunable model query.
     */
    public function prunable(): Builder
    {
        return static::query()
            ->where('created_at', '<=', now()->subDay()->startOfDay())
            ->where('status', PharmaceuticalServiceStatus::DRAFT);
    }

    public function getLocalizedDateAttribute($value): Carbon
    {
        return $this->getLocalizedCarbonInstance($this->date);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function healthInsuranceCompany(): BelongsTo
    {
        return $this->belongsTo(HealthInsuranceCompany::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getUserNameAttribute()
    {
        $user = self::user()->first();

        return ! empty($user) ? trim($user->first_name.' '.$user->last_name) : null;
    }

    public function pharmaceutical(): BelongsTo
    {
        return $this->belongsTo(Pharmaceutical::class);
    }

    public function scopeSuccessfullyFinished(Builder $query): Builder
    {
        return $query->whereNull('reasons_to_abort')->where('status', PharmaceuticalServiceStatus::FINISHED);
    }

    public function scopeSuccessfullyFinishedByMeasureBloodPressure(Builder $query): Builder
    {
        return $query->where('type', PharmaceuticalServiceTypeEnum::MEASURE_BLOOD_PRESSURE)->where('status', PharmaceuticalServiceStatus::FINISHED);
    }

    public function scopeSuccessfullyFinishedByInhalationTechnique(Builder $query): Builder
    {
        return $query->where('type', PharmaceuticalServiceTypeEnum::INHALATION_TECHNIQUE)->where('status', PharmaceuticalServiceStatus::FINISHED);
    }

    public function pharmaceuticalServicePatient(): HasOne
    {
        return $this->hasOne(PharmaceuticalServicePatient::class, 'ps_id', 'id');
    }

    public function measureBloodPressurePatient(): HasOne
    {
        return $this->hasOne(MeasureBloodPressurePatient::class, 'ps_id', 'id');
    }

    public function inhalationTechniquePatient(): HasOne
    {
        return $this->hasOne(InhalationTechniquePatient::class, 'ps_id', 'id');
    }

    public function getSpznAttribute()
    {
        if ($this->type == PharmaceuticalServiceTypeEnum::MEASURE_BLOOD_PRESSURE) {
            return 17716872;
        }

        return 17716783;
    }
}
