<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Class FocusArea
 *
 * @mixin IdeHelperFocusArea
 *
 * @property int $id
 * @property string $name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\FocusAreaPharmacy|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Pharmacy> $pharmacies
 * @property-read int|null $pharmacies_count
 *
 * @method static \Database\Factories\FocusAreaFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusArea newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusArea newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusArea query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusArea whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusArea whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusArea whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusArea whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class FocusArea extends Model
{
    use HasFactory;

    protected $fillable = ['name'];

    protected $hidden = [
        'pivot',
        'created_at',
        'updated_at',
    ];

    public function pharmacies(): BelongsToMany
    {
        return $this->belongsToMany(Pharmacy::class)->using(FocusAreaPharmacy::class)->withTimestamps();
    }
}
