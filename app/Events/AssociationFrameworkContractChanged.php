<?php

namespace App\Events;

use App\Association;
use App\Domains\Subscription\Application\AssociationFrameworkContracts\AssociationFrameworkContract;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AssociationFrameworkContractChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Association $association,
        public AssociationFrameworkContract $from,
        public AssociationFrameworkContract $to,
    ) {}
}
