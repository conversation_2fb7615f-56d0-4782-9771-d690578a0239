<?php

namespace App\Traits;

use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Enums\Settings\PharmacySettingTypes;
use App\Pharmacy;
use App\Setting;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use InvalidArgumentException;

/** @mixin Model */
trait HasGeneralSettings
{
    public function generalSettings(): MorphMany
    {
        return $this->morphMany(Setting::class, 'settingable');
    }

    public function hasGeneralSetting(string $settingName): bool
    {
        return $this->generalSettings->where('type', $settingName)
            ->first() !== null;
    }

    public function getGeneralSetting(string $settingName)
    {
        return $this->generalSettings->where('type', $settingName)->first();
    }

    /**
     * @return Setting|Model
     */
    public function setGeneralSetting(string $settingName, $val)
    {
        $isTermsSetting = match ($settingName) {
            PharmacySettingTypes::TERMS_OF_USE => true,
            PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT => true,
            default => false,
        };

        if ($isTermsSetting && $val === false && $this instanceof Pharmacy && $this->isSubscribedToProduct(BaseStripeProduct::class)) {
            throw new InvalidArgumentException('Pharmacies are not allowed to decline terms while subscribed.');
        }

        $this->generalSettings()->create([
            'type' => $settingName,
            'value' => $val,
        ]);

        if (method_exists($this, 'searchable') && method_exists($this, 'shouldBeSearchable') && $this->shouldBeSearchable()) {
            $this->searchable();
        }

        return $this->refresh()->getGeneralSetting($settingName);
    }
}
