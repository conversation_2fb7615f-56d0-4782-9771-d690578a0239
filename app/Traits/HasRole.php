<?php

namespace App\Traits;

use Illuminate\Support\Collection;

trait HasRole
{
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * @param  Collection|array<string>  $roles
     */
    public function hasAnyRole(Collection|array $roles): bool
    {
        return Collection::wrap($roles)->contains(fn ($role) => $role === $this->role);
    }

    public function doesntHaveRole(string $role): bool
    {
        return ! $this->hasRole($role);
    }
}
