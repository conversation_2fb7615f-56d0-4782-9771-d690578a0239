<?php

namespace App\Traits;

use App\ShiftPlanBetaUser;
use Illuminate\Support\Facades\Cache;

trait InteractsWithBetaPhases
{
    public function isShiftPlanBetaUser(): bool
    {
        return (bool) Cache::remember(
            sprintf('shift_plan.beta.user.%s', $this->id),
            3600,
            fn () => (bool) ShiftPlanBetaUser::enabled()->firstWhere('user_id', $this->id)
        );
    }
}
