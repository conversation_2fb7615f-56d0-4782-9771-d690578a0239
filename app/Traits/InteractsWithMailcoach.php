<?php

namespace App\Traits;

use App\Enums\Newsletter\MailcoachList;
use App\Integrations\IntegrationTypeEnum;
use App\Integrations\MailcoachIntegration;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

trait InteractsWithMailcoach
{
    /** @param MailcoachList|array<MailcoachList> $lists */
    public function scopeSubscribedToMailcoachList(Builder $query, MailcoachList|array $lists): Builder
    {
        $lists = is_array($lists) ? $lists : [$lists];

        $query->whereHas('integrations', function (Builder $query) use ($lists) {
            foreach ($lists as $list) {
                $query->whereNotNull(sprintf('settings->%s', $list->referenceFieldName()));
            }
        });

        return $query;
    }

    /** @param MailcoachList|array<MailcoachList> $lists */
    public function scopeNotSubscribedToMailcoachList(Builder $query, MailcoachList|array $lists): Builder
    {
        $lists = is_array($lists) ? $lists : [$lists];

        $query->whereDoesntHave('integrations', function (Builder $query) use ($lists) {
            foreach ($lists as $list) {
                $query->whereNotNull(sprintf('settings->%s', $list->referenceFieldName()));
            }
        });

        return $query;
    }

    public function scopeByMailcoachSubscriberId(Builder $query, string $subscriberId): Builder
    {
        return $query->whereHas('integrations', function (Builder $query) use ($subscriberId) {
            $query->where(function (Builder $query) use ($subscriberId) {
                foreach (collect((new MailcoachIntegration)->toArray())->keys() as $key) {
                    $query->orWhere(sprintf('settings->%s', $key), $subscriberId);
                }
            });
        });
    }

    public function mailcoachSubscriptions(): Collection
    {
        $mailcoachIntegrationModel = $this->getIntegration(IntegrationTypeEnum::Mailcoach);
        $mailcoachIntegration = $mailcoachIntegrationModel->settings ?? new MailcoachIntegration;

        assert($mailcoachIntegration instanceof MailcoachIntegration);

        return collect($mailcoachIntegration->toArray())
            ->filter(fn ($value) => ! is_null($value));
    }

    /** @param MailcoachList|array<MailcoachList>|null $lists */
    public function hasMailcoachSubscriptions(MailcoachList|array|null $lists = null): bool
    {
        $subscriptions = $this->mailcoachSubscriptions();

        if (is_null($lists)) {
            return $subscriptions->isNotEmpty();
        }

        $keys = collect(is_array($lists) ? $lists : [$lists])
            ->map(fn (MailcoachList $list) => $list->referenceFieldName())
            ->values();

        return $subscriptions->keys()->intersect($keys)->count() === $keys->count();
    }

    public function forEachMailcoachSubscription(callable $callback): self
    {
        $mailcoachSubscriptions = $this->mailcoachSubscriptions();

        foreach ($mailcoachSubscriptions as $referenceFieldName => $subscriptionId) {
            $callback($subscriptionId, $referenceFieldName);
        }

        return $this;
    }
}
