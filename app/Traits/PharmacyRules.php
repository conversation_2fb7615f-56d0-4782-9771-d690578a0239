<?php

namespace App\Traits;

use App\Enums\PharmacyRoleEnum;
use App\Pharmacy;
use App\User;

/**
 * Trait PharmacyRules
 *
 * @mixin User
 */
trait PharmacyRules
{
    /**
     * @param  int|Pharmacy  $pharmacy
     */
    public function hasPharmacyRole($pharmacy, ?string $role = null): bool
    {
        $pharmacy = $this->getPharmacy($pharmacy);

        if ($pharmacy === null) {
            return false;
        }

        if ($role === null) {
            return true;
        }

        return $role === $pharmacy->pivot->role_name;
    }

    public function isOwnerOrSubOwnerOfPharmacy(?Pharmacy $pharmacy = null): bool
    {
        if ($pharmacy === null) {
            $pharmacy = currentPharmacy();

            if ($pharmacy === null) {
                return false;
            }
        }

        return $this->hasPharmacyRole($pharmacy, PharmacyRoleEnum::OWNER) || $this->hasPharmacyRole($pharmacy, PharmacyRoleEnum::SUB_OWNER);
    }

    public function hasPharmacyPermission($pharmacy, $permission): bool
    {
        $pharmacy = $this->getPharmacy($pharmacy);

        if ($pharmacy === null) {
            return false;
        }

        return in_array($permission, $pharmacy->pivot->permissions ?? []);
    }

    /**
     * This method works on the assumption that a pharmacist can only ever have exactly one role across all related
     * pharmacies.
     * Furthermore, an employee/branch manager can only be an employee/branch manager of exactly one pharmacy
     * and an owner/subowner is always the owner/subowner of all related pharmacies.
     */
    public function getPharmacyUserRole(): ?string
    {
        if (! $this->isPharmacyUser()) {
            return null;
        }

        if ($this->isOwner()) {
            return PharmacyRoleEnum::OWNER;
        }

        return $this->pharmacies->first()?->pivot?->role_name;
    }

    public function getPharmacyRole($pharmacy)
    {
        $pharmacy = $this->getPharmacy($pharmacy);

        return optional($pharmacy)->pivot->role_name;
    }

    public function getPharmacyPermissions($pharmacy)
    {
        // TODO IA: Add caching (Not critical for release. Ticket: PIN-280)
        $pharmacy = $this->getPharmacy($pharmacy);

        return optional($pharmacy)->pivot->permissions;
    }

    private function getPharmacy($pharmacy)
    {
        if (is_a($pharmacy, Pharmacy::class)) {
            $pharmacy = $pharmacy->id;
        }

        return $this->pharmacies->find($pharmacy);
    }
}
