<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PharmacyAddress
 *
 * @mixin IdeHelperPharmacyAddress
 */
class PharmacyAddress extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $touches = ['pharmacy'];

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    public function getStringAddressAttribute()
    {
        return ($this->optional_address_line ? $this->optional_address_line.', ' : null)
            .$this->street.' '.$this->house_number.', '
            .$this->postcode.' '.$this->city;
    }
}
