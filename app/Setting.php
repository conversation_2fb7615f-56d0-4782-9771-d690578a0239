<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $settingable_type
 * @property int $settingable_id
 * @property string $type
 * @property string|null $value
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $settingable
 *
 * @method static Builder<static>|Setting accepted(string $type)
 * @method static Builder<static>|Setting newModelQuery()
 * @method static Builder<static>|Setting newQuery()
 * @method static Builder<static>|Setting onlyTrashed()
 * @method static Builder<static>|Setting query()
 * @method static Builder<static>|Setting whereCreatedAt($value)
 * @method static Builder<static>|Setting whereDeletedAt($value)
 * @method static Builder<static>|Setting whereId($value)
 * @method static Builder<static>|Setting whereSettingableId($value)
 * @method static Builder<static>|Setting whereSettingableType($value)
 * @method static Builder<static>|Setting whereType($value)
 * @method static Builder<static>|Setting whereUpdatedAt($value)
 * @method static Builder<static>|Setting whereValue($value)
 * @method static Builder<static>|Setting withTrashed()
 * @method static Builder<static>|Setting withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Setting extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected static function booted()
    {
        static::created(function (Setting $setting) {
            $relation = $setting->settingable;

            $same = $relation->generalSettings()->where('type', $setting->type)
                ->orderBy('created_at')
                ->get();

            if ($same->count() == 2) {
                $same->first()->delete();
            }
        });
    }

    public function settingable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeAccepted(Builder $builder, string $type)
    {
        return $builder->where('type', $type)
            ->where('value', true);
    }
}
