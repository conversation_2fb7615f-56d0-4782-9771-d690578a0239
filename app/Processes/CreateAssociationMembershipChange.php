<?php

namespace App\Processes;

use App\Processes\Tasks\CreateAssociationMembershipChange\CreateChange;
use App\Processes\Tasks\CreateAssociationMembershipChange\DetermineMode;
use App\Processes\Tasks\CreateAssociationMembershipChange\LoadAssociations;
use App\Processes\Tasks\CreateAssociationMembershipChange\SendMail;

class CreateAssociationMembershipChange extends Process
{
    /**
     * @return array<class-string>
     */
    public function tasks(): array
    {
        return [];

        /**
         * TODO: Redo this process
         * AP-2406 disable change association membership
        return [
            DetermineMode::class,
            LoadAssociations::class,
            CreateChange::class,
            SendMail::class,
        ];*/
    }
}
