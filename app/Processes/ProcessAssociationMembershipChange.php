<?php

namespace App\Processes;

use App\Processes\Tasks\ProcessAssociationMembershipChange\CancelMembership;
use App\Processes\Tasks\ProcessAssociationMembershipChange\CreateAssociationMembership;
use App\Processes\Tasks\ProcessAssociationMembershipChange\DeleteAssociationMembership;
use App\Processes\Tasks\ProcessAssociationMembershipChange\FinishChange;
use App\Processes\Tasks\ProcessAssociationMembershipChange\RequireTermsOfServicesAcceptance;
use App\Processes\Tasks\ProcessAssociationMembershipChange\VerifyChange;

class ProcessAssociationMembershipChange extends Process
{
    /**
     * @return array<class-string>
     */
    public function tasks(): array
    {
        return [
            VerifyChange::class,
            CancelMembership::class,
            RequireTermsOfServicesAcceptance::class,
            DeleteAssociationMembership::class,
            CreateAssociationMembership::class,
            FinishChange::class,
        ];
    }
}
