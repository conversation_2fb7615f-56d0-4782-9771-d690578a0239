<?php

namespace App\Processes\Tasks\CreateAssociationMembershipChange;

use App\Enums\AssociationMembershipChangeModeEnum;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use Closure;

class DetermineMode
{
    public function __invoke(CreateAssociationMembershipChangePayload $payload, Closure $next): CreateAssociationMembershipChangePayload
    {
        $payload->mode = $payload->mode ?? match (true) {
            $payload->old === null => AssociationMembershipChangeModeEnum::ADD,
            $payload->new === null => AssociationMembershipChangeModeEnum::DELETE,
            default => AssociationMembershipChangeModeEnum::CHANGE
        };

        return $next($payload);
    }
}
