<?php

namespace App\Processes\Tasks\CreateAssociationMembershipChange;

use App\Enums\AssociationMembershipChangeModeEnum;
use App\Mail\ChangeAssociationMembershipUserInfoMail;
use App\Mail\RevertAssociationMembershipUserInfoMail;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use Closure;
use Illuminate\Support\Facades\Mail;

class SendMail
{
    public function __invoke(CreateAssociationMembershipChangePayload $payload, Closure $next): CreateAssociationMembershipChangePayload
    {
        $new = is_int($payload->new) ? $payload->new : $payload->new?->id;

        $mail = match ($payload->mode) {
            /** @phpstan-ignore-next-line this is validated before so it's in a valid state here. */
            AssociationMembershipChangeModeEnum::DELETE, AssociationMembershipChangeModeEnum::CHANGE, AssociationMembershipChangeModeEnum::ADD => new ChangeAssociationMembershipUserInfoMail($payload->user, $new, $payload->changeAt),
            AssociationMembershipChangeModeEnum::REVERT => new RevertAssociationMembershipUserInfoMail($payload->user, $new),
            default => throw new \Exception('Cannot process association membership change mode'),
        };

        Mail::to($payload->user)->queue($mail);

        return $next($payload);
    }
}
