<?php

namespace App\Processes\Tasks\ProcessAssociationMembershipChange;

use App\Domains\Subscription\Application\Discounts\BaseSubscriptionAssociationPaidStripeDiscount;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Domains\Subscription\Domain\Actions\Subscription\RemoveDiscountFromSubscription;
use App\Domains\Subscription\Domain\Actions\Subscription\SendOneTimeInvoiceForNotProRatedProducts;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Pharmacy;
use App\Processes\Payloads\ProcessAssociationMembershipChangePayload;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Throwable;

class ChangeSubscriptionDeleteMode
{
    public function __construct(
        private RemoveDiscountFromSubscription $removeBaseDiscountFromSubscription,
    ) {}

    public function __invoke(ProcessAssociationMembershipChangePayload $payload, \Closure $next): ProcessAssociationMembershipChangePayload|bool
    {
        if ($payload->associationMembershipChange->mode !== AssociationMembershipChangeModeEnum::DELETE) {
            return $next($payload);
        }

        /** @var Collection<Pharmacy> $pharmacies */
        $pharmacies = $payload->associationMembershipChange->user?->pharmacies->filter(fn (Pharmacy $pharmacy) => $pharmacy->subscribed()) ?? collect();

        if ($pharmacies->isEmpty()) {
            return $next($payload);
        }

        foreach ($pharmacies as $pharmacy) {
            try {
                if ($pharmacy->isSubscribedToProduct(BaseStripeProduct::class)) {
                    $this->removeBaseDiscountFromSubscription->execute($pharmacy, new BaseSubscriptionAssociationPaidStripeDiscount);

                    if ($this->needsOneTimeInvoice($payload)) {
                        app(SendOneTimeInvoiceForNotProRatedProducts::class)->execute(
                            pharmacy: $pharmacy,
                            products: [BaseStripeProduct::make()],
                            startDate: $this->calculateOneOffInvoiceStartTime(),
                            nextBillingDate: now()->endOfQuarter()->addDay()->startOfDay(),
                        );
                    }
                }

            } catch (Throwable $t) {
                report(new Exception(
                    sprintf('Fehler beim Anpassen des Abos für Apotheke [%s] im Rahmen des Verbandsaustritts. Manuelle Aktion(en) notwendig.', $pharmacy->id),
                    previous: $t
                ));
            }
        }

        return $next($payload);
    }

    private function needsOneTimeInvoice(ProcessAssociationMembershipChangePayload $payload): bool
    {
        $changeAt = $payload->associationMembershipChange->change_at;
        assert($changeAt instanceof Carbon);

        return $changeAt->isToday() && ! $changeAt->copy()->endOfQuarter()->isSameDay($changeAt);
    }

    private function calculateOneOffInvoiceStartTime(): Carbon
    {
        $monthsThatNeedCorrection = abs(now()->endOfQuarter()->diffInMonths()) - 1;

        return now()->endOfQuarter()->subMonthsNoOverflow($monthsThatNeedCorrection)->startOfMonth();
    }
}
