<?php

namespace App\Processes\Tasks\ProcessAssociationMembershipChange;

use App\Pharmacy;
use App\Processes\Payloads\ProcessAssociationMembershipChangePayload;

class RequireTermsOfServicesAcceptance
{
    public function __invoke(ProcessAssociationMembershipChangePayload $payload, \Closure $next): ProcessAssociationMembershipChangePayload|bool
    {
        $payload->associationMembershipChange->user()->withTrashed()->first()?->pharmacies->each(fn (Pharmacy $pharmacy) => $pharmacy->acceptTerms(false));

        return $next($payload);
    }
}
