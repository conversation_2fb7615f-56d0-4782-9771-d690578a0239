<?php

namespace App\Processes\Tasks\ProcessAssociationMembershipChange;

use App\Domains\Subscription\Application\AssociationFrameworkContracts\AssociationFrameworkContract;
use App\Domains\Subscription\Application\AssociationFrameworkContracts\NoAssociationFrameworkContract;
use App\Domains\Subscription\Application\Discounts\BaseSubscriptionAssociationPaidStripeDiscount;
use App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Domains\Subscription\Domain\Actions\Subscription\AddDiscountToSubscription;
use App\Domains\Subscription\Domain\Actions\Subscription\RemoveRecurringProductFromSubscription;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Pharmacy;
use App\Processes\Payloads\ProcessAssociationMembershipChangePayload;
use Illuminate\Support\Collection;
use Lara<PERSON>\Cashier\Cashier;
use RuntimeException;
use Stripe\InvoiceItem;

class ChangeSubscriptionAddMode
{
    public function __construct(
        protected AddDiscountToSubscription $addDiscountToSubscription,
        protected RemoveRecurringProductFromSubscription $removeRecurringProductFromSubscription,
    ) {}

    public function __invoke(ProcessAssociationMembershipChangePayload $payload, \Closure $next): ProcessAssociationMembershipChangePayload|bool
    {
        if ($payload->associationMembershipChange->mode !== AssociationMembershipChangeModeEnum::ADD) {
            return $next($payload);
        }

        /** @var ?AssociationFrameworkContract $frameworkContract */
        $frameworkContract = $payload->associationMembershipChange->associationAfter?->currentAssociationFrameworkContractHistory?->contract->instance();

        if ($frameworkContract === null) {
            throw new RuntimeException(sprintf('Rahmenvertrag für Verband [%s] ist null.', $payload->associationMembershipChange->associationAfter?->name ?? 'kein Verband'));
        }

        if ($frameworkContract instanceof NoAssociationFrameworkContract) {
            return $next($payload);
        }

        /** @var Collection<Pharmacy> $pharmacies */
        $pharmacies = $payload->associationMembershipChange->user?->pharmacies->filter(fn (Pharmacy $pharmacy) => $pharmacy->subscribed()) ?? collect();

        if ($pharmacies->isEmpty()) {
            return $next($payload);
        }

        // Rabatt hinterlegen und doppelte Produkte stornieren
        foreach ($pharmacies as $pharmacy) {
            try {
                $intersectedProducts = collect($pharmacy->getProductsInStripeSubscription())
                    ->intersect($frameworkContract->getIncludedProducts());

                if ($pharmacy->isSubscribedToProduct(BaseStripeProduct::class)) {
                    $this->addDiscountToSubscription->execute($pharmacy, new BaseSubscriptionAssociationPaidStripeDiscount);

                    /** @var \Stripe\Collection<InvoiceItem> $invoiceItems */
                    $invoiceItems = Cashier::stripe()->invoiceItems->all([
                        'customer' => $pharmacy->stripeId(),
                        'pending' => true,
                    ]);

                    /** @var Collection<string> $priceIds */
                    $priceIds = $intersectedProducts->map(fn (string $class) => $class::make()->getRecurringStripePrice($pharmacy)->stripePriceId);

                    /** @var InvoiceItem $invoiceItem */
                    foreach ($invoiceItems as $invoiceItem) {
                        if ($invoiceItem->proration && $priceIds->contains($invoiceItem->price?->id)) {
                            $invoiceItem->delete();
                        }
                    }
                }

                $intersectedProducts
                    ->filter(fn (string $product) => $product !== BaseStripeProduct::class)
                    ->map(fn (string $product) => new $product)
                    ->each(fn (StripeProduct $product) => $this->removeRecurringProductFromSubscription->execute($pharmacy, $product));
            } catch (\Throwable $t) {
                report(new \Exception(
                    sprintf('Fehler beim Anpassen des Abos für Apotheke [%s] im Rahmen des Verbandsbeitritts. Manuelle Aktion(en) notwendig.', $pharmacy->id),
                    previous: $t
                ));
            }
        }

        return $next($payload);
    }
}
