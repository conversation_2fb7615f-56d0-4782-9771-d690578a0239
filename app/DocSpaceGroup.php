<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property string $name
 * @property string|null $suffix
 * @property string $sdr_group_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\DocSpaceGroupUser|\App\DocSpaceDocSpaceGroup|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\DocSpace> $docSpaces
 * @property-read int|null $doc_spaces_count
 * @property-read string $full_name
 * @property-read \App\Pharmacy $pharmacy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\User> $users
 * @property-read int|null $users_count
 *
 * @method static \Database\Factories\DocSpaceGroupFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup whereSdrGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup whereSuffix($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpaceGroup whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DocSpaceGroup extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function docSpaces(): BelongsToMany
    {
        return $this->belongsToMany(DocSpace::class)->using(DocSpaceDocSpaceGroup::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->using(DocSpaceGroupUser::class);
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function getFullNameAttribute(): string
    {
        if (! empty($this->suffix)) {
            return $this->name.' ['.$this->suffix.']';
        }

        return $this->name;
    }
}
