<?php

namespace App;

use App\Enums\VaccinationImport\AccountingTypeEnum;
use App\Enums\VaccinationImport\VaccinationImportTypeEnum;
use App\Traits\HasLocalizedTimestamp;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Laravel\Scout\Searchable;

/**
 * A Covid-19 Certificate
 *
 * @mixin IdeHelperVaccinationImport
 */
class VaccinationImport extends Model
{
    use HasFactory, HasLocalizedTimestamp, Searchable;

    public const MINUTES_BETWEEN_IMPORTS_TO_COUNT_AS_RECERTIFIED = 10;

    protected $guarded = [];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function searchableAs()
    {
        return config('scout.prefix').'vaccination_imports';
    }

    public function toSearchableArray()
    {
        return [
            'pharmacy' => [
                'location' => $this->pharmacy?->homeAddress?->latitude ? [
                    'lat' => $this->pharmacy->homeAddress->latitude,
                    'lon' => $this->pharmacy->homeAddress->longitude,
                ] : [],
                'name' => $this->pharmacy?->name,
                'internal_id' => $this->pharmacy?->id,
                'city' => $this->pharmacy?->homeAddress?->city,
                'address' => $this->pharmacy?->homeAddress?->optional_address_line ? $this->pharmacy->homeAddress->optional_address_line.', ' : null
                    .$this->pharmacy?->homeAddress?->street.' '.$this->pharmacy?->homeAddress?->house_number.', '
                    .$this->pharmacy?->homeAddress?->postcode.' '.$this->pharmacy?->homeAddress?->city,
                'association_id' => $this->pharmacy ? ($this->pharmacy->association_id ?? 0) : null,
            ],
            'created_at' => $this->created_at,
            'is_recovered' => (bool) $this->is_recovered,
            'is_recovered_only' => (bool) $this->is_recovered_only,
            'is_booster' => (bool) $this->booster,
            'accounting_type' => $this->accounting_type,
        ];
    }

    public function getPriceAttribute(): int
    {
        if (
            $this->created_at->gte(Carbon::make('2023-07-01 00:00:00'))
            || ($this->is_recovered_only && $this->created_at->gte(Carbon::make('01-03-2023 00:00')))
        ) {
            return 0;
        }

        if ($this->created_at->gte(Carbon::make('08-07-2021 00:00'))) {
            return 600;
        }

        if ($this->accounting_type == AccountingTypeEnum::FIRST || $this->accounting_type == AccountingTypeEnum::RECERTIFIED) {
            return 1800;
        }

        return 600;
    }

    public function getTypeAttribute()
    {
        if ($this->is_recovered_only) {
            return VaccinationImportTypeEnum::RECOVERED;
        }

        if ($this->booster) {
            return VaccinationImportTypeEnum::BOOSTER;
        }

        if ($this->is_recovered) {
            return VaccinationImportTypeEnum::RECOVEREDVACCINATION;
        }

        return VaccinationImportTypeEnum::NORMALVACCINATION;
    }
}
