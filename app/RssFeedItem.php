<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class RssFeedItem
 *
 * @mixin IdeHelperRssFeedItem
 */
class RssFeedItem extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'modification_date' => 'datetime',
        'extra' => 'array',
    ];

    public function source()
    {
        return $this->belongsTo(RssFeedSource::class, 'rss_feed_source_id');
    }
}
