<?php

namespace App;

use App\Enums\Kim\FailedKimReportStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class CompanyUser
 *
 * @mixin IdeHelperFailedKim
 *
 * @property int $id
 * @property string|null $current_kim_address
 * @property array<array-key, mixed>|null $current_additional
 * @property string|null $current_status
 * @property int|null $pharmacy_id
 * @property string|null $actual_kim_address
 * @property string $actual_code
 * @property string|null $actual_state
 * @property FailedKimReportStatus $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Pharmacy|null $pharmacy
 *
 * @method static \Database\Factories\FailedKimReportFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereActualCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereActualKimAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereActualState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereCurrentAdditional($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereCurrentKimAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereCurrentStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FailedKimReport whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class FailedKimReport extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'current_additional' => 'array',
        'status' => FailedKimReportStatus::class,
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    /**
     * @param  array<string>  $activatedKim
     */
    public static function report(?KimAddress $kim, array $activatedKim, FailedKimReportStatus $status): void
    {
        if (! array_key_exists('code', $activatedKim)) {
            throw new \RuntimeException('Code is required to report failed KIM');
        }

        // TODO refactor may use Object for activatedKim or use parameters for each value
        if (! FailedKimReport::where('actual_code', $activatedKim['code'])->exists()) {
            self::create([
                'current_kim_address' => $kim?->email,
                'current_additional' => $kim?->additional,
                'current_status' => $kim?->status,
                'pharmacy_id' => $kim?->pharmacy_id,
                'actual_kim_address' => $activatedKim['komleAddress'] ?? null,
                'actual_code' => $activatedKim['code'],
                'actual_state' => $activatedKim['state'] ?? null,
                'status' => $status,
            ]);
        }
    }
}
