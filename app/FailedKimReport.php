<?php

namespace App;

use App\Enums\Kim\FailedKimReportStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class CompanyUser
 *
 * @mixin IdeHelperFailedKim
 */
class FailedKimReport extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'current_additional' => 'array',
        'status' => FailedKimReportStatus::class,
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    /**
     * @param  array<string>  $activatedKim
     */
    public static function report(?KimAddress $kim, array $activatedKim, FailedKimReportStatus $status): void
    {
        if (! array_key_exists('code', $activatedKim)) {
            throw new \RuntimeException('Code is required to report failed KIM');
        }

        // TODO refactor may use Object for activatedKim or use parameters for each value
        if (! FailedKimReport::where('actual_code', $activatedKim['code'])->exists()) {
            self::create([
                'current_kim_address' => $kim?->email,
                'current_additional' => $kim?->additional,
                'current_status' => $kim?->status,
                'pharmacy_id' => $kim?->pharmacy_id,
                'actual_kim_address' => $activatedKim['komleAddress'] ?? null,
                'actual_code' => $activatedKim['code'],
                'actual_state' => $activatedKim['state'] ?? null,
                'status' => $status,
            ]);
        }
    }
}
