<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @mixin IdeHelperLanguagePharmacy
 */
class LanguagePharmacy extends Pivot
{
    public static function booted()
    {
        static::created(function ($item) {
            Pharmacy::query()->where('id', $item->pharmacy_id)->update([
                'updated_at' => now(),
            ]);
        });

        static::deleted(function ($item) {
            Pharmacy::query()->where('id', $item->pharmacy_id)->update([
                'updated_at' => now(),
            ]);
        });
    }
}
