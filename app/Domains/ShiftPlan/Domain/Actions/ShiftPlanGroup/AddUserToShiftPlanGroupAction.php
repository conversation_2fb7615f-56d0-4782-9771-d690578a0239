<?php

namespace App\Domains\ShiftPlan\Domain\Actions\ShiftPlanGroup;

use App\ShiftPlanGroup;
use App\ShiftPlanGroupUser;
use App\User;

class AddUserToShiftPlanGroupAction
{
    public static function execute(User $user, ShiftPlanGroup $shiftPlanGroup): ShiftPlanGroupUser
    {
        return ShiftPlanGroupUser::create([
            'user_id' => $user->id,
            'shift_plan_group_id' => $shiftPlanGroup->id,
        ]);
    }
}
