<?php

namespace App\Domains\User\Application\Controllers\Api;

use App\Domains\User\Application\Resources\UserResource;
use App\Http\Controllers\Controller;
use Illuminate\Http\Resources\Json\JsonResource;

class UserController extends Controller
{
    public function index(): JsonResource
    {
        $authUser = user();

        $users = $authUser?->getAllOwnerRelatedUsers();

        return UserResource::collection($users);
    }
}
