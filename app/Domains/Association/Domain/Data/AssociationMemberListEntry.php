<?php

namespace App\Domains\Association\Domain\Data;

use App\Domains\Association\Domain\Enums\AssociationMemberListEntryStatus;
use Carbon\Carbon;
use Livewire\Wireable;
use Spatie\LaravelData\Concerns\WireableData;
use Spatie\LaravelData\Data;

class AssociationMemberListEntry extends Data implements Wireable
{
    use WireableData;

    public function __construct(
        public string $id,
        public ?string $title,
        public ?string $firstName,
        public ?string $lastName,
        public string $email,
        public ?Carbon $createdAt,
        public ?int $pharmaciesCount,
        public AssociationMemberListEntryStatus $status,
        public ?string $statusDescription,
    ) {}

    public function isDeletable(): bool
    {
        return $this->status === AssociationMemberListEntryStatus::Failed;
    }

    public function isAssociationChangable(): bool
    {
        return in_array($this->status, [
            AssociationMemberListEntryStatus::RegistrationSent,
            AssociationMemberListEntryStatus::Active,
        ]);
    }

    public function isEditable(): bool
    {
        return $this->isDeletable() || $this->isAssociationChangable();
    }
}
