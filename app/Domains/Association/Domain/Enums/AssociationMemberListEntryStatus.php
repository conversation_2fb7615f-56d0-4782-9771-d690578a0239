<?php

namespace App\Domains\Association\Domain\Enums;

enum AssociationMemberListEntryStatus: string
{
    case Pending = 'Pending';
    case Importing = 'Importing';
    case RegistrationSent = 'RegistrationSent';
    case Active = 'Active';
    case Failed = 'Failed';
    case MarkedForDeletion = 'MarkedForDeletion';

    public function color(): string
    {
        return match ($this) {
            self::Pending, self::Importing => 'gray',
            self::MarkedForDeletion, self::RegistrationSent => 'yellow',
            self::Active => 'green',
            self::Failed => 'red',
        };
    }

    public function translate(): string
    {
        return match (true) {
            $this === self::Pending => 'Import ausstehend',
            $this === self::Importing => 'Import laufend',
            $this === self::RegistrationSent => 'Registrierung verschickt',
            $this === self::Active => 'Aktiv',
            $this === self::Failed => 'Import fehlgeschlagen',
            $this === self::MarkedForDeletion => 'Austritt vorgemerkt',
        };
    }
}
