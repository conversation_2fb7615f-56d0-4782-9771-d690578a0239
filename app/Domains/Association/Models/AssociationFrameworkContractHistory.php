<?php

namespace App\Domains\Association\Models;

use App\Association;
use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
use App\Staff;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssociationFrameworkContractHistory extends Model
{
    protected $guarded = [
        'id',  'staff_id', 'created_at', 'updated_at',
    ];

    protected $casts = [
        'contract' => AssociationFrameworkContractEnum::class,
        'starts_at' => 'date',
        'ends_at' => 'date',
    ];

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class);
    }
}
