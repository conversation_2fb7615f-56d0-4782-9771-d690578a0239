<?php

namespace App\Domains\Subscription\Domain\Actions\Subscription;

use App\Domains\Subscription\Application\Discounts\StripeDiscount;
use App\Pharmacy;
use RuntimeException;
use Stripe\Coupon;
use Stripe\Subscription;

class RemoveDiscountFromSubscription
{
    public function execute(Pharmacy $pharmacy, StripeDiscount $discount): Subscription
    {
        $subscription = $pharmacy->subscription();

        if (! $subscription) {
            return throw new RuntimeException(sprintf('Apotheke [%s] hat kein laufendes Abo.', $pharmacy->id));
        }

        $stripeSubscription = $subscription->asStripeSubscription();
        $discountsCount = count($stripeSubscription->discounts);

        if ($discountsCount === 0) {
            return $stripeSubscription;
        }

        if ($discountsCount > 1) {
            // it is not possible to check if the coupon is among the already applied ones because cashier returns null for discount() if there are more than 1 discounts applied. Also we only have the coupon id of the discount we want to remove. But we'd need the discount id to check against ->discounts.
            return throw new RuntimeException(sprintf('Abo der Apotheke [%s] hat mehr als einen Rabatt applied und diese Aktion würde alle löschen. Manuelle Aktion notwendig', $pharmacy->id));
        }

        $currentCoupon = $stripeSubscription->discount?->coupon;
        assert($currentCoupon instanceof Coupon);

        if ($currentCoupon->id !== $discount->getSettings()->discount_id) {
            return throw new RuntimeException(sprintf('Das Abo der Apotheke [%s] hat bereits einen anderen Rabatt applied. Diese Aktion würde ihn entfernen. Manuelle Aktion notwendig', $pharmacy->id));
        }

        $stripeSubscription->deleteDiscount();
        $stripeSubscription->discounts = [];

        return $stripeSubscription;
    }
}
