<?php

namespace App\Domains\Subscription\Domain\Actions\Subscription;

use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Pharmacy;
use Laravel\Cashier\Subscription;
use RuntimeException;

class RemoveRecurringProductFromSubscription
{
    public function execute(Pharmacy $pharmacy, StripeProduct $product): Subscription
    {
        $subscription = $pharmacy->subscription();

        if (! $subscription) {
            throw new RuntimeException(sprintf('Apotheke [%s] hat kein laufendes Abo.', $pharmacy->name));
        }

        return $subscription->removePrice($product->getRecurringStripePrice($pharmacy)->stripePriceId);
    }
}
