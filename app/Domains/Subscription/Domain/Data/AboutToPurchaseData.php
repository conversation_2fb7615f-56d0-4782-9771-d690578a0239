<?php

namespace App\Domains\Subscription\Domain\Data;

use App\Domains\Subscription\Application\Discounts\StripeDiscount;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Pharmacy;
use Spatie\LaravelData\Data;

class AboutToPurchaseData extends Data
{
    /**
     * @param  StripeProduct[]  $stripeProducts
     * @param  StripeDiscount[]  $stripeDiscounts
     */
    public function __construct(
        public Pharmacy $pharmacy,
        public array $stripeProducts,
        public array $stripeDiscounts,
    ) {}
}
