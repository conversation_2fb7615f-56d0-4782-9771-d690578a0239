<?php

namespace App\Domains\Subscription\Application\FeatureAccess;

use App\Domains\Subscription\Application\StripeProducts\AddOns\CalendarStripeProduct;
use Illuminate\Database\Eloquent\Builder;

class TelepharmacyFeatureAccess extends FeatureAccess
{
    protected function canUseNew(): bool
    {
        return $this->pharmacy->canUseProduct(CalendarStripeProduct::class);
    }

    protected function scopeCanUseNew(Builder $builder): Builder
    {
        return CalendarStripeProduct::scopePharmacyBuilder($builder);
    }
}
