<?php

namespace App\Domains\Subscription\Application\FeatureAccess;

use App\Domains\Subscription\Application\StripeProducts\AddOns\RetaxStripeProduct;
use App\Settings\RetaxSettings;
use Illuminate\Database\Eloquent\Builder;
use RuntimeException;

class RetaxFeatureAccess extends FeatureAccess
{
    protected function canUseNew(): bool
    {
        $owner = $this->pharmacy->owner()?->fresh();

        return $this->pharmacy->canUseProduct(RetaxStripeProduct::class) && $owner && app(RetaxSettings::class)->isAllowedToUse($owner);
    }

    protected function scopeCanUseNew(Builder $builder): Builder
    {
        throw new RuntimeException('Not implemented');
    }
}
