<?php

namespace App\Domains\Subscription\Application\StripeProducts\Base;

use App\Domains\Payment\Domain\Data\StripeProductFrontendTextData;
use App\Domains\Payment\Domain\Data\StripePublicRepresentationData;
use App\Domains\Subscription\Application\Settings\Products\BaseProductSetting;
use App\Domains\Subscription\Application\Settings\Products\StripeProductSetting;
use App\Domains\Subscription\Application\StripeProducts\StripeProduct;
use App\Domains\Subscription\Domain\Data\OneTimePriceData;
use App\Domains\Subscription\Domain\Enums\StripeProductTypeEnum;
use App\Pharmacy;
use Carbon\Carbon;

class BaseStripeProduct extends StripeProduct
{
    public static StripeProductTypeEnum $type = StripeProductTypeEnum::BASE;

    public static bool $isProrated = true;

    public function getSettings(): StripeProductSetting
    {
        return app(BaseProductSetting::class);
    }

    public function getPublicRepresentationData(Pharmacy $pharmacy): StripePublicRepresentationData
    {
        return new StripePublicRepresentationData(
            class: self::class,
            price: $this->getOneTimeStripePrice($pharmacy)->price,
            text: new StripeProductFrontendTextData(
                name: 'Basismitgliedschaft',
                description: 'Die Basismitgliedschaft ist die Grundlage für alle weiteren Mitgliedschaften. Sie beinhaltet die Nutzung der ApoMail-Funktion.',
                features: [
                    'ApoMail',
                    'Pharmazeutische Dienstleistungen',
                    'Impfungen',
                    'Verbandsnews',
                ]
            )
        );
    }

    public function getOneTimePriceArrayForProration(Pharmacy $pharmacy, Carbon $recurringSubscriptionStartsAt, ?Carbon $currentDate = null): array
    {
        $monthsToBookOneTimePrice = intval(ceil(($currentDate ?? now())->floatDiffInMonths($recurringSubscriptionStartsAt)));

        return [new OneTimePriceData(
            price: $this->getOneTimeStripePrice($pharmacy)->stripePriceId,
            quantity: $monthsToBookOneTimePrice,
        )];
    }
}
