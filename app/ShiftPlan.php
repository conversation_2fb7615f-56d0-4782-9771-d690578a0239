<?php

namespace App;

use App\Domains\ShiftPlan\Domain\Observers\ShiftPlanObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\MediaCollections\Models\Concerns\HasUuid;

#[ObservedBy([ShiftPlanObserver::class])]
class ShiftPlan extends Model
{
    use HasFactory;
    use HasUuid;

    protected $guarded = [];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function groups(): HasMany
    {
        return $this->hasMany(ShiftPlanGroup::class, 'shift_plan_id');
    }

    public function shifts(): HasMany
    {
        return $this->hasMany(Shift::class, 'shift_plan_id');
    }
}
