<?php

namespace App;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Laravel\Pennant\Feature;

class ShiftPlanBetaUser extends Model
{
    protected $fillable = [
        'user_id',
        'enabled',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('enabled', true);
    }

    private static function purgeShiftPlanPennantFeature(): void
    {
        Feature::purge(\App\Features\ShiftPlan::class);
    }

    private static function purgeCache(int $userId): void
    {
        Cache::forget(sprintf('shift_plan.beta.user.%s', $userId));
    }

    protected static function booted(): void
    {
        static::created(function (ShiftPlanBetaUser $user) {
            self::purgeShiftPlanPennantFeature();
            self::purgeCache($user->user_id);
        });

        static::updated(function (ShiftPlanBetaUser $user) {
            self::purgeShiftPlanPennantFeature();
            self::purgeCache($user->user_id);
        });

        static::deleted(function (ShiftPlanBetaUser $user) {
            self::purgeShiftPlanPennantFeature();
            self::purgeCache($user->user_id);
        });
    }
}
