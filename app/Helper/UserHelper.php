<?php

namespace App\Helper;

use App\Rules\GedisaMail;

class UserHelper
{
    public static function getLoginAttribute($value)
    {
        if (str_ends_with($value, '@apomail.de')) {
            return 'username';
        }

        return filter_var($value, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
    }

    public static function getLoginAttributeValidator($value)
    {
        switch (self::getLoginAttribute($value)) {
            case 'email':
                return app(GedisaMail::class);

            case 'username':
            default:
                return 'alpha_num';
        }
    }
}
