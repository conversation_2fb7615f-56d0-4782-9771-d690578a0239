<?php

namespace App\Helper;

use App\Http\Integrations\CardLinkService\Requests\GetChannelsRequest;
use App\Http\Integrations\CardLinkService\Requests\GetTransactionUsageRequest;
use Saloon\Http\Faking\MockClient;
use Saloon\Http\Faking\MockResponse;

class CardLinkServiceMockHelper
{
    public static function getMockClient(): MockClient
    {
        return new MockClient([
            GetChannelsRequest::class => MockResponse::make([
                'gedisa_id' => 'ABC12345',
                'channels' => [
                    [
                        'channel_external_id' => 1,
                        'channel_name' => 'apoguide-web',
                        'vendor_external_id' => 1,
                        'vendor_name' => 'GEDISA',
                        'website' => 'http://localhost',
                        'is_active' => true,
                    ],
                    [
                        'channel_external_id' => 2,
                        'channel_name' => 'ihre-apotheken-web',
                        'vendor_external_id' => 2,
                        'vendor_name' => 'IhreApotheken',
                        'website' => 'http://localhost',
                        'is_active' => true,
                    ],
                    [
                        'channel_external_id' => 3,
                        'channel_name' => 'gesund-de-web',
                        'vendor_external_id' => 3,
                        'vendor_name' => 'gesund.de',
                        'website' => 'http://localhost',
                        'is_active' => false,
                    ],
                    [
                        'channel_external_id' => 4,
                        'channel_name' => 'wave-web',
                        'vendor_external_id' => 4,
                        'vendor_name' => 'Wave',
                        'website' => 'http://localhost',
                        'is_active' => false,
                    ],
                ],
            ]),
            GetTransactionUsageRequest::class => MockResponse::make([
                'usage' => 75,
                'vendors' => [
                    [
                        'id' => 'apoguide',
                        'usage' => 75,
                        'channels' => [
                            [
                                'id' => 'apoguide-web',
                                'usage' => 50,
                            ],
                            [
                                'id' => 'apoguide-app',
                                'usage' => 25,
                            ],
                        ],
                    ],
                ],
            ]),
        ]);
    }
}
