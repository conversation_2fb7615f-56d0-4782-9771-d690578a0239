<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperGoodsManagementSystem
 *
 * @property int $id
 * @property string|null $company
 * @property string|null $system_id
 * @property string|null $system_name
 * @property string|null $tag
 * @property string|null $city
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Pharmacy> $pharmacies
 * @property-read int|null $pharmacies_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereSystemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereSystemName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereTag($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GoodsManagementSystem whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class GoodsManagementSystem extends Model
{
    protected $guarded = [];

    public function pharmacies()
    {
        return $this->hasMany(Pharmacy::class);
    }
}
