<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class AssociationSetting
 *
 * @mixin IdeHelperAssociationSetting
 *
 * @property int $association_id
 * @property int $can_vaccinate
 * @property int $can_vaccinate_model
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Association $association
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting whereAssociationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting whereCanVaccinate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting whereCanVaccinateModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationSetting whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class AssociationSetting extends Model
{
    protected $primaryKey = 'association_id';

    public $incrementing = false;

    protected $fillable = [
        'association_id',
        'can_vaccinate',
    ];

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }
}
