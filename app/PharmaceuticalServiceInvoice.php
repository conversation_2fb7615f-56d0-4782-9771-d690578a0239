<?php

namespace App;

use App\Enums\PharmaceuticalServiceInvoiceStatus;
use App\Mail\PharmaceuticalServiceInvoiceFinishedMail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class PharmaceuticalServiceInvoice extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('invoice')
            ->singleFile()
            ->useDisk('pharmaceutical-service-invoices');
    }

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isFinished()
    {
        return $this->status == PharmaceuticalServiceInvoiceStatus::GENERATED;
    }

    public function finished(bool $notify = true)
    {
        $this->update([
            'status' => PharmaceuticalServiceInvoiceStatus::GENERATED,
        ]);

        if ($notify) {
            Mail::to($this->user->routeEmailsTo)->send(new PharmaceuticalServiceInvoiceFinishedMail($this->user, $this));
        }
    }

    public function scopeStatus(Builder $query, $status)
    {
        return $query->where('status', $status);
    }
}
