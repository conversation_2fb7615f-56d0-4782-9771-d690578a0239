<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class UserPharmacyProfile
 *
 * @mixin IdeHelperUserPharmacyProfile
 */
class UserPharmacyProfile extends Model
{
    use HasFactory;

    protected $primaryKey = 'user_id';

    public $incrementing = false;

    protected $fillable = [
        'user_id',
        'association_id',
        'announcement_modal_dismiss',
        'company_user_id',
    ];

    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function companyHeadUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'company_user_id');
    }
}
