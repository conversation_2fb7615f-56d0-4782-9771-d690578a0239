<?php

namespace App;

use App\Enums\NewsStatusEnum;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * Class News
 *
 * @mixin IdeHelperNews
 *
 * @property int $id
 * @property string $slug
 * @property string $title
 * @property string $excerpt
 * @property string $text
 * @property string $seo_title
 * @property string $seo_description
 * @property int $author_id
 * @property int $status
 * @property \Illuminate\Support\Carbon $release_date
 * @property bool $visible_extern
 * @property bool $visible_intern
 * @property bool $with_login_wall
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Author|null $author
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Category> $categories
 * @property-read int|null $categories_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Tag> $tags
 * @property-read int|null $tags_count
 *
 * @method static \Database\Factories\NewsFactory factory($count = null, $state = [])
 * @method static Builder<static>|News newModelQuery()
 * @method static Builder<static>|News newQuery()
 * @method static Builder<static>|News public()
 * @method static Builder<static>|News query()
 * @method static Builder<static>|News released()
 * @method static Builder<static>|News visibility($type = null)
 * @method static Builder<static>|News whereAuthorId($value)
 * @method static Builder<static>|News whereCreatedAt($value)
 * @method static Builder<static>|News whereExcerpt($value)
 * @method static Builder<static>|News whereId($value)
 * @method static Builder<static>|News whereReleaseDate($value)
 * @method static Builder<static>|News whereSeoDescription($value)
 * @method static Builder<static>|News whereSeoTitle($value)
 * @method static Builder<static>|News whereSlug($value)
 * @method static Builder<static>|News whereStatus($value)
 * @method static Builder<static>|News whereText($value)
 * @method static Builder<static>|News whereTitle($value)
 * @method static Builder<static>|News whereUpdatedAt($value)
 * @method static Builder<static>|News whereVisibleExtern($value)
 * @method static Builder<static>|News whereVisibleIntern($value)
 * @method static Builder<static>|News whereWithLoginWall($value)
 *
 * @mixin \Eloquent
 */
class News extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'release_date' => 'datetime',
        'with_login_wall' => 'boolean',
        'visible_intern' => 'boolean',
        'visible_extern' => 'boolean',
    ];

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('masonry')
            ->width(650)
            ->sharpen(10)
            ->performOnCollections('header');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('header')
            ->singleFile()
            ->withResponsiveImages();
    }

    /** RELATIONS */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class)->withTimestamps();
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(Author::class);
    }

    /** SCOPES */
    public function scopeReleased(Builder $query)
    {
        return $query
            ->where('release_date', '<=', Carbon::now())
            ->where('status', NewsStatusEnum::PUBLISHED);
    }

    public function scopeVisibility(Builder $query, $type = null)
    {
        if ($type) {
            $query->where($type == 'blog' ? 'visible_intern' : 'visible_extern', true);
        }

        return $query;
    }

    public function scopePublic(Builder $query)
    {
        return $query
            ->where('release_date', '<=', Carbon::now())
            ->where('status', NewsStatusEnum::PUBLISHED)
            ->where('visible_extern', '=', 1);
    }

    /** METHODS */
    public function isReleased()
    {
        if (now() < $this->release_date) {
            return false;
        }

        if (in_array($this->status, [NewsStatusEnum::PUBLISHED])) {
            return true;
        }

        return false;
    }

    public function shouldDisplayLoginWall()
    {
        return ! user() && $this->with_login_wall === true;
    }
}
