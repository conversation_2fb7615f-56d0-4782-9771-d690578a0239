<?php

namespace App;

use App\Enums\ApomailStatus;
use App\Rules\NotOnBlacklist;
use App\Rules\OnlyAllowedMailCharacters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Validator;

class Apomail extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'apomail_user');
    }

    public function scopeActive(Builder $builder): Builder
    {
        return $builder->where('status', ApomailStatus::ACTIVE);
    }

    public function scopeUnused(Builder $builder): Builder
    {
        return $builder->whereNull('idp_user_id')
            ->whereNull('idp_user_uuid')
            ->whereNull('idp_user_name');
    }

    public function scopeUsedInIDP(Builder $builder): Builder
    {
        return $builder->whereNotNull('idp_user_id')
            ->orWhereNotNull('idp_user_uuid');
    }

    public function attachUsers(array $userIds): void
    {
        $this->users()->attach($userIds);
    }

    public function detachUsers(array $userIds): void
    {
        $this->users()->detach($userIds);
    }

    public function notOnBlacklist(): bool
    {
        $email = $this->email;
        $validator = Validator::make(compact('email'), [
            'email' => new NotOnBlacklist,
        ]);

        return $validator->passes();
    }

    public function hasOnlyAllowedCharacters(): bool
    {
        $email = $this->email;
        $validator = Validator::make(compact('email'), [
            'email' => new OnlyAllowedMailCharacters,
        ]);

        return $validator->passes();
    }
}
