<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InstitutionIdentifier extends Model
{
    use HasFactory;

    protected $fillable = [
        'identifier', 'name', 'address', 'zip', 'city',
    ];

    public function healthInsuranceCompany(): BelongsTo
    {
        return $this->belongsTo(HealthInsuranceCompany::class);
    }
}
