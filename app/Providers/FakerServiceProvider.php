<?php

namespace App\Providers;

use App\Faker\PharmacyProvider;
use Faker\Factory;
use Faker\Generator;
use Illuminate\Support\ServiceProvider;

class FakerServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(Generator::class, function () {
            $faker = Factory::create();
            $faker->addProvider(new PharmacyProvider($faker));

            return $faker;
        });
    }

    public function boot()
    {
        //
    }
}
