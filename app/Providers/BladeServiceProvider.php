<?php

namespace App\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class BladeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerSpacelessDirective();
    }

    public function registerSpacelessDirective()
    {
        // Register the Starting Tag
        Blade::directive('spaceless', function () {
            return '<?php ob_start() ?>';
        });

        // Register the Ending Tag
        Blade::directive('endspaceless', function () {
            return "<?php echo preg_replace('/>\\s+</', '><', ob_get_clean()); ?>";
        });
    }
}
