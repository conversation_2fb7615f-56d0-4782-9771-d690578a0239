<?php

namespace App;

use App\Jobs\ImportVaccinationImportBatchNumbers;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class VaccinationImportBatchImport extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('upload')
            ->useDisk('private')
            ->singleFile();
    }

    protected static function booted()
    {
        static::created(function (VaccinationImportBatchImport $import) {
            ImportVaccinationImportBatchNumbers::dispatch($import)->delay(2);
        });
    }
}
