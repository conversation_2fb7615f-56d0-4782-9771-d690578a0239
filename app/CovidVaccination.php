<?php

namespace App;

use App\Enums\Vaccinate\CovidVaccinationInvoiceableActions;
use App\Enums\Vaccinate\HomeVisitTypeEnum;
use App\Enums\Vaccinate\VaccinationStatus;
use App\Support\Calendar;
use App\Support\RKIVaccinationSurveillanceApi;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection;

/**
 * @property int $foo
 * @property int $id
 * @property int $vaccination_id
 * @property int|null $vaccination_type
 * @property int $rki_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $home_visit
 * @property int|null $home_visit_type
 * @property array<array-key, mixed>|null $psn
 * @property int $had_vaccination_import_id
 * @property-read Collection $invoiceable_actions
 * @property-read \App\Vaccination $vaccination
 *
 * @method static \Database\Factories\CovidVaccinationFactory factory($count = null, $state = [])
 * @method static Builder<static>|CovidVaccination newModelQuery()
 * @method static Builder<static>|CovidVaccination newQuery()
 * @method static Builder<static>|CovidVaccination query()
 * @method static Builder<static>|CovidVaccination successfullyFinished()
 * @method static Builder<static>|CovidVaccination whereCreatedAt($value)
 * @method static Builder<static>|CovidVaccination whereHadVaccinationImportId($value)
 * @method static Builder<static>|CovidVaccination whereHomeVisit($value)
 * @method static Builder<static>|CovidVaccination whereHomeVisitType($value)
 * @method static Builder<static>|CovidVaccination whereId($value)
 * @method static Builder<static>|CovidVaccination wherePsn($value)
 * @method static Builder<static>|CovidVaccination whereRkiStatus($value)
 * @method static Builder<static>|CovidVaccination whereUpdatedAt($value)
 * @method static Builder<static>|CovidVaccination whereVaccinationId($value)
 * @method static Builder<static>|CovidVaccination whereVaccinationType($value)
 *
 * @mixin \Eloquent
 */
class CovidVaccination extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'psn' => 'array',
    ];

    public function vaccination(): BelongsTo
    {
        return $this->belongsTo(Vaccination::class);
    }

    public function getInvoiceableActionsAttribute(): Collection
    {
        $actions = [];

        if (
            ! empty($this->vaccination->pharmacy->homeAddress->state)
            && Calendar::isHolidayOrWeekend($this->vaccination->date, $this->vaccination->pharmacy->homeAddress->state)
        ) {
            $actions[] = CovidVaccinationInvoiceableActions::VACCINATION_WEEKEND_HOLIDAY;
        } else {
            $actions[] = CovidVaccinationInvoiceableActions::VACCINATION;
        }

        if ($this->home_visit) {
            if ($this->home_visit_type === HomeVisitTypeEnum::FIRST) {
                $actions[] = $this->getHomeVisitFirst($this->vaccination?->date);
            } else {
                $actions[] = $this->getHomeVisitFollowing(Carbon::make($this->vaccination?->date));
            }
        }

        if ($this->had_vaccination_import_id) {
            $actions[] = $this->getCertificate();
        }

        return collect($actions);
    }

    public function scopeSuccessfullyFinished(Builder $query): Builder
    {
        return $query->whereHas('vaccination', function ($query) {
            return $query->whereNull('reasons_to_abort')->where('status', VaccinationStatus::FINISHED);
        });
    }

    public function isPseudomizedCorrectly(): bool
    {
        return RKIVaccinationSurveillanceApi::validatePseudomizationResponseContent($this->psn);
    }

    public function getHomeVisitFirst(?Carbon $vaccinationDate = null): int|string
    {
        return $vaccinationDate !== null && $vaccinationDate->isBefore(Carbon::make('2023-10-01 00:00:00'))
            ? CovidVaccinationInvoiceableActions::HOME_VISIT_FIRST
            : CovidVaccinationInvoiceableActions::HOME_VISIT_FIRST_WITHOUT_REMUNERATION;
    }

    public function getHomeVisitFollowing(?Carbon $vaccinationDate = null): int|string
    {
        return $vaccinationDate !== null && $vaccinationDate->isBefore(Carbon::make('2023-10-01 00:00:00'))
            ? CovidVaccinationInvoiceableActions::HOME_VISIT_FOLLOWING
            : CovidVaccinationInvoiceableActions::HOME_VISIT_FOLLOWING_WITHOUT_REMUNERATION;
    }

    public function getCertificate(): int|string
    {
        return $this->created_at?->isBefore(Carbon::make('2023-07-01 00:00:00'))
            ? CovidVaccinationInvoiceableActions::CERTIFICATE
            : CovidVaccinationInvoiceableActions::CERTIFICATE_WITHOUT_REMUNERATION;
    }
}
