<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class HealthInsuranceCompany
 *
 * @mixin IdeHelperHealthInsuranceCompany
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $name
 * @property int $vaccinate_enabled
 * @property int $is_private
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Association> $associations
 * @property-read int|null $associations_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\InstitutionIdentifier> $institutionIdentifiers
 * @property-read int|null $institution_identifiers_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Vaccination> $vaccinations
 * @property-read int|null $vaccinations_count
 *
 * @method static \Database\Factories\HealthInsuranceCompanyFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany whereIsPrivate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|HealthInsuranceCompany whereVaccinateEnabled($value)
 *
 * @mixin \Eloquent
 */
class HealthInsuranceCompany extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function associations(): BelongsToMany
    {
        return $this->belongsToMany(Association::class)->withTimestamps();
    }

    public function vaccinations(): HasMany
    {
        return $this->hasMany(Vaccination::class);
    }

    public function hasAssociation(Association $association): bool
    {
        return (bool) $this->associations()->find($association);
    }

    public function institutionIdentifiers(): HasMany
    {
        return $this->hasMany(InstitutionIdentifier::class);
    }
}
