<?php

namespace App;

use App\Actions\AnonymizeVaccinationAction;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;

/**
 * Class VaccinationPatient
 *
 * @mixin IdeHelperVaccinationPatient
 */
class VaccinationPatient extends Model
{
    use HasFactory;

    protected $primaryKey = 'vaccination_id';

    public $incrementing = false;

    protected $encrypted = [
        'birthdate',
        'first_name',
        'last_name',
        'insurance_number',
        'optional_address_line',
        'street',
        'house_number',
        'postcode',
        'city',
        'email',
        'phone',
        'age',
    ];

    protected $guarded = [];

    public function vaccination(): BelongsTo
    {
        return $this->belongsTo(Vaccination::class);
    }

    public function getBirthdateAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setBirthdateAttribute($value)
    {
        if (! $value) {
            $this->attributes['birthdate'] = null;

            return;
        }
        $this->attributes['birthdate'] = Crypt::encrypt($value);
    }

    public function getFirstNameAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setFirstNameAttribute($value)
    {
        if (! $value) {
            $this->attributes['first_name'] = null;

            return;
        }
        $this->attributes['first_name'] = Crypt::encrypt($value);
    }

    public function getLastNameAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setLastNameAttribute($value)
    {
        if (! $value) {
            $this->attributes['last_name'] = null;

            return;
        }
        $this->attributes['last_name'] = Crypt::encrypt($value);
    }

    public function getInsuranceNumberAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setInsuranceNumberAttribute($value)
    {
        if (! $value) {
            $this->attributes['insurance_number'] = null;

            return;
        }
        $this->attributes['insurance_number'] = Crypt::encrypt($value);
    }

    public function getStreetAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setStreetAttribute($value)
    {
        if (! $value) {
            $this->attributes['street'] = null;

            return;
        }
        $this->attributes['street'] = Crypt::encrypt($value);
    }

    public function getHouseNumberAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setHouseNumberAttribute($value)
    {
        if (! $value) {
            $this->attributes['house_number'] = null;

            return;
        }
        $this->attributes['house_number'] = Crypt::encrypt($value);
    }

    public function getOptionalAddressLineAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setOptionalAddressLineAttribute($value)
    {
        if (! $value) {
            $this->attributes['optional_address_line'] = null;

            return;
        }
        $this->attributes['optional_address_line'] = Crypt::encrypt($value);
    }

    public function getPostcodeAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setPostcodeAttribute($value)
    {
        $this->attributes['postcode'] = Crypt::encrypt($value);
    }

    public function getCityAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setCityAttribute($value)
    {
        $this->attributes['city'] = Crypt::encrypt($value);
    }

    public function getEmailAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setEmailAttribute($value)
    {
        if (! $value) {
            $this->attributes['email'] = null;

            return;
        }
        $this->attributes['email'] = Crypt::encrypt($value);
    }

    public function getPhoneAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setPhoneAttribute($value)
    {
        if (! $value) {
            $this->attributes['phone'] = null;

            return;
        }
        $this->attributes['phone'] = Crypt::encrypt($value);
    }

    public function getAgeAttribute($value)
    {
        return $value ? Crypt::decrypt($value) : null;
    }

    public function setAgeAttribute($value)
    {
        $this->attributes['age'] = Crypt::encrypt($value);
    }

    public function isAnonymized(): bool
    {
        foreach (array_keys(AnonymizeVaccinationAction::$anonymizedData) as $attribute) {
            if ($this->$attribute !== null) {
                return false;
            }
        }

        return true;
    }
}
