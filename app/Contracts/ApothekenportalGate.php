<?php

namespace App\Contracts;

use Illuminate\Support\Facades\Gate;
use ReflectionClass;
use ReflectionException;
use RuntimeException;

/**
 * @deprecated https://gedisa.atlassian.net/wiki/spaces/A/pages/85065781/Development+Guidelines#App%2FGates-(Deprecated)
 */
class ApothekenportalGate
{
    protected static string $prefix;

    /**
     * @throws ReflectionException
     */
    final public static function registerPublicMethodsAsGates(): void
    {
        if (! isset(static::$prefix)) {
            throw new RuntimeException('You must override and define the $prefix property in your Gate class');
        }
        $className = static::class;

        $classReflection = new ReflectionClass($className);
        $methods = $classReflection->getMethods();

        foreach ($methods as $method) {
            if (! $method->isPublic() || $method->isConstructor() || $method->getName() === 'registerPublicMethodsAsGates') {
                continue;
            }

            $gateName = static::$prefix.'.'.$method->getName();

            Gate::define($gateName, $className.'@'.$method->getName());
        }
    }
}
