<?php

namespace App\Mail;

use App\Pharmacy;
use App\SubscriptionCancellation;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SubscriptionCancelledMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public Pharmacy $pharmacy,
        public SubscriptionCancellation $cancellation
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Ihre Mitgliedschaft wurde gekündigt',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.subscription.cancelled',
        );
    }
}
