<?php

namespace App\Mail\CardLink;

use App\User;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class CardLinkReserved extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public User $user,
        public Collection $pharmacies,
        public Carbon $orderedAt
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Bestätigung Vorreservierung CardLink',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.card-link.reserved',
            with: [
                'customerName' => $this->user->greeting,
            ],
        );
    }
}
