<?php

namespace App\Mail\CardLink;

use App\CardLinkOrder;
use App\Data\CardLink\OrderInformationData;
use App\Enums\CardLink\CardLinkPackageChangeEnum;
use App\Pharmacy;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use RuntimeException;

class CardLinkPackageChangedMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public User $user,
        public CardLinkOrder $cardLinkOrder,
        public CardLinkPackageChangeEnum $changeType,
    ) {
        if ($this->changeType !== CardLinkPackageChangeEnum::Downgrade && $this->changeType !== CardLinkPackageChangeEnum::Upgrade) {
            throw new RuntimeException('This Mail should only be sent for Up or Downgrade.');
        }
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Ihre Bestellbestätigung zu CardLink',
        );
    }

    public function content(): Content
    {
        $orderInformationData = OrderInformationData::fromCardLinkOrder($this->cardLinkOrder);
        assert($orderInformationData?->package !== null);
        assert($this->cardLinkOrder->pharmacy instanceof Pharmacy);

        return new Content(
            markdown: 'mail.card-link.change-package',
            with: [
                'customerName' => $this->user->greeting,
                'pharmacyName' => $this->cardLinkOrder->pharmacy->name,
                'packageLabel' => $orderInformationData->package->label(),
                'changeType' => $this->changeType->label(),
            ],
        );
    }
}
