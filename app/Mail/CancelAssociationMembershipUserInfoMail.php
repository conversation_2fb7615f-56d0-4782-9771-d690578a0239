<?php

namespace App\Mail;

use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class CancelAssociationMembershipUserInfoMail extends Mailable
{
    use Queueable, SerializesModels;

    protected User $user;

    protected int $associationId;

    protected Carbon $terminationDate;

    public function __construct(User $user, int $associationId, Carbon $terminationDate)
    {
        $this->user = $user;
        $this->associationId = $associationId;
        $this->terminationDate = $terminationDate;
    }

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Achtung: Auflösung der Verbandsmitgliedschaft im Portal',
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.cancel-association-membership-user-info',
            with: [
                'user' => $this->user,
                'associationId' => $this->associationId,
                'terminationDate' => $this->terminationDate,
                'deadlineDate' => $this->getDeadlineForTosAndSubscription(
                    Carbon::make($this->terminationDate)
                ),
            ]
        );
    }

    public function getDeadlineForTosAndSubscription(Carbon $date): Carbon
    {
        return $date <= now()
            ? now()->addMonthsNoOverflow()->addDay()
            : $date->endOfMonth()->addMonthsNoOverflow()->addDay();
    }
}
