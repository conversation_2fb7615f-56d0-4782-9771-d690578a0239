<?php

namespace App\Mail;

use App\Domains\Subscription\Application\StripeProducts\AddOns\KimProduct;
use App\KimAddress;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;

class KimAddressOrderConfirmationMail extends Mailable implements ShouldQueue
{
    public function __construct(
        public KimAddress $kimAddress,
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'KIM-Adresse - Bestellbestätigung'
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.kim-address.order-confirmation',
            with: [
                'customerName' => $this->kimAddress->owner()->greeting,
                'kimAddressEmail' => $this->kimAddress->email,
                'priceText' => KimProduct::make()->getPriceText(),
            ]
        );
    }
}
