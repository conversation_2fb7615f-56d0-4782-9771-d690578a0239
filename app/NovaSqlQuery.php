<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NovaSqlQuery extends Model
{
    protected $fillable = [
        'staff_id',
        'name',
        'query',
        'params',
    ];

    protected $casts = [
        'params' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($model) {
            if (auth()->check() && $staffUser = auth()->user()) {
                assert($staffUser instanceof Staff);

                $model->staff_id = $staffUser->id;
            }
        });
    }

    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class);
    }
}
