<?php

namespace App;

use App\Enums\Vaccinate\InfluenzaVaccinationInvoiceStatus;
use App\Mail\InfluenzaVaccinationInvoiceFinishedMail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property int|null $user_id
 * @property \Illuminate\Support\Carbon $start_date
 * @property \Illuminate\Support\Carbon $end_date
 * @property float|null $price
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \App\Pharmacy $pharmacy
 * @property-read \App\User|null $user
 *
 * @method static Builder<static>|InfluenzaVaccinationInvoice newModelQuery()
 * @method static Builder<static>|InfluenzaVaccinationInvoice newQuery()
 * @method static Builder<static>|InfluenzaVaccinationInvoice query()
 * @method static Builder<static>|InfluenzaVaccinationInvoice status($status)
 * @method static Builder<static>|InfluenzaVaccinationInvoice whereCreatedAt($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice whereEndDate($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice whereId($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice wherePharmacyId($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice wherePrice($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice whereStartDate($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice whereStatus($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice whereUpdatedAt($value)
 * @method static Builder<static>|InfluenzaVaccinationInvoice whereUserId($value)
 *
 * @mixin \Eloquent
 */
class InfluenzaVaccinationInvoice extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('invoice')
            ->singleFile()
            ->useDisk('influenza-vaccinations-invocies');
    }

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isFinished()
    {
        return $this->status == InfluenzaVaccinationInvoiceStatus::GENERATED;
    }

    public function finished(bool $notify = true)
    {
        $this->update([
            'status' => InfluenzaVaccinationInvoiceStatus::GENERATED,
        ]);

        if ($notify) {
            Mail::to($this->user->routeEmailsTo)->send(new InfluenzaVaccinationInvoiceFinishedMail($this->user, $this));
        }
    }

    public function scopeStatus(Builder $query, $status)
    {
        return $query->where('status', $status);
    }
}
