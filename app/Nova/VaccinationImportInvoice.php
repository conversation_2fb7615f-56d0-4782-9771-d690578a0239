<?php

namespace App\Nova;

use App\Enums\StaffRoleEnum;
use App\Enums\VaccinationImport\InvoiceStatusEnum;
use App\VaccinationImport;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class VaccinationImportInvoice extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\VaccinationImportInvoice::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public static function label(): string
    {
        return 'Impfimport Rechnungen';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),

            Number::make('year')->readonly()->sortable(),

            Number::make('month')->readonly()->sortable(),

            BelongsTo::make('pharmacy')->readonly(),

            BelongsTo::make('user')->readonly(),

            Select::make('status')
                ->options(InvoiceStatusEnum::getForDropdown())
                ->readonly(),

            Text::make('PDF', function () {
                return "<a target='_blank' class='no-underline dim text-primary font-bold' href='".route('nova.vaccination-import-invoice.download', $this->id)."'>PDF</a>";
            })->asHtml()
                ->showOnIndex(function (): bool {
                    return auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN) ?? false;
                })
                ->showOnDetail(function (): bool {
                    return auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN) ?? false;
                })
                ->readonly(),

            Text::make('ausgestellte Zertifikate im Zeitraum', function () {
                return VaccinationImport::query()
                    ->where('pharmacy_id', $this->pharmacy_id)
                    ->whereMonth('created_at', $this->month)
                    ->whereYear('created_at', $this->year)
                    ->whereNull('covid_vaccination_id')
                    ->count();
            }),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [
            (new Actions\RegenerateVaccinationImportInvoice)->showInline(),
        ];
    }
}
