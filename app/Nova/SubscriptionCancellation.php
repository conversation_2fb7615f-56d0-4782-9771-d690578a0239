<?php

namespace App\Nova;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Nova\Actions\Subscription\RevokeCancellationNovaAction;
use App\SubscriptionCancellation as SubscriptionCancellationModel;
use <PERSON><PERSON>\Nova\Fields\Badge;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Resource;

class SubscriptionCancellation extends Resource
{
    public static $model = SubscriptionCancellationModel::class;

    public static $title = 'id';

    public static $search = [
        'id', 'pharmacy.name',
    ];

    public static $group = 'Mitgliedschaft';

    public static function label(): string
    {
        return 'Kündigungen';
    }

    public static function singularLabel(): string
    {
        return 'Kündigung';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Apotheke', 'pharmacy', Pharmacy::class)
                ->searchable()
                ->sortable(),

            BelongsTo::make('Gekündigt von', 'requestedBy', User::class)
                ->searchable()
                ->sortable(),

            DateTime::make('Beantragt am', 'requested_at')
                ->sortable(),

            Badge::make('Status', 'status')
                ->map([
                    SubscriptionCancellationStatusEnum::PENDING->value => 'warning',
                    SubscriptionCancellationStatusEnum::CONFIRMED->value => 'info',
                    SubscriptionCancellationStatusEnum::REVOKED->value => 'danger',
                    SubscriptionCancellationStatusEnum::EXECUTED->value => 'success',
                ])
                ->labels([
                    SubscriptionCancellationStatusEnum::PENDING->value => SubscriptionCancellationStatusEnum::PENDING->label(),
                    SubscriptionCancellationStatusEnum::CONFIRMED->value => SubscriptionCancellationStatusEnum::CONFIRMED->label(),
                    SubscriptionCancellationStatusEnum::REVOKED->value => SubscriptionCancellationStatusEnum::REVOKED->label(),
                    SubscriptionCancellationStatusEnum::EXECUTED->value => SubscriptionCancellationStatusEnum::EXECUTED->label(),
                ])
                ->sortable(),

            DateTime::make('Kündigungsdatum', 'effective_date')
                ->sortable(),

            DateTime::make('Bestätigt am', 'confirmed_at')
                ->hideFromIndex()
                ->nullable(),

            DateTime::make('Ausgeführt am', 'executed_at')
                ->hideFromIndex()
                ->nullable(),

            BelongsTo::make('Widerrufen von', 'revokedBy', User::class)
                ->searchable()
                ->sortable(),

            DateTime::make('Widerrufen am', 'revoked_at')
                ->hideFromIndex()
                ->nullable(),

            Textarea::make('Grund', 'reason')
                ->hideFromIndex()
                ->nullable(),

            Text::make('Metadaten', function () {
                if (! $this->metadata) {
                    return '-';
                }

                return json_encode($this->metadata, JSON_PRETTY_PRINT);
            })
                ->asHtml()
                ->hideFromIndex()
                ->onlyOnDetail(),

            DateTime::make('Erstellt am', 'created_at')
                ->hideFromIndex()
                ->sortable(),

            DateTime::make('Aktualisiert am', 'updated_at')
                ->hideFromIndex()
                ->sortable(),
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            RevokeCancellationNovaAction::make()
                ->canSee(function () {
                    return in_array($this->resource->status, [
                        SubscriptionCancellationStatusEnum::PENDING,
                        SubscriptionCancellationStatusEnum::CONFIRMED,
                    ]) && $this->resource->canBeRevoked();
                })
                ->canRun(function ($request, $model) {
                    dump($model);

                    return $model->canBeRevoked();
                }),
        ];
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->with(['pharmacy', 'requestedBy', 'revokedBy']);
    }
}
