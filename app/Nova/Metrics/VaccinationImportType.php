<?php

namespace App\Nova\Metrics;

use App\VaccinationImport;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Partition;

class VaccinationImportType extends Partition
{
    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $vaccination = VaccinationImport::query()
            ->where('is_recovered', false)
            ->where('is_recovered_only', false)
            ->count();

        $vaccinationRecovered = VaccinationImport::query()
            ->where('is_recovered', true)
            ->where('is_recovered_only', false)
            ->count();

        $recoveredOnly = VaccinationImport::query()
            ->where('is_recovered', false)
            ->where('is_recovered_only', true)
            ->count();

        return $this->result([
            'Impfung' => $vaccination,
            'Genesenenimpfung' => $vaccinationRecovered,
            'Genesen' => $recoveredOnly,
        ])->colors([
            'Impfung' => 'green',
            'Genesenenimpfung' => 'red',
            '<PERSON>sen' => 'darkblue',
        ]);
    }

    /**
     * Get the displayable name of the metric
     *
     * @return string
     */
    public function name()
    {
        return 'Zertifikate Typen';
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        //         return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'vaccination-import-type';
    }
}
