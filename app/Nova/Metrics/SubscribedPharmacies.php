<?php

namespace App\Nova\Metrics;

use App\Enums\AssociationEnum;
use App\Pharmacy;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\Partition;

class SubscribedPharmacies extends Partition
{
    /**
     * Calculate the value of the metric.
     *
     * @return mixed
     */
    public function calculate(NovaRequest $request)
    {
        $payed_member = Pharmacy::query()
            ->whereHas('users', function ($query) {
                $query
                    ->onlyOwner()
                    ->whereHas('pharmacyProfile', function ($query) {
                        return $query->where('association_id', '!=', AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V)->whereNotNull('association_id');
                    });
            })
            ->acceptedTermsOfUse()
            ->count();

        return $this->result([
            'Gesellschafter' => $payed_member,
            'WL Basisversion' => $this->countPharmacies('base', AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V),
            'WL Vollversion' => $this->countPharmacies('extended', AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V),
            'Extern Basisversion' => $this->countPharmacies('base', null),
            'Extern Vollversion' => $this->countPharmacies('extended', null),
        ])->colors([
            'Gesellschafter' => 'lightblue',
            'WL Basisversion' => 'green',
            'WL Vollversion' => 'red',
            'Extern Basisversion' => 'darkblue',
            'Extern Vollversion' => 'orange',
        ]);
    }

    private function countPharmacies(string $plan, ?int $association_id): int
    {
        return Pharmacy::query()
            ->whereHas('oldSubscriptions', function ($query) use ($plan) {
                return $query->where('plan', $plan)->where(fn ($q) => $q->where('ends_at', '>', now())->orWhereNull('ends_at'));
            })
            ->whereHas('users', function ($query) use ($association_id) {
                $query
                    ->onlyOwner()
                    ->whereHas('pharmacyProfile', function ($query) use ($association_id) {
                        if ($association_id) {
                            return $query->where('association_id', $association_id);
                        } else {
                            return $query->whereNull('association_id');
                        }
                    });
            })
            ->acceptedTermsOfUse()
            ->count();
    }

    /**
     * Get the displayable name of the metric
     *
     * @return string
     */
    public function name()
    {
        return 'Apotheken - Gesamt';
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return \DateTimeInterface|\DateInterval|float|int
     */
    public function cacheFor()
    {
        // return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'subscribed-pharmacies';
    }
}
