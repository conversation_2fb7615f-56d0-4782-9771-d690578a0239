<?php

namespace App\Nova\Metrics\ManagementReportMetrics;

use App\Domains\Subscription\Application\FeatureAccess\KimFeatureAccess;
use App\Enums\KimAddressStatus;
use App\KimAddress;
use Laravel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Metrics\PartitionResult;

class KimAdressesPerAssociationTable extends ManagementReportMetric
{
    public function name(): string
    {
        return 'Aktivierte KIM Adressen pro Verband';
    }

    public function calculate(NovaRequest $request): PartitionResult
    {
        $kimAddresses = KimAddress::query()
            ->selectRaw('associations.id AS id, COUNT(kim_addresses.id) Anzahl')
            ->whereHas('pharmacy', function ($q) {
                $q->withFeatureAccess(KimFeatureAccess::class);
            })
            ->join('pharmacies', 'pharmacies.id', '=', 'kim_addresses.pharmacy_id')
            ->join('pharmacy_role_user', function ($x) {
                $x->on('pharmacy_role_user.pharmacy_id', '=', 'pharmacies.id')
                    ->where('pharmacy_role_user.role_name', 'owner');
            })
            ->join('user_pharmacy_profiles', 'user_pharmacy_profiles.user_id', '=', 'pharmacy_role_user.user_id')
            ->leftJoin('associations', 'associations.id', '=', 'user_pharmacy_profiles.association_id')
            ->groupBy('associations.id')
            ->where('kim_addresses.status', KimAddressStatus::ACTIVATED)
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->id => $item->Anzahl];
            });

        return $this->formatResult($kimAddresses->toArray());
    }
}
