<?php

namespace App\Nova;

use App\Enums\StaffRoleEnum;
use App\Enums\Vaccinate\CovidVaccinationInvoiceStatus;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\Vaccination;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class CovidVaccinationInvoice extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\CovidVaccinationInvoice::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    public static function label(): string
    {
        return 'COVID-Impfung Rechnungen';
    }

    /**
     * Get the fields displayed by the resource.
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),

            Date::make('start_date')->readonly()->sortable(),

            Date::make('end_date')->readonly()->sortable(),

            BelongsTo::make('pharmacy')->readonly(),

            BelongsTo::make('user')->readonly(),

            Text::make('PDF', function () {
                $wait = ! $this->isFinished();

                return $wait ?
                    '...' :
                    "<a target='_blank' class='no-underline dim text-primary font-bold' href='".route('nova.covid-vaccination-invoice.download', $this->id)."'>PDF</a>";
            })->asHtml()
                ->showOnIndex(function (): bool {
                    return auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN) ?? false;
                })
                ->showOnDetail(function (): bool {
                    return auth('staff')->user()?->hasRole(StaffRoleEnum::ADMIN) ?? false;
                })
                ->readonly(),

            Select::make('status')
                ->options(CovidVaccinationInvoiceStatus::getForDropdown())
                ->readonly(),

            Text::make('durchgeführte Impfungen im Zeitraum', function () {
                return Vaccination::query()
                    ->where('pharmacy_id', $this->pharmacy_id)
                    ->where('type', VaccinationTypeEnum::COVID)
                    ->where('date', '>=', $this->start_date)
                    ->where('date', '<=', $this->end_date)
                    ->where('date', '<=', $this->created_at)
                    ->count();
            }),

            DateTime::make('created_at')
                ->displayUsing(fn ($value) => $value?->format('d.m.Y H:i'))
                ->readonly()
                ->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     */
    public function filters(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     */
    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     */
    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
