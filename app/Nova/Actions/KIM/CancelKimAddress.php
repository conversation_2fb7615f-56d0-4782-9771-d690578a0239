<?php

namespace App\Nova\Actions\KIM;

use App\Enums\KimAddressStatus;
use App\KimAddress;
use App\Processes\Payloads\CancelKimAddressPayload;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionEvent;
use Laravel\Nova\Actions\DestructiveAction;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class CancelKimAddress extends DestructiveAction
{
    use InteractsWithQueue, Queueable;

    public $name = 'KIM-Adresse stornieren (Reserviert/Bestellt)';

    public function handle(ActionFields $fields, Collection $models): mixed
    {
        $failedModels = collect();

        /** @var Collection<KimAddress> $models */
        foreach ($models as $model) {
            try {
                if ($this->canCancelKimAddress($model)) {
                    /** @var string $reason */
                    $reason = $fields->get('reason');

                    $payload = new CancelKimAddressPayload(
                        kimAddress: $model,
                        customerName: $model->owner()->greeting,
                        kimAddressEmail: $model->email,
                        originalStatus: $model->status,
                        reason: $reason,
                    );

                    $model->update(['status' => KimAddressStatus::CANCELLATION_SCHEDULED->value]);

                    $process = new \App\Processes\CancelKimAddress;
                    $process->onQueue()->run($payload);
                } else {
                    $failedModels->push($model);
                }
            } catch (\Exception $exception) {
                $failedModels->push($model);
            }
        }

        if ($failedModels->isEmpty()) {
            return Action::message('Die Aktion war erfolgreich.')->withRedirect('/nova/resources/kim-addresses');
        }

        $failedModels->each(fn (KimAddress $kimAddress) => $this->createActionEvent($kimAddress));

        return Action::danger(
            sprintf(
                'Die KIM-Adressen (%s) konnten nicht storniert werden.',
                $failedModels->pluck('id')->implode(', ')
            )
        );
    }

    /** @return array<int, Field> */
    public function fields(NovaRequest $request): array
    {
        return [
            Textarea::make('Begründung', 'reason')->nullable(),
        ];
    }

    protected function canCancelKimAddress(KimAddress $kimAddress): bool
    {
        return in_array($kimAddress->status, [KimAddressStatus::RESERVED->value, KimAddressStatus::ORDERED->value], true);
    }

    protected function createActionEvent(KimAddress $kimAddress): void
    {
        ActionEvent::create([
            'batch_id' => (string) Str::orderedUuid(),
            'user_id' => auth()->user()?->getAuthIdentifier(),
            'name' => 'Cancel',
            'actionable_type' => $kimAddress->pharmacy?->getMorphClass(),
            'actionable_id' => $kimAddress->pharmacy?->getKey(),
            'target_type' => $kimAddress->getMorphClass(),
            'target_id' => $kimAddress->getKey(),
            'model_type' => $kimAddress->getMorphClass(),
            'model_id' => $kimAddress->getKey(),
            'fields' => '',
            'original' => null,
            'changes' => $kimAddress->attributesToArray(),
            'status' => 'failed',
            'exception' => '',
        ]);
    }
}
