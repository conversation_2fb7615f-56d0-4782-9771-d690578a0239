<?php

namespace App\Nova\Actions;

use App\Enums\AssociationEnum;
use App\Processes\CreateAssociationMembershipChange;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use App\User;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class AddAssociationMembership extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Verbandseintritt durchführen';

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        $failedModels = collect();
        assert(isset($fields->change_at, $fields->association_id));
        $changeDate = Carbon::parse($fields->change_at)->subDay()->startOfDay();
        $associationId = (int) $fields->association_id;

        if (! $associationId) {
            $associationId = null;
        }

        if ($models->isEmpty()) {
            $models->add(NovaRequest::createFrom(request())->findParentModel());
        }

        foreach ($models as $model) {
            assert($model instanceof User);

            if ($this->canChangeAssociationMembership($model, $associationId, $changeDate)) {
                $payload = new CreateAssociationMembershipChangePayload(
                    user: $model,
                    changeAt: $changeDate->startOfDay(),
                    old: $model->pharmacyProfile?->association_id,
                    new: $associationId,
                );

                $process = new CreateAssociationMembershipChange;
                $process->run($payload);
            } else {
                $failedModels->push($model);
            }
        }

        if ($failedModels->isNotEmpty()) {
            return Action::danger(
                'Der Vorgang konnte für Datensätze mit den folgenden IDs nicht abgeschlossen werden,'
                .' da sie entweder bereits für eine Änderung vorgemerkt wurden oder das Änderungsdatum'
                .' vor dem Eintrittsdatum liegt: '.$failedModels->pluck('id')->implode(', ')
            );
        }

        return Action::message('Der Vorgang war erfolgreich.');
    }

    /**
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        $associations = AssociationEnum::getLabels();
        $owner = $request->findParentModel();
        assert($owner instanceof User);

        /** @var array<string, string> $dates */
        $dates = collect($this->getAvailableDates($owner))
            ->mapWithKeys(
                fn (CarbonInterface $date) => [$date->toDateString() => $date->translatedFormat('j. F Y')]
            )
            ->toArray();

        return [
            Heading::make('Bitte die Dokumentation konsultieren, dort sind alle wichtigen Informationen enthalten: <a target="_blank" href="https://gedisa.atlassian.net/wiki/spaces/A/pages/1084424201/Feature+Dokumentation+f+r+die+bersicht+der+Verbandsmitglieder+Verbandseintritt+und+Verbandsaustritt#Verbandseintritt-durchf%C3%BChren"><strong>Hier klicken (öffnet im neuen Tab)</strong></a>')->asHtml(),
            Select::make('Verband', 'association_id')
                ->options($associations)
                ->displayUsingLabels()
                ->required(),
            Select::make('Wechseldatum', 'change_at')
                ->options($dates)
                ->displayUsingLabels()
                ->required(),
        ];
    }

    public function canChangeAssociationMembership(User $user, ?int $associationId, Carbon $changeDate): bool
    {
        if ($user->pharmacyProfile === null) {
            return false;
        }

        if ($user->pharmacyProfile->association_id === $associationId) {
            return false;
        }

        if ($user->currentAssociationMembershipChange && $user->currentAssociationMembershipChange->canceled_at === null) {
            return false;
        }

        return true;
    }

    /**
     * @return array<int, CarbonInterface>
     */
    protected function getAvailableDates(User $owner): array
    {
        return CarbonPeriod::create(
            $this->earliestChangeDate($owner),
            '1 quarter',
            now()->startOfQuarter()->addQuarters(4),
        )->toArray();
    }

    protected function earliestChangeDate(User $owner): Carbon
    {
        $boundary = now()->subQuartersNoOverflow(2)->startOfQuarter()->gt(Carbon::create(2025)) ? now()->subQuartersNoOverflow(2)->startOfQuarter() : Carbon::create(2025);
        assert($boundary instanceof Carbon);
        $earliestPossible = $owner->getEarliestAssociationChangeDate($boundary);

        if ($earliestPossible->isSameDay($earliestPossible->copy()->startOfQuarter())) {
            return $earliestPossible;
        }

        return $earliestPossible->addQuarterNoOverflow()->startOfQuarter();
    }
}
