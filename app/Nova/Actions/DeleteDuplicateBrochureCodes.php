<?php

namespace App\Nova\Actions;

use App\BrochureCode;
use Illuminate\Database\Eloquent\Collection;
use <PERSON>vel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;

class DeleteDuplicateBrochureCodes extends Action
{
    public $name = 'BrochureCode Duplikate entfernen';

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields, Collection $models)
    {
        $models->toQuery()->chunk(50, function ($brochureCodes) {
            foreach ($brochureCodes as $brochureCode) {
                if (BrochureCode::where('first_name', '=', $brochureCode->first_name)
                    ->where('last_name', '=', $brochureCode->last_name)
                    ->where('association_id', '=', $brochureCode->association_id)
                    ->count() > 1
                ) {
                    $brochureCode->delete();
                }
            }
        });
    }
}
