<?php

namespace App\Nova\Actions;

use App\Enums\AssociationEnum;
use App\Processes\CreateAssociationMembershipChange;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use App\User;
use App\UserPharmacyProfile;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class ChangeAssociationMembership extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Verbandsmitgliedschaft ändern';

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        $failedModels = collect();
        $changeDate = Carbon::parse($fields->change_at)->endOfMonth();
        $associationId = (int) $fields->association_id;

        if (! $associationId) {
            $associationId = null;
        }

        if ($models->isEmpty()) {
            $models->add(NovaRequest::createFrom(request())->findParentModel());
        }

        /** @var Collection<User> $models */
        foreach ($models as $model) {
            if ($this->canChangeAssociationMembership($model, $associationId, $changeDate)) {
                $payload = new CreateAssociationMembershipChangePayload(
                    user: $model,
                    changeAt: $changeDate->endOfMonth(),
                    old: $model->pharmacyProfile?->association_id,
                    new: $associationId,
                );

                $process = new CreateAssociationMembershipChange;
                $process->run($payload);
            } else {
                $failedModels->push($model);
            }
        }

        if ($failedModels->isNotEmpty()) {
            return Action::danger(
                'Der Vorgang konnte für Datensätze mit den folgenden IDs nicht abgeschlossen werden,'
                .' da sie entweder bereits für eine Änderung vorgemerkt wurden oder das Änderungsdatum'
                .' vor dem Eintrittsdatum liegt: '.$failedModels->pluck('id')->implode(', ')
            );
        }

        return Action::message('Der Vorgang war erfolgreich.');
    }

    public function fields(NovaRequest $request): array
    {
        $associations = ['Verbandslos'] + AssociationEnum::getLabels();

        return [
            Select::make('Verband', 'association_id')
                ->options($associations)
                ->displayUsingLabels(),
            Date::make('Wechseldatum', 'change_at')->default(function () {
                return Carbon::now();
            }),
        ];
    }

    public function canChangeAssociationMembership(?User $user, ?int $associationId, Carbon $changeDate): bool
    {
        return false;

        /**
         *  TODO: Redo this process
         *  AP-2406 disable change association membership
        return $user instanceof User
            && $user->pharmacyProfile instanceof UserPharmacyProfile
            && $user->pharmacyProfile->association_id !== $associationId
            && (
                $user->currentAssociationMembershipChange === null
                || $user->currentAssociationMembershipChange->canceled_at !== null
            )
            && $user->getLastPossibleDateChangingAssociationRetroactively()?->format('Y-m-d') <= $changeDate->format('Y-m-d')
            && now()->addYearNoOverflow() >= $changeDate->format('Y-m-d');
        */
    }
}
