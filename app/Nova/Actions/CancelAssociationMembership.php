<?php

namespace App\Nova\Actions;

use App\Processes\CreateAssociationMembershipChange;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use App\User;
use App\UserPharmacyProfile;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class CancelAssociationMembership extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Verbandsmitgliedschaft kündigen';

    /** @phpstan-ignore-next-line */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        $failedModels = collect();
        $terminateDate = Carbon::parse($fields->terminated_at);

        if ($models->isEmpty()) {
            $models->add(NovaRequest::createFrom(request())->findParentModel());
        }

        /** @var Collection<User> $models */
        foreach ($models as $model) {
            if ($this->canCancelAssociationMembership($model, $terminateDate)) {
                $payload = new CreateAssociationMembershipChangePayload(
                    user: $model,
                    changeAt: $terminateDate->endOfMonth(),
                    old: $model->pharmacyProfile?->association_id,
                );

                $process = new CreateAssociationMembershipChange;
                $process->run($payload);
            } else {
                $failedModels->push($model);
            }
        }

        if ($failedModels->isEmpty()) {
            return Action::message('Die Aktion war erfolgreich');
        }

        return Action::danger(
            'Die Aktion konnte für Datensätze mit den folgenden IDs nicht abgeschlossen werden, da sie '
            .'entweder bereits abschließend gekündigt wurden oder das Kündigungsdatum vor dem Eintrittsdatum liegt: '
            .$failedModels->pluck('id')->implode(', ')
        );
    }

    public function fields(NovaRequest $request): array
    {
        $owner = $request->findParentModel();
        assert($owner instanceof User);

        /** @var array<string, string> $dates */
        $dates = collect($this->getAvailableDates($owner))
            ->mapWithKeys(
                fn (CarbonInterface $date) => [$date->toDateString() => $date->translatedFormat('j. F Y')]
            )
            ->toArray();

        return [
            Heading::make('Bitte die Dokumentation konsultieren, dort sind alle wichtigen Informationen enthalten: <a target="_blank" href="https://gedisa.atlassian.net/wiki/spaces/A/pages/1084424201/Feature+Dokumentation+f+r+die+bersicht+der+Verbandsmitglieder+Verbandseintritt+und+Verbandsaustritt#Verbandsaustritt-durchf%C3%BChren"><strong>Hier klicken (öffnet im neuen Tab)</strong></a>')->asHtml(),
            Select::make('Kündigungsdatum', 'terminated_at')
                ->options($dates)
                ->displayUsingLabels()
                ->required(),
        ];
    }

    /**
     * @return array<int, CarbonInterface>
     */
    protected function getAvailableDates(User $owner): array
    {
        $period = CarbonPeriod::create(
            $this->earliestChangeDate($owner),
            '1 month',
            now()->addMonthsNoOverflow(12)->startOfMonth(),
        );

        return collect($period)
            ->map(fn (CarbonInterface $date) => $date->endOfMonth())
            ->toArray();
    }

    public function canCancelAssociationMembership(User $user, Carbon $terminateDate): bool
    {
        if ($user->pharmacyProfile instanceof UserPharmacyProfile === false) {
            return false;
        }

        if ($user->pharmacyProfile->association_id === null) {
            return false;
        }

        if ($user->currentAssociationMembershipChange !== null) {
            return false;
        }

        return true;
    }

    protected function earliestChangeDate(User $owner): Carbon
    {
        $earliestDate = $owner->getEarliestAssociationChangeDate(now()->subMonthsNoOverflow(5)->startOfMonth());

        if ($earliestDate->isSameDay($earliestDate->copy()->startOfMonth())) {
            return $earliestDate->subMonthNoOverflow();
        }

        return $earliestDate->startOfMonth();
    }
}
