<?php

namespace App\Nova\Actions\Subscription;

use App\Actions\Subscription\RevokeSubscriptionCancellationAction;
use App\SubscriptionCancellation;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

class RevokeCancellationNovaAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Kündigung widerrufen';

    public $confirmText = 'Soll die Kündigung widerrufen werden?';

    public function handle(ActionFields $fields, Collection $models): ActionResponse
    {
        if ($models->count() > 1) {
            return Action::danger('Bitte nur eine Kündigung auswählen.');
        }

        $cancellation = $models->first();
        assert($cancellation instanceof SubscriptionCancellation);

        if (! $cancellation->canBeRevoked()) {
            return Action::danger('Diese Kündigung kann nicht mehr widerrufen werden.');
        }

        app(RevokeSubscriptionCancellationAction::class)->execute(
            $cancellation->pharmacy,
            auth()->user(),
            $fields->get('notes') ?: 'Kündigung über Nova widerrufen'
        );

        return Action::message('Die Kündigung wurde erfolgreich widerrufen.');
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Textarea::make('Grund für Widerruf', 'notes')
                ->rows(3)
                ->help('Bitte geben Sie den Grund für den Widerruf an.')
                ->required(),
        ];
    }
}
