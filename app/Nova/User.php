<?php

namespace App\Nova;

use App\Enums\AssociationRoleEnum;
use App\Enums\Newsletter\MailcoachList;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\Enums\SalutationEnum;
use App\Nova\Actions\AddUserToPreflightAction;
use App\Nova\Actions\CheckUserAtIDP;
use App\Nova\Actions\ImportToIDP;
use App\Nova\Actions\ReactivateMarkedAsDeletedOwner;
use App\Nova\Filters\IsBetaTester;
use App\Rules\GedisaMail;
use App\Settings\RiseSettings;
use App\Support\FamedlyApi;
use Illuminate\Database\Eloquent\Builder;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphMany;
use Lara<PERSON>\Nova\Fields\Number;
use Laravel\Nova\Fields\Password;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Maatwebsite\LaravelNovaExcel\Actions\DownloadExcel;

/**
 * @property int $pharmacies_count
 * @property \App\UserPharmacyProfile|null $pharmacyProfile
 */
class User extends Resource
{
    public static $with = ['companyUser', 'pharmacyProfile', 'brochureCode'];

    public static string $model = \App\User::class;

    public static $relatableSearchResults = 10;

    public static $title = 'search_name';

    public static $search = [
        'phone',
        'search_name',
        'uuid',
        'organisation_uuid',
    ];

    public static $group = 'Nutzerverwaltung';

    public static function label(): string
    {
        return 'Benutzer';
    }

    public static function indexQuery(NovaRequest $request, $query): Builder
    {
        $query = parent::indexQuery($request, $query);

        return $query->withCount(['pharmacies as pharmacies_count' => function ($query) {
            $query->where('role_name', PharmacyRoleEnum::OWNER);
        }]);
    }

    public function fields(NovaRequest $request): array
    {
        $url = app(RiseSettings::class)->rise_user_profile_url ?? '';

        $iDPProfileUrl = str_replace(
            '{UUID}',
            $this->uuid ?? '',
            ! is_string($url) ? '' : $url
        );

        return [
            ID::make()->sortable(),

            Boolean::make('In IDP', 'is_at_idp')->exceptOnForms(),

            Text::make('UUID', 'uuid')->exceptOnForms()->hideFromIndex(),

            Text::make('homeserver_uuid', 'organisation_uuid')->exceptOnForms()->hideFromIndex(),

            Text::make('IDP Profil', static function () use ($iDPProfileUrl) {
                return '<a target="_blank" class="no-underline dim text-primary font-bold" href="'.$iDPProfileUrl.'">Ansehen</a>';
            })->asHtml()->hideFromIndex(),

            Text::make('Name', function () {
                return $this->nova_name;
            }),

            Text::make('Famedly Username', function (): string {
                $user = $this->resource;

                if ($user instanceof \App\User === false) {
                    return '';
                }

                return app(FamedlyApi::class)->getFullFamedlyIdForUser($user);
            })->onlyOnDetail(),

            Number::make('Anzahl Apotheken', 'number_of_pharmacies', function () {
                return $this->pharmacies_count;
            })->onlyOnExport(),

            Text::make('Mitglied seit', 'member_since', function () {
                return $this->pharmacyProfile?->created_at?->format('d.m.Y');
            })->onlyOnExport(),

            Select::make('Salutation')
                ->options(
                    SalutationEnum::getForDropdown()->map(function ($val) {
                        return trans('validation.attributes.'.$val);
                    })
                )
                ->required()
                ->displayUsingLabels()
                ->onlyOnForms(),

            Text::make('title')
                ->nullable()
                ->rules('nullable', 'max:255')
                ->onlyOnForms(),

            Text::make('First_name')
                ->sortable()
                ->rules('required', 'max:255')
                ->onlyOnForms(),

            Text::make('Last_name')
                ->sortable()
                ->rules('required', 'max:255')
                ->onlyOnForms(),

            Text::make('Phone')
                ->sortable()
                ->rules('required', 'max:255')
                ->onlyOnForms(),

            Text::make('E-Mail-Adresse für Benachrichtigungen', 'email')
                ->sortable()
                ->rules('required_without:username', app(GedisaMail::class), 'nullable')
                ->creationRules('unique:users,email')
                ->updateRules('unique:users,email,{{resourceId}}')
                ->readonly(),

            Text::make('IDP E-Mail-Adresse für den Login', 'idp_email')
                ->sortable()
                ->rules('nullable', app(GedisaMail::class))
                ->creationRules('unique:users,idp_email')
                ->updateRules('unique:users,idp_email,{{resourceId}}')
                ->readonly(),

            Boolean::make('Newsletter abonniert', function () {
                // @phpstan-ignore-next-line
                return $this->hasMailcoachSubscriptions(MailcoachList::Advertising);
            })
                ->readonly(),

            Text::make('Username')
                ->sortable()
                ->rules('required_without:email', 'alpha_num', 'max:254', 'min:6', 'nullable')
                ->creationRules('unique:users,username')
                ->updateRules('unique:users,username,{{resourceId}}'),

            Number::make('failed_login_attempts')
                ->onlyOnDetail(),

            Boolean::make('verified', function () {
                return ! is_null($this->email_verified_at);
            })->exceptOnForms(),

            Password::make('Password')
                ->onlyOnForms()
                ->creationRules('required', 'string', 'min:8')
                ->updateRules('nullable', 'string', 'min:8'),

            DateTime::make('last_login')
                ->displayUsing(fn ($value) => $value?->format('d.m.Y H:i'))
                ->onlyOnDetail(),

            DateTime::make('first_login')
                ->displayUsing(fn ($value) => $value?->format('d.m.Y H:i'))
                ->exceptOnForms()
                ->sortable(),

            DateTime::make('created_at')
                ->displayUsing(fn ($value) => $value?->format('d.m.Y H:i'))
                ->exceptOnForms()
                ->sortable(),

            DateTime::make('deleted_at')
                ->displayUsing(fn ($value) => $value?->format('d.m.Y H:i'))
                ->exceptOnForms()
                ->sortable(),

            HasOne::make('companyUser'),

            BelongsToMany::make('pharmacies')
                ->fields(function () {
                    return [
                        Select::make('role_name')
                            ->options(PharmacyRoleEnum::getForNova()),

                        Text::make('Rechte', function ($pivot) {
                            return implode(
                                '- <br>',
                                mb_str_split(
                                    collect($pivot->permissions)->map(
                                        fn ($permission) => PharmacyPermissionsEnum::getLabels()[$permission]
                                    )->implode(', '),
                                    30
                                )
                            );
                        })->asHtml(),
                    ];
                })->searchable(),

            BelongsToMany::make('associations')
                ->fields(function () {
                    return [
                        Select::make('role_name')
                            ->options(AssociationRoleEnum::getForNova()),
                    ];
                }),

            HasOne::make('PharmacyProfile', 'pharmacyProfile', UserPharmacyProfile::class),

            HasMany::make('Historie der Verbandsmitgliedschaft', 'associationMembershipHistories', AssociationMembershipHistory::class),

            HasMany::make('Änderungen der Verbandsmitgliedschaft', 'associationMembershipChanges', AssociationMembershipChange::class),

            HasOne::make('AssociationProfile', 'associationProfile', UserAssociationProfile::class),

            HasMany::make('ApoMail', 'apomails', Apomail::class),

            MorphMany::make('generalSettings', 'generalSettings', Setting::class),
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new IsBetaTester,
            new \App\Nova\Filters\Association,
        ];
    }

    public function actions(NovaRequest $request): array
    {
        /** @var \App\User|null $resource */
        $resource = $request->findModelQuery()->first();

        return [
            (new DownloadExcel)
                ->withFilename('Benutzerliste_'.now()->format('Ymd'))
                ->withHeadings('Titel', 'Vorname', 'Nachname', 'Telefon', 'E-Mail', 'Mitglied seit', 'Letzter Login', 'Anzahl der Apotheken')
                ->only('title', 'first_name', 'last_name', 'phone', 'email', 'member_since', 'last_login', 'number_of_pharmacies'),
            (new ImportToIDP)
                ->canSee(function () use ($resource) {
                    return is_null($resource) || ! $resource->is_at_idp;
                })->onlyOnDetail(),
            (new CheckUserAtIDP)->onlyOnDetail(),
            (new ReactivateMarkedAsDeletedOwner)
                ->canSee(function () use ($resource) {
                    return is_null($resource) || ReactivateMarkedAsDeletedOwner::isTosDeletedReactivatable($resource);
                })->onlyOnDetail(),
            new AddUserToPreflightAction,
        ];
    }
}
