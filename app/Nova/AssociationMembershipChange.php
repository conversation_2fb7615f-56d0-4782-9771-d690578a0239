<?php

namespace App\Nova;

use App\Nova\Actions\CancelAssociationMembership;
use App\Nova\Actions\ChangeAssociationMembership;
use App\Nova\Actions\RevertLastAssociationMembershipChangeAction;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Heading;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class AssociationMembershipChange extends Resource
{
    public static string $model = \App\AssociationMembershipChange::class;

    public static $group = 'Nutzerverwaltung';

    public static function label(): string
    {
        return 'Änderungen der Verbandsmitgliedschaften';
    }

    public function title()
    {
        return $this->id;
    }

    public static function indexQuery(NovaRequest $request, $query)
    {
        $query->whereNull(['change_done_at', 'canceled_at']);

        return parent::indexQuery($request, $query);
    }

    public function fields(NovaRequest $request): array
    {
        $heading = '<p>Die endgültige Änderung findet rein technisch am Stichtag "<b>geändert zum</b>" nachts um 23:55 Uhr statt. So lange kann die Änderung noch zurückgezogen werden.</p>';
        if ($this->change_done_at) {
            $heading = '<p>Die Änderung ist <b>technisch abgeschlossen</b> und kann nicht mehr zurückgezogen werden.</p>';
        }

        return [
            Heading::make($heading)
                ->asHtml()
                ->onlyOnDetail(),

            ID::make()->sortable(),

            BelongsTo::make('Nutzer', 'user', User::class),

            BelongsTo::make('Verband vorher', 'associationBefore', Association::class)->nullable(),

            BelongsTo::make('Verband nachher', 'associationAfter', Association::class)->nullable(),

            Text::make('Mitglied seit', function ($model) {
                return $model->user()->withTrashed()->first()?->currentAssociationMembershipHistory->started_at?->toDateString();
            })->readonly(),

            Text::make('geändert am', function () {
                return $this->created_at?->toDateString();
            })->readonly(),

            Text::make('geändert zum', function () {
                return $this->change_at?->toDateString();
            })->readonly(),

            Text::make('abgebrochen am', function () {
                // @phpstan-ignore-next-line
                return $this->canceled_at?->toDateString();
            })->readonly(),

            Text::make('abgeschlossen am', function () {
                return $this->change_done_at?->toDateString();
            })->readonly(),

        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        // TODO: Ausbauen, wenn die Ressourcen für History und Changes aus dem Menü verschwunden sind.
        if (! $request->findParentModel()) {
            return [];
        }

        return [
            (new ChangeAssociationMembership)->standalone(),
            (new CancelAssociationMembership)->standalone(),
            (new RevertLastAssociationMembershipChangeAction)->standalone(),
        ];
    }

    public static function authorizedToCreate(Request $request): false
    {
        return false;
    }

    public function authorizedToUpdate(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request)
    {
        return false;
    }
}
