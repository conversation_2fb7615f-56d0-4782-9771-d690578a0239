<?php

namespace App\Nova\Filters;

use <PERSON><PERSON>\Nova\Filters\BooleanFilter;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class CardLinkOrderStatus extends BooleanFilter
{
    /**
     * The displayable name of the filter.
     *
     * @var string
     */
    public $name = 'Status';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        $query->where(function ($query) use ($value) {
            foreach (collect(\App\Enums\CardLink\CardLinkOrderStatusEnum::labels())->keys() as $status) {
                $query->when($value[$status], function ($query) use ($status) { // @phpstan-ignore-line: Cannot access offset (int|string) on mixed.
                    $query->orWhere('status', $status);
                });
            }
        });

        return $query;
    }

    /**
     * Get the filter's available options.
     *
     * @return array<string, string>
     */
    public function options(NovaRequest $request): array
    {
        /** @var array<string, string> $options */
        $options = collect(\App\Enums\CardLink\CardLinkOrderStatusEnum::labels())
            ->mapWithKeys(fn ($value, $label) => [$value => $label])
            ->toArray();

        return $options;
    }
}
