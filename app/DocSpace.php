<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property string $name
 * @property string|null $suffix
 * @property string|null $description
 * @property string|null $additional_description
 * @property string $sdr_doc_space_id
 * @property int $soft_quota
 * @property int $hard_quota
 * @property int $current_usage
 * @property int $max_upload_file_size
 * @property string $retention_duration
 * @property array<array-key, mixed>|null $allowed_file_types
 * @property bool $encrypted
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\DocSpaceDocSpaceGroup|null $pivot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\DocSpaceGroup> $docSpaceGroups
 * @property-read int|null $doc_space_groups_count
 * @property-read mixed $current_usage_in_g_b
 * @property-read string $full_name
 * @property-read mixed $hard_quota_in_g_b
 * @property-read \App\Pharmacy $pharmacy
 *
 * @method static \Database\Factories\DocSpaceFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereAdditionalDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereAllowedFileTypes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereCurrentUsage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereEncrypted($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereHardQuota($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereMaxUploadFileSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereRetentionDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereSdrDocSpaceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereSoftQuota($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereSuffix($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereUpdatedAt($value)
 *
 * @property int $used_by_retax
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DocSpace whereUsedByRetax($value)
 *
 * @mixin \Eloquent
 */
class DocSpace extends Model
{
    use HasFactory;

    public const MAX_SPACES = 2;

    protected $guarded = [];

    protected $casts = [
        'allowed_file_types' => 'array',
        'encrypted' => 'boolean',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function docSpaceGroups(): BelongsToMany
    {
        return $this->belongsToMany(DocSpaceGroup::class)->using(DocSpaceDocSpaceGroup::class);
    }

    public function getFullNameAttribute(): string
    {
        if (! empty($this->suffix)) {
            return $this->name.' ['.$this->suffix.']';
        }

        return $this->name;
    }

    public function getCurrentUsageInGBAttribute()
    {
        return $this->current_usage / 1024;
    }

    public function getHardQuotaInGBAttribute()
    {
        return $this->hard_quota / 1024;
    }
}
