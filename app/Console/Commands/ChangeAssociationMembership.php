<?php

namespace App\Console\Commands;

use App\AssociationMembershipChange;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Processes\Payloads\ProcessAssociationMembershipChangePayload;
use App\Processes\ProcessAssociationMembershipChange;
use Illuminate\Console\Command;

class ChangeAssociationMembership extends Command
{
    protected $signature = 'user:change-association-membership';

    public function handle(): void
    {
        AssociationMembershipChange::query()
            ->whereIn('mode', [AssociationMembershipChangeModeEnum::ADD, AssociationMembershipChangeModeEnum::DELETE])

            ->where('change_at', '<=', now()->endOfDay()->toDateTimeString())
            ->whereNull(['change_done_at', 'canceled_at'])
            ->eachById(function (AssociationMembershipChange $associationMembershipChange) {
                $payload = new ProcessAssociationMembershipChangePayload($associationMembershipChange);

                $process = new ProcessAssociationMembershipChange;
                $process->run($payload);
            });
    }
}
