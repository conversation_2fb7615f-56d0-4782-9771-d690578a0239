<?php

namespace App\Console\Commands;

use App\Enums\PharmacyRoleEnum;
use App\Mail\ToEveryVerifiedUserNotificationEmail;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class SendEmailToEveryVerifiedUserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:sendToEveryVerifiedUser {--test=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $test = $this->option('test');

        if ($test) {
            $user = User::where('email', $test)->firstOrFail();
            Mail::to($user)->send(new ToEveryVerifiedUserNotificationEmail($user));
            $this->info('E-Mail an '.$user->email.' erfolgreich versandt!');

            return;
        }

        if (! $this->confirm('Bist du sicher, dass du eine E-Mail an alle registrierten Nutzer versenden möchtest?!')) {
            return;
        }

        $brocheureCodeQuery = User::whereHas('brochureCode');
        $withoutBrocheureCodeQuery = User::query()
            ->whereHas('pharmacies', function ($query) {
                return $query->where('role_name', PharmacyRoleEnum::OWNER);
            })
            ->whereDoesntHave('brochureCode');

        foreach ([$brocheureCodeQuery, $withoutBrocheureCodeQuery] as $query) {
            $executedUsers = 0;
            $userCount = $query->count();
            $query->chunkById(200, function ($users) use ($userCount, &$executedUsers) {
                $this->line('CHUNCK');
                foreach ($users as $user) {
                    $executedUsers++;
                    if (Cache::get('info_email_send_user_id'.$user->id) == 'send') {
                        $this->line('Benutzer '.$user->id.' Hat bereits eine E-Mail bekommen ('.$executedUsers.'/'.$userCount.')');

                        continue;
                    }

                    try {
                        Mail::to($user)->send(new ToEveryVerifiedUserNotificationEmail($user));
                    } catch (\Exception $e) {
                        app('sentry')->captureException($e);
                        $this->error('E-Mail an '.$user->email.' Konnte nicht versandt werden ('.$executedUsers.'/'.$userCount.')');

                        continue;
                    }
                    $this->info('E-Mail an '.$user->email.' erfolgreich versandt! ('.$executedUsers.'/'.$userCount.')');

                    Cache::put('info_email_send_user_id'.$user->id, '_send', 60 * 60 * 60);
                }
            });
        }
    }
}
