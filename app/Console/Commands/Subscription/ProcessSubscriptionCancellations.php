<?php

namespace App\Console\Commands\Subscription;

use App\Actions\Subscription\ExecuteSubscriptionCancellationAction;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\SubscriptionCancellation;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;

class ProcessSubscriptionCancellations extends Command
{
    protected $signature = 'subscription:process-cancellations {--dry-run : Nur anzeigen, was passieren würde}';

    protected $description = 'Verarbeitet fällige Kündigungen';

    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');

        $dueCancellations = SubscriptionCancellation::query()
            ->where('status', SubscriptionCancellationStatusEnum::CONFIRMED)
            ->where('effective_date', '<=', now())
            ->with(['pharmacy'])
            ->get();

        if ($dueCancellations->isEmpty()) {
            $this->info('Keine fälligen Kündigungen gefunden.');

            return CommandAlias::SUCCESS;
        }

        $this->info("Gefundene fällige Kündigungen: {$dueCancellations->count()}");

        foreach ($dueCancellations as $cancellation) {
            $this->line("- Apotheke: {$cancellation->pharmacy->name} (ID: {$cancellation->pharmacy->id})");
            $this->line("  Fällig seit: {$cancellation->effective_date->format('d.m.Y')}");

            if (! $isDryRun) {
                try {
                    app(ExecuteSubscriptionCancellationAction::class)->execute($cancellation);
                    $this->info('  ✓ Kündigung erfolgreich ausgeführt');
                } catch (\Exception $e) {
                    $this->error("  ✗ Fehler beim Ausführen der Kündigung: {$e->getMessage()}");
                }
            } else {
                $this->comment('  → Würde ausgeführt werden (Dry-Run)');
            }
        }

        if ($isDryRun) {
            $this->warn('Dry-Run Modus: Keine Änderungen wurden vorgenommen.');
        } else {
            $this->info('Alle fälligen Kündigungen wurden verarbeitet.');
        }

        return CommandAlias::SUCCESS;
    }
}
