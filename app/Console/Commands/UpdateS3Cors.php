<?php

namespace App\Console\Commands;

use Aws\Exception\AwsException;
use Illuminate\Console\Command;

class UpdateS3Cors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 's3:update-cors';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates the CORS policy for the S3 bucket used for file uploads.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $disks = ['local'];

        foreach ($disks as $disk) {
            $client = new \Aws\S3\S3Client([
                'endpoint' => config('filesystems.disks.'.$disk.'.endpoint'),
                'version' => 'latest',
                'use_path_style_endpoint' => true,
                'region' => config('filesystems.disks.'.$disk.'.region'),
                'credentials' => [
                    'key' => config('filesystems.disks.'.$disk.'.key'),
                    'secret' => config('filesystems.disks.'.$disk.'.secret'),
                ],
            ]);

            // --- CORS Policy Definition ---
            $corsPolicy = [
                'CORSRules' => [
                    [
                        'AllowedHeaders' => ['*'],
                        'AllowedMethods' => ['POST', 'GET', 'PUT', 'DELETE', 'HEAD'],
                        'AllowedOrigins' => [config('app.url')],
                        'ExposeHeaders' => [],
                        'MaxAgeSeconds' => 3000,
                    ],
                ],
            ];

            // --- Apply the policy ---
            try {
                $client->putBucketCors([
                    'Bucket' => config('filesystems.disks.'.$disk.'.bucket'),
                    'CORSConfiguration' => $corsPolicy,
                ]);

                $this->info('Successfully applied CORS policy to the bucket.');

            } catch (AwsException $e) {
                $this->error('Failed to update CORS policy:');
                $this->error($e->getMessage());

                return 1;
            }
        }

        return 0;
    }
}
