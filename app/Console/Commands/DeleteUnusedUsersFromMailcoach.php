<?php

namespace App\Console\Commands;

use App\Enums\Newsletter\MailcoachList;
use App\Jobs\DeleteMailcoachUserJob;
use App\User;
use Illuminate\Console\Command;

class DeleteUnusedUsersFromMailcoach extends Command
{
    /**
     * @var string
     */
    protected $signature = 'mailcoach:delete-unused';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $query = User::withTrashed()
            ->whereNull('email')
            ->subscribedToMailcoachList(MailcoachList::System);

        $bar = $this->output->createProgressBar($query->count());

        $bar->start();

        $query->eachById(function (User $user) use ($bar) {
            DeleteMailcoachUserJob::dispatch($user);
            $bar->advance();
        });

        $bar->finish();
    }
}
