<?php

namespace App\Console\Commands;

use App\Domains\Subscription\Application\StripeProducts\AddOns\KimProduct;
use App\Domains\Subscription\Application\StripeProducts\OneTimePurchases\KimInstallProduct;
use App\KimAddress;
use App\Pharmacy;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Laravel\Cashier\Cashier;

class MigrateKimSubscriptionToStripe extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscription:migrate-kim {--pharmacy=}';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $count = Pharmacy::query()
            ->whereHas('kimAddresses', fn ($query) => $query->whereNotNull('activated_at'))
            ->count();
        $counter = 1;

        Pharmacy::query()
            ->when($this->option('pharmacy'), function ($query) {
                $query->where('id', $this->option('pharmacy'));
            })
            ->whereHas('kimAddresses', fn ($query) => $query->whereNotNull('activated_at'))
            ->eachById(function (Pharmacy $pharmacy) use ($count, &$counter) {
                $endDate = Carbon::parse('2025-07-01 00:00:00', 'Europe/Berlin');
                $firstKimFreeUntil = Carbon::parse('2025-01-01 00:00:00', 'Europe/Berlin');
                $items = [];
                $currentlyRunningQuantity = 0;
                $firstKimAddressAlreadyFree = false;
                $installationPriceCount = 0;

                if ($pharmacy->subscribedToProduct(KimProduct::make()->getStripeProductId())) {
                    // Already subscribed to KIM, so we don't need to do anything
                    return;
                }

                $counter++;

                $pharmacy->kimAddresses()->whereNotNull('activated_at')->orderBy('activated_at')->eachById(function (KimAddress $kimAddress) use (&$items, $endDate, $pharmacy, &$currentlyRunningQuantity, &$firstKimAddressAlreadyFree, &$installationPriceCount, $firstKimFreeUntil) {
                    if (! $kimAddress->activated_at) {
                        // Adresse wurde nie aktiviert, daher auch nicht abgerechnet
                        return;
                    }

                    $newEndDate = $kimAddress->canceled_at && $kimAddress->canceled_at->lt($endDate) ? $kimAddress->canceled_at->clone() : $endDate;
                    $newStartDate = $kimAddress->activated_at->clone();

                    if (! $newEndDate->lt($endDate)) {
                        // Adresse ist heute noch aktiv, daher müssen wir sie in die subscription übernehmen.
                        $currentlyRunningQuantity++;
                    }

                    if ($firstKimAddressAlreadyFree) {
                        // Erste kostenlose Adresse wurde abgerechnet oder die Aktivierung ist im nächsten Jahr gewesen,
                        // daher müssen wir die Installationspauschale kassieren
                        $installationPriceCount++;
                    }

                    if ($newStartDate->lt($firstKimFreeUntil) && ! $firstKimAddressAlreadyFree) {
                        // Kim wurde im letzten Jahr gebucht und es wurde noch keine kostenlose abgerechnet, daher ist die 1. Kostenfrei
                        if ($kimAddress->canceled_at && $kimAddress->canceled_at->lte($firstKimFreeUntil)) {
                            // Adresse wurde vor 2025 deaktiviert, daher muss sie garnicht abgerechnet werden
                            $firstKimAddressAlreadyFree = true;

                            return;
                        }

                        $associationChange = $kimAddress->owner()
                            ->associationMembershipChanges()
                            ->whereBetween('change_done_at', [$kimAddress->activated_at, $firstKimFreeUntil])
                            ->whereNull('association_id_after')
                            ->orderBy('change_done_at')
                            ->first();

                        if ($associationChange) {
                            // User was association member on booking, but later decided to leave association -> We bill them from this point on
                            $newStartDate = $associationChange->change_done_at->clone()->addDay()->startOfDay();

                            if ($pharmacy->oldSubscriptions()->where('started_at', '>', $newStartDate)->exists()) {
                                // user booked a subscription after the association change, so we gift the 1st subscription again.
                                $newStartDate = $firstKimFreeUntil->clone();
                            }
                        } else {
                            // user has not left association, so we gift the 1st subscription
                            $newStartDate = $firstKimFreeUntil->clone();
                        }
                    }

                    $firstKimAddressAlreadyFree = true;

                    $items[] = [
                        'description' => 'KIM Adresse '.$kimAddress->email,
                        'price_data' => [
                            'currency' => 'eur',
                            'product' => app(KimProduct::class)->getStripeProductId(),
                            'unit_amount' => round(app(KimProduct::class)->getOneTimeStripePrice($pharmacy)->price * $newStartDate->floatDiffInMonths($newEndDate)),
                        ],
                        'period' => [
                            'start' => $newStartDate->timestamp,
                            'end' => $newEndDate->timestamp,
                        ],
                    ];
                });

                $newInvoice = null;

                if ($installationPriceCount > 0) {
                    $items[] = [
                        'quantity' => $installationPriceCount,
                        'pricing' => [
                            'price' => app(KimInstallProduct::class)->getOneTimeStripePrice($pharmacy)->stripePriceId,
                        ],
                    ];
                }

                if (count($items) === 0) {
                    // No items to bill, so we don't need to create an invoice
                    return;
                }

                try {
                    $billed = false;
                    $stripeCustomer = $pharmacy->createOrGetStripeCustomer();
                    if ($prevInv = Cashier::stripe()->invoices->all([
                        'customer' => $pharmacy->stripe_id,
                        'status' => 'draft',
                    ])) {
                        foreach ($prevInv as $item) {
                            if ($item->billing_reason == 'manual') {
                                $this->info('Invoice found: '.$pharmacy->stripe_id);

                                $billed = true;
                            }
                        }
                    }

                    if (! $billed) {
                        $defaultPaymentMethod = $stripeCustomer->invoice_settings?->default_payment_method;
                        if (! $defaultPaymentMethod) {
                            $newInvoice = Cashier::stripe()->invoices->create([
                                'customer' => $pharmacy->stripe_id,
                                'automatically_finalizes_at' => now()->addHours(2)->timestamp,
                                'auto_advance' => true,
                                'default_tax_rates' => $pharmacy->taxRates(),
                                'collection_method' => 'send_invoice',
                                'days_until_due' => 14,
                                'payment_settings' => [
                                    'payment_method_types' => ['customer_balance', 'sepa_debit'],
                                ],
                            ]);
                        } else {
                            $newInvoice = Cashier::stripe()->invoices->create([
                                'customer' => $pharmacy->stripe_id,
                                'auto_advance' => true,
                                'automatically_finalizes_at' => now()->addHours(2)->timestamp,
                                'default_tax_rates' => $pharmacy->taxRates(),
                                'collection_method' => 'charge_automatically',
                                'default_payment_method' => $defaultPaymentMethod,
                            ]);
                        }

                        Cashier::stripe()->invoices->addLines($newInvoice->id, [
                            'lines' => $items,
                        ]);
                    }

                } catch (\Exception $e) {
                    if (isset($newInvoice)) {
                        Cashier::stripe()->invoices->delete($newInvoice->id);
                    }

                    $this->error('Invoice error =>'.$e->getMessage().' '.$pharmacy->stripe_id);

                    return;
                }

                if ($currentlyRunningQuantity === 0) {
                    // No active addresses, so we don't need to create a subscription
                    $this->info('pharmacy '.$pharmacy->id.' migrated to stripe '.$counter.'/'.$count);

                    return;
                }

                try {
                    if ($pharmacy->subscribed()) {
                        $pharmacy->subscription()->noProrate()->addPrice(app(KimProduct::class)->getRecurringStripePrice($pharmacy)->stripePriceId, $currentlyRunningQuantity);
                    } else {
                        $subscription = $pharmacy->newSubscription('default', [app(KimProduct::class)->getRecurringStripePrice($pharmacy)->stripePriceId])
                            ->quantity($currentlyRunningQuantity)
                            ->noProrate()
                            ->create(null, [], array_merge(
                                [
                                    'collection_method' => 'send_invoice',
                                    'days_until_due' => 14,
                                    'billing_cycle_anchor' => now('Europe/Berlin')->addQuarterNoOverflow()->startOfQuarter()->timestamp,
                                    'payment_settings' => [
                                        'payment_method_types' => ['customer_balance', 'sepa_debit'],
                                    ],
                                ],
                            ));
                    }
                } catch (\Exception $e) {
                    $this->error('Subscription error =>'.$e->getMessage().' '.$pharmacy->stripe_id);
                }

                $this->info('pharmacy '.$pharmacy->id.' migrated to stripe '.$counter.'/'.$count);
            });
    }
}
