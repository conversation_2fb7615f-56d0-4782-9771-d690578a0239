<?php

namespace App\Console\Commands;

use App\PharmaceuticalServicePatient;
use App\VaccinationPatient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use ReflectionClass;

class ReencryptPatientData extends Command
{
    /**
     * Cache key constants for tracking last processed IDs
     */
    const VACCINATION_CACHE_KEY = 'reencrypt_last_vaccination_id';

    const PHARMACEUTICAL_CACHE_KEY = 'reencrypt_last_pharmaceutical_id';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'patients:reencrypt
                            {type : Type of patients to reencrypt (vaccination, pharmaceutical)}
                            {limit : Maximum number of records to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reencrypt patient data for VaccinationPatient or PharmaceuticalServicePatient';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $chunkSize = 1000;
        $type = $this->argument('type');
        $limit = (int) $this->argument('limit');

        if ($limit <= 0) {
            $this->error('Limit must be a positive number');

            return 1;
        }

        if ($type === 'vaccination') {
            $this->info('Starting reencryption of VaccinationPatient data...');
            $this->reencryptVaccinationPatients($chunkSize, $limit);
        } elseif ($type === 'pharmaceutical') {
            $this->info('Starting reencryption of PharmaceuticalServicePatient data...');
            $this->reencryptPharmaceuticalServicePatients($chunkSize, $limit);
        } else {
            $this->error('Invalid type specified. Please choose either "vaccination" or "pharmaceutical".');

            return 1;
        }

        $this->info('Reencryption completed successfully!');

        return 0;
    }

    /**
     * Reencrypt all VaccinationPatient records.
     */
    private function reencryptVaccinationPatients(int $chunkSize, int $limit): void
    {
        // Get last processed ID from cache
        $lastProcessedId = Cache::get(self::VACCINATION_CACHE_KEY, 0);

        $query = VaccinationPatient::query();

        // Start from the last processed ID
        if ($lastProcessedId > 0) {
            $this->info("Continuing from vaccination ID: $lastProcessedId");
            $query->where('vaccination_id', '>', $lastProcessedId);
        }

        // Count records that will be processed
        $totalToProcess = min($limit, $query->count());
        $bar = $this->output->createProgressBar($totalToProcess);
        $bar->start();

        $latestId = $lastProcessedId;
        $processedCount = 0;

        $query->orderBy('vaccination_id')
            ->chunkById($chunkSize, function ($patients) use ($bar, &$latestId, &$processedCount, $limit) {
                DB::beginTransaction();
                try {
                    foreach ($patients as $patient) {
                        $this->reencryptVaccinationPatient($patient);
                        $latestId = $patient->vaccination_id;
                        $bar->advance();

                        $processedCount++;
                        if ($processedCount >= $limit) {
                            break;
                        }
                    }

                    // Store the latest processed ID in cache
                    Cache::put(self::VACCINATION_CACHE_KEY, $latestId);

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Error reencrypting vaccination patient data: '.$e->getMessage());
                    $this->error('Error reencrypting vaccination patient data: '.$e->getMessage());
                    throw $e;
                }

                return $processedCount < $limit; // Continue chunking if we haven't reached the limit
            }, 'vaccination_id');

        $bar->finish();
        $this->newLine();
        $this->info("Processed $processedCount vaccination patient records. Last ID: $latestId");
    }

    /**
     * Reencrypt all PharmaceuticalServicePatient records.
     */
    private function reencryptPharmaceuticalServicePatients(int $chunkSize, int $limit): void
    {
        // Get last processed ID from cache
        $lastProcessedId = Cache::get(self::PHARMACEUTICAL_CACHE_KEY, 0);

        $query = PharmaceuticalServicePatient::query();

        // Start from the last processed ID
        if ($lastProcessedId > 0) {
            $this->info("Continuing from pharmaceutical service ID: $lastProcessedId");
            $query->where('ps_id', '>', $lastProcessedId);
        }

        // Count records that will be processed
        $totalToProcess = min($limit, $query->count());
        $bar = $this->output->createProgressBar($totalToProcess);
        $bar->start();

        $latestId = $lastProcessedId;
        $processedCount = 0;

        $query->orderBy('ps_id')
            ->chunkById($chunkSize, function ($patients) use ($bar, &$latestId, &$processedCount, $limit) {
                DB::beginTransaction();
                try {
                    foreach ($patients as $patient) {
                        $this->reencryptPharmaceuticalServicePatient($patient);
                        $latestId = $patient->ps_id;
                        $bar->advance();

                        $processedCount++;
                        if ($processedCount >= $limit) {
                            break;
                        }
                    }

                    // Store the latest processed ID in cache
                    Cache::put(self::PHARMACEUTICAL_CACHE_KEY, $latestId);

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error('Error reencrypting pharmaceutical service patient data: '.$e->getMessage());
                    $this->error('Error reencrypting pharmaceutical service patient data: '.$e->getMessage());
                    throw $e;
                }

                return $processedCount < $limit; // Continue chunking if we haven't reached the limit
            }, 'ps_id');

        $bar->finish();
        $this->newLine();
        $this->info("Processed $processedCount pharmaceutical service patient records. Last ID: $latestId");
    }

    /**
     * Reencrypt a single VaccinationPatient record.
     */
    private function reencryptVaccinationPatient(VaccinationPatient $patient): void
    {
        $attributes = self::getProtectedProperty($patient, 'encrypted');

        // Temporarily disable timestamps to avoid updating them
        $patient->timestamps = false;

        foreach ($attributes as $attribute) {
            $value = $patient->$attribute;
            if ($value !== null) {
                // Using the accessor and mutator methods to decrypt and re-encrypt
                $patient->$attribute = $value;
            }
        }

        $patient->save();

        // Re-enable timestamps
        $patient->timestamps = true;
    }

    /**
     * Reencrypt a single PharmaceuticalServicePatient record.
     */
    private function reencryptPharmaceuticalServicePatient(PharmaceuticalServicePatient $patient): void
    {
        $attributes = self::getProtectedProperty($patient, 'encrypted');

        // Temporarily disable timestamps to avoid updating them
        $patient->timestamps = false;

        foreach ($attributes as $attribute) {
            $value = $patient->$attribute;
            if ($value !== null) {
                // Using the accessor and mutator methods to decrypt and re-encrypt
                $patient->$attribute = $value;
            }
        }

        $patient->save();

        // Re-enable timestamps
        $patient->timestamps = true;
    }

    private static function getProtectedProperty(object $object, string $property): mixed
    {
        $reflection = new ReflectionClass($object);
        $property = $reflection->getProperty($property);
        $property->setAccessible(true);

        return $property->getValue($object);
    }
}
