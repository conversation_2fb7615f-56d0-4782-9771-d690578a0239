<?php

namespace App\Console\Commands;

use App\Actions\Newsletter\SubscribeToMailcoachList;
use App\Enums\Newsletter\MailcoachList;
use App\User;
use Illuminate\Console\Command;

class SubscribeSystemMailingList extends Command
{
    protected $signature = 'app:subscribe-system-mailing-list';

    protected $description = 'Command description';

    public function handle(): void
    {
        User::query()
            ->whereNotNull('email')
            ->notSubscribedToMailcoachList(MailcoachList::System)
            ->eachById(fn (User $user) => dispatch(
                fn () => SubscribeToMailcoachList::execute($user, MailcoachList::System)
            ));
    }
}
