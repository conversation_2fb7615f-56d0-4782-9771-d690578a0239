<?php

namespace App\Console\Commands;

use App\Enums\Newsletter\MailcoachList;
use App\Jobs\SubscribeToMailcoachListJob;
use App\User;
use Illuminate\Console\Command;

class ImportUserToMailcoach extends Command
{
    /**
     * @var string
     */
    protected $signature = 'mailcoach:import';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $query = User::query()
            ->whereNotNull('email')
            ->notSubscribedToMailcoachList(MailcoachList::System);

        $bar = $this->output->createProgressBar($query->count());

        $bar->start();

        $query->eachById(function (User $user) use ($bar) {
            SubscribeToMailcoachListJob::dispatch($user, MailcoachList::System);
            $bar->advance();
        });

        $bar->finish();
    }
}
