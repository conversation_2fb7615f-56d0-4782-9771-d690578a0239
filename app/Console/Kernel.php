<?php

namespace App\Console;

use App\Console\Commands\CardLink\ReindexPharmacies;
use App\Console\Commands\CheckAndInvalidateWebchatCache;
use App\Console\Commands\RefreshNgdaTokens;
use App\Console\Commands\ReportKimAddressesToNNF;
use App\Console\Commands\SendKimAddressNotification;
use App\Console\Commands\SyncKimAddresses;
use App\Enums\FeatureScope;
use App\Features\CardLink;
use App\Features\IntegrateNgda;
use App\Jobs\DeleteUnfinishedVaccinations;
use App\PharmaceuticalService;
use App\Settings\KimSettings;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Laravel\Pennant\Feature;

class Kernel extends ConsoleKernel
{
    protected function schedule(Schedule $schedule): void
    {
        $this->hourly($schedule);
        $this->daily($schedule);

        $schedule->command('horizon:snapshot')
            ->onOneServer()
            ->everyFiveMinutes();

        // TODO: Remove when working on AP-1057
        $schedule->command('sends:order-to-ibm')
            ->onOneServer()
            ->days([Schedule::MONDAY, Schedule::THURSDAY])
            ->at('09:00')
            ->when(isCovidVaccinationCertificateCenterActive());

        $schedule->command(SyncKimAddresses::class)
            ->when(app(KimSettings::class)->sync_kim_job_active)
            ->onOneServer()
            ->sundays()
            ->at('23:50');

        $schedule->command(CheckAndInvalidateWebchatCache::class)
            ->onOneServer()
            ->everyTenMinutes();

        if (Feature::for(FeatureScope::App->value)->active(CardLink::class)) {
            $schedule->command(ReindexPharmacies::class)
                ->onOneServer()
                ->monthlyOn(1, '00:15');
        }

        $schedule->command('model:prune', [
            '--model' => PharmaceuticalService::class,
        ])->dailyAt('03:00')
            ->onOneServer();
    }

    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }

    protected function hourly(Schedule $schedule): void
    {
        $schedule->command('rssfeed:import')
            ->onOneServer()
            ->hourly();

        $schedule->command('passport:purge')
            ->onOneServer()
            ->hourly();

        $schedule->command('cache:prune-stale-tags')
            ->onOneServer()
            ->hourly();
    }

    protected function daily(Schedule $schedule): void
    {
        $schedule->command('sends:care-centers-to-info-gedisa')
            ->onOneServer()
            ->dailyAt('01:10');

        $schedule->job(new DeleteUnfinishedVaccinations)
            ->onOneServer()
            ->dailyAt('03:00');

        /**
         * TODO: Redo this process
         * AP-2406 disable change association membership
        $schedule->command('user:change-association-membership')
            ->onOneServer()
            ->dailyAt('23:45');
         */
        if (Feature::for(FeatureScope::App->value)->active(IntegrateNgda::class)) {
            $schedule->command(RefreshNgdaTokens::class)
                ->onOneServer()
                ->dailyAt('00:30');

            $schedule->command(ReportKimAddressesToNNF::class)
                ->onOneServer()
                ->dailyAt('01:00');

            $schedule->command(SendKimAddressNotification::class)
                ->onOneServer()
                ->dailyAt('01:05');
        }
    }
}
