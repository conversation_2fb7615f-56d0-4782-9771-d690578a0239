<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdvertisingMaterial extends Model
{
    protected $casts = [
        'ordered_at' => 'datetime',
        'dispatched_at' => 'datetime',
    ];

    protected $fillable = [
        'pharmacy_id',
        'ordered_at',
        'dispatched_at',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
