<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @mixin IdeHelperFocusAreaPharmacy
 *
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $pharmacy_id
 * @property int $focus_area_id
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusAreaPharmacy newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusAreaPharmacy newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusAreaPharmacy query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusAreaPharmacy whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusAreaPharmacy whereFocusAreaId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusAreaPharmacy wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FocusAreaPharmacy whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class FocusAreaPharmacy extends Pivot
{
    public static function booted()
    {
        static::created(function ($item) {
            Pharmacy::query()->where('id', $item->pharmacy_id)->update([
                'updated_at' => now(),
            ]);
        });

        static::deleted(function ($item) {
            Pharmacy::query()->where('id', $item->pharmacy_id)->update([
                'updated_at' => now(),
            ]);
        });
    }
}
