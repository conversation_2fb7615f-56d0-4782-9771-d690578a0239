<?php

namespace App;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ClosedNotification extends Model
{
    use HasFactory, SoftDeletes;

    protected $casts = [
        'data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function closeable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeAllForUserAndHisPharmacies(Builder $builder, User $user): Builder
    {
        $pharmacyIds = $user->pharmacies->pluck('id')->toArray();

        return $builder->where(function ($query) use ($user, $pharmacyIds) {
            $query->where(function (Builder $q) use ($user) {
                $q->where('closeable_type', $user->getMorphClass())
                    ->where('closeable_id', $user->id);
            });

            if (! empty($pharmacyIds)) {
                $query->orWhere(function ($q) use ($pharmacyIds) {
                    $q->where('closeable_type', (new Pharmacy)->getMorphClass())
                        ->whereIn('closeable_id', $pharmacyIds);
                });
            }
        });
    }
}
