<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $ia_sync_id
 * @property string $name
 * @property string $url
 * @property string $method
 * @property string|null $request_body
 * @property int|null $response_status_code
 * @property string|null $response_body
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\IaSync|null $sync
 *
 * @method static \Database\Factories\IaRequestFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereIaSyncId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereRequestBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereResponseBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereResponseStatusCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IaRequest whereUrl($value)
 *
 * @mixin \Eloquent
 */
class IaRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'url',
        'method',
        'request_body',
        'response_status_code',
        'response_body',
    ];

    public function sync(): BelongsTo
    {
        return $this->belongsTo(IaSync::class);
    }
}
