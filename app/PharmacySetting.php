<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PharmacySetting
 *
 * @mixin IdeHelperPharmacySetting
 */
class PharmacySetting extends Model
{
    use HasFactory;

    protected $primaryKey = 'pharmacy_id';

    public $incrementing = false;

    protected $fillable = [
        'pharmacy_id',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
