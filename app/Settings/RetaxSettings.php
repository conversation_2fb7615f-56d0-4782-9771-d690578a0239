<?php

namespace App\Settings;

use App\Enums\SystemSettings\SystemSettingGroupEnum;
use App\User;
use RuntimeException;
use <PERSON><PERSON>\LaravelSettings\Settings;

class RetaxSettings extends Settings
{
    public string $base_url;

    /** @var array <string> */
    public array $use_latest_paths = [];

    public string $retax_service_user_group_id = '';

    public static function group(): string
    {
        return SystemSettingGroupEnum::RETAX->value;
    }

    public function issuer(): string
    {
        $config = config('app.url');
        if (! is_string($config)) {
            throw new RuntimeException('Invalid configuration for retax issuer');
        }

        return $config;
    }

    public function audience(): string
    {
        $config = config('services.retax.audience');
        if (! is_string($config)) {
            throw new RuntimeException('Invalid configuration for retax audience');
        }

        return $config;
    }

    public function isAllowedToUse(User $user): bool
    {
        if (in_array('all', app(RetaxSettings::class)->use_latest_paths)) {
            return true;
        }

        if (in_array($user->id, app(RetaxSettings::class)->use_latest_paths)) {
            return true;
        }

        if (in_array('fut', app(RetaxSettings::class)->use_latest_paths) && $user->is_beta_tester) {
            return true;
        }

        return false;
    }
}
