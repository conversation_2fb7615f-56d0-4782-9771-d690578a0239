<?php

namespace App\Settings;

use App\Enums\SystemSettings\SystemSettingGroupEnum;
use <PERSON><PERSON>\LaravelSettings\Settings;

class MailcoachSettings extends Settings
{
    public string $closeFrom;

    public string $systemMailingListId;

    public string $advertisingMailingListId;

    public static function enabled(): bool
    {
        $enabled = config('services.mailcoach.enabled');

        assert(is_bool($enabled));

        return $enabled;
    }

    public static function token(): string
    {
        $token = config('services.mailcoach.token');

        assert(is_string($token));

        return $token;
    }

    public static function url(): string
    {
        $url = config('services.mailcoach.url');

        assert(is_string($url));

        return $url;
    }

    public static function webhookSecret(): string
    {
        $secret = config('services.mailcoach.webhook_secret');

        assert(is_string($secret));

        return $secret;
    }

    public static function group(): string
    {
        return SystemSettingGroupEnum::MAILCOACH->value;
    }
}
