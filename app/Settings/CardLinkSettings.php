<?php

namespace App\Settings;

use App\Enums\CardLink\CardLinkDiscountEnum;
use App\Enums\SystemSettings\SystemSettingGroupEnum;
use App\Features\CardLink;
use App\Helper\CardLinkOrderHelper;
use App\Pharmacy;
use Carbon\Carbon;
use <PERSON>tie\LaravelSettings\Settings;

class CardLinkSettings extends Settings implements HasPurgeablesInterface
{
    public ?Carbon $reservationEnabledAt;

    public ?Carbon $transmissionEnabledAt;

    public ?Carbon $preActivationDiscountUntil;

    public static function group(): string
    {
        return SystemSettingGroupEnum::CARD_LINK->value;
    }

    public function purgeables(): array
    {
        return [CardLink::class];
    }

    public static function url(?string $endpoint = null): string
    {
        $baseUrl = config('services.card-link-service.url');
        assert(is_string($baseUrl));

        if ($endpoint) {
            return $baseUrl.$endpoint;
        }

        return $baseUrl;
    }

    public static function clientId(): string
    {
        assert(is_string($clientId = config('services.card-link-service.client-id')));

        return $clientId;
    }

    public static function clientSecret(): string
    {
        assert(is_string($clientSecret = config('services.card-link-service.client-secret')));

        return $clientSecret;
    }

    public static function clientIdVendor(): string
    {
        assert(is_string($clientId = config('services.card-link-service.client-id-vendor')));

        return $clientId;
    }

    public static function clientSecretVendor(): string
    {
        assert(is_string($clientSecret = config('services.card-link-service.client-secret-vendor')));

        return $clientSecret;
    }

    public static function tokenEndpoint(): string
    {
        return '/oauth/token';
    }

    public static function apoGuideChannelId(): string
    {
        assert(is_string($id = config('services.card-link-service.apoguide.channel-id')));

        return $id;
    }

    public static function getDiscount(Pharmacy $pharmacy): ?CardLinkDiscountEnum
    {
        if (CardLinkOrderHelper::isPartner($pharmacy)) {
            return CardLinkDiscountEnum::Partner;
        }

        if (now()->isBefore(app(self::class)->preActivationDiscountUntil)) {
            return CardLinkDiscountEnum::ProductStartDiscount;
        }

        return null;
    }
}
