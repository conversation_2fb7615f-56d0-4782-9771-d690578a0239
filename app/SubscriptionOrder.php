<?php

namespace App;

use App\Enums\AssociationEnum;
use App\Enums\InvoiceStatusEnum;
use App\Enums\SubscriptionOrderPaymentScopeOperator;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @deprecated
 *
 * @property int $id
 * @property string $plan
 * @property \Illuminate\Support\Carbon $started_at
 * @property \Illuminate\Support\Carbon $ended_at
 * @property float $total_price
 * @property int $subscription_id
 * @property string $orderable_type
 * @property int $orderable_id
 * @property string|null $processed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $is_upgrade
 * @property int|null $invoice_id
 * @property-read string $period
 * @property-read int $price_per_month
 * @property-read int $price_per_period
 * @property-read \App\Invoice|null $invoice
 * @property-read Model|\Eloquent $orderable
 * @property-read \App\Subscription|null $subscription
 *
 * @method static \Database\Factories\SubscriptionOrderFactory factory($count = null, $state = [])
 * @method static Builder<static>|SubscriptionOrder newModelQuery()
 * @method static Builder<static>|SubscriptionOrder newQuery()
 * @method static Builder<static>|SubscriptionOrder paymentCancelled(\App\Enums\SubscriptionOrderPaymentScopeOperator $operator = \App\Enums\SubscriptionOrderPaymentScopeOperator::Equal)
 * @method static Builder<static>|SubscriptionOrder pendingPayment()
 * @method static Builder<static>|SubscriptionOrder query()
 * @method static Builder<static>|SubscriptionOrder whereCreatedAt($value)
 * @method static Builder<static>|SubscriptionOrder whereEndedAt($value)
 * @method static Builder<static>|SubscriptionOrder whereId($value)
 * @method static Builder<static>|SubscriptionOrder whereInvoiceId($value)
 * @method static Builder<static>|SubscriptionOrder whereIsUpgrade($value)
 * @method static Builder<static>|SubscriptionOrder whereOrderableId($value)
 * @method static Builder<static>|SubscriptionOrder whereOrderableType($value)
 * @method static Builder<static>|SubscriptionOrder wherePlan($value)
 * @method static Builder<static>|SubscriptionOrder whereProcessedAt($value)
 * @method static Builder<static>|SubscriptionOrder whereStartedAt($value)
 * @method static Builder<static>|SubscriptionOrder whereSubscriptionId($value)
 * @method static Builder<static>|SubscriptionOrder whereTotalPrice($value)
 * @method static Builder<static>|SubscriptionOrder whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class SubscriptionOrder extends Model
{
    use HasFactory;

    protected $casts = [
        'started_at' => 'date',
        'ended_at' => 'date',
    ];

    protected $guarded = [];

    /**
     * @deprecated
     */
    public array $possiblePlans = ['wl_base', 'wl_extended', 'base', 'extended', 'extern_base'];

    protected static function booted(): void
    {
        static::addGlobalScope('sorting', function (Builder $builder) {
            $builder->orderBy('started_at', 'asc');
        });
    }

    /**
     * @deprecated
     */
    public function orderable()
    {
        return $this->morphTo();
    }

    /**
     * @deprecated
     */
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * @deprecated
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * @deprecated
     */
    public function getPeriodAttribute(): string
    {
        return config('subscription.plans.'.$this->plan.'.period');
    }

    /**
     * @deprecated
     */
    public function getPricePerMonthAttribute(): int
    {
        if ($this->orderable()->first()?->owner()?->pharmacyProfile->association_id == AssociationEnum::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V) {
            if ($this->ended_at >= Carbon::create(2023, 7, 1) && $this->ended_at <= Carbon::create(2023, 9)->endOfMonth()) {
                return 20;
            }
        }

        return config('subscription.plans.'.$this->plan.'.price_per_month');
    }

    /**
     * @deprecated
     */
    public function getPricePerPeriodAttribute(): int
    {
        return config('subscription.plans.'.$this->plan.'.price');
    }

    /**
     * @deprecated
     */
    public function calcPrice($isStart = false)
    {
        if (in_array($this->plan, $this->possiblePlans)) {
            if (
                $this->orderable &&
                $this->started_at->year == 2022 &&
                $this->orderable->created_at->gte(config('subscription.backtrack_payments_until'))
            ) {
                $newPrice = $this->price_per_month * (int) abs($this->orderable->created_at->diffInMonths($this->ended_at->addMonthNoOverflow()->startOfMonth()));
            } else {
                $date = clone $this->started_at;
                $newPrice = $this->price_per_month * ($isStart && $date->startOfMonth() == $this->started_at ? max($this->getMonthsInCurrentCycle() - 1, 1) : $this->getMonthsInCurrentCycle());
            }

            if (! $this->is_upgrade) {
                return $newPrice;
            }

            if (
                $this->started_at->year == 2022 &&
                $this->orderable->created_at->gte(config('subscription.backtrack_payments_until'))
            ) {
                $pricePerMonth = config('subscription.plans.'.config('subscription.plans.'.$this->plan.'.replaces').'.price_per_month');
                assert(is_int($pricePerMonth));
                $oldPrice = $pricePerMonth * (int) abs($this->orderable->created_at->endOfMonth()->diffInMonths($this->ended_at));
            } else {
                $date = clone $this->started_at;
                $oldPrice = $this->replicate()->fill(['plan' => config('subscription.plans.'.$this->plan.'.replaces')])->price_per_month * ($isStart && $date->startOfMonth() == $this->started_at ? max($this->getMonthsInCurrentCycle() - 1, 1) : $this->getMonthsInCurrentCycle());
            }

            return $newPrice - $oldPrice;
        }
    }

    /**
     * @deprecated
     */
    public function getMonthsInCurrentCycle(): int
    {
        $start = $this->ended_at
            ->addMonthNoOverflow()
            ->startOfMonth();

        $end = $this->started_at;

        return $start->lt($end) ? 0 :
            min((int) abs($end->diffInMonths($start)), 12);
    }

    /**
     * @deprecated
     */
    public function scopePendingPayment(Builder $query): Builder
    {
        return $query->whereNull('invoice_id')
            ->orWhereHas('invoice', function (Builder $invoiceQuery) {
                return $invoiceQuery->whereNotIn('status_code', InvoiceStatusEnum::getFinalizedPaymentStatuses());
            });
    }

    /**
     * @deprecated
     */
    public function scopePaymentCancelled(Builder $query, SubscriptionOrderPaymentScopeOperator $operator = SubscriptionOrderPaymentScopeOperator::Equal): Builder
    {
        return $query->whereRelation('invoice', 'status_code', $operator->value, InvoiceStatusEnum::CANCELED);
    }
}
