<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class DocSpaceDocSpaceGroup extends Pivot
{
    protected $table = 'doc_space_doc_space_group';

    public $timestamps = null;

    public function docSpace(): BelongsTo
    {
        return $this->belongsTo(DocSpace::class);
    }

    public function docSpaceGroup(): BelongsTo
    {
        return $this->belongsTo(DocSpaceGroup::class);
    }
}
