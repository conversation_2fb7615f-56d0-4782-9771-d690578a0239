<?php

namespace App;

use App\Domains\ShiftPlan\Domain\Observers\ShiftPlanGroupUserObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

#[ObservedBy([ShiftPlanGroupUserObserver::class])]
/**
 * @property int $id
 * @property string $uuid
 * @property int $sort_order
 * @property int|null $shift_plan_group_id
 * @property int|null $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\ShiftPlanGroup|null $group
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Shift> $shifts
 * @property-read int|null $shifts_count
 * @property-read \App\User|null $user
 *
 * @method static \Database\Factories\ShiftPlanGroupUserFactory factory($count = null, $state = [])
 * @method static Builder<static>|ShiftPlanGroupUser newModelQuery()
 * @method static Builder<static>|ShiftPlanGroupUser newQuery()
 * @method static Builder<static>|ShiftPlanGroupUser ordered(string $direction = 'asc')
 * @method static Builder<static>|ShiftPlanGroupUser query()
 * @method static Builder<static>|ShiftPlanGroupUser whereCreatedAt($value)
 * @method static Builder<static>|ShiftPlanGroupUser whereId($value)
 * @method static Builder<static>|ShiftPlanGroupUser whereShiftPlanGroupId($value)
 * @method static Builder<static>|ShiftPlanGroupUser whereSortOrder($value)
 * @method static Builder<static>|ShiftPlanGroupUser whereUpdatedAt($value)
 * @method static Builder<static>|ShiftPlanGroupUser whereUserId($value)
 * @method static Builder<static>|ShiftPlanGroupUser whereUuid($value)
 *
 * @mixin \Eloquent
 */
class ShiftPlanGroupUser extends Pivot implements Sortable
{
    use HasFactory, SortableTrait;

    public $incrementing = true;

    protected $guarded = [];

    public function buildSortQuery(): Builder
    {
        return static::query()->where('shift_plan_group_id', $this->shift_plan_group_id);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(ShiftPlanGroup::class, 'shift_plan_group_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function shifts(): HasMany
    {
        return $this->hasMany(Shift::class, 'shift_plan_group_user_id');
    }
}
