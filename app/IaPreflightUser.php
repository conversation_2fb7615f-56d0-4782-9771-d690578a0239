<?php

namespace App;

use App\Features\IhreApothekenPreflight;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Laravel\Pennant\Feature;

/**
 * @property int $id
 * @property int $user_id
 * @property int $enabled
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\User $user
 *
 * @method static Builder<static>|IaPreflightUser enabled()
 * @method static Builder<static>|IaPreflightUser newModelQuery()
 * @method static Builder<static>|IaPreflightUser newQuery()
 * @method static Builder<static>|IaPreflightUser query()
 * @method static Builder<static>|IaPreflightUser whereCreatedAt($value)
 * @method static Builder<static>|IaPreflightUser whereEnabled($value)
 * @method static Builder<static>|IaPreflightUser whereId($value)
 * @method static Builder<static>|IaPreflightUser whereUpdatedAt($value)
 * @method static Builder<static>|IaPreflightUser whereUserId($value)
 *
 * @mixin \Eloquent
 */
class IaPreflightUser extends Model
{
    protected $fillable = [
        'user_id',
        'enabled',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeEnabled(Builder $query): Builder
    {
        return $query->where('enabled', true);
    }

    private static function purgeIaPreflightPennantFeature(): void
    {
        Feature::purge(IhreApothekenPreflight::class);
    }

    private static function purgeCache(int $userId): void
    {
        Cache::forget(sprintf('ia.preflight.user.%s', $userId));
    }

    protected static function booted(): void
    {
        static::created(function (IaPreflightUser $user) {
            self::purgeIaPreflightPennantFeature();
            self::purgeCache($user->user_id);
        });

        static::updated(function (IaPreflightUser $user) {
            self::purgeIaPreflightPennantFeature();
            self::purgeCache($user->user_id);
        });

        static::deleted(function (IaPreflightUser $user) {
            self::purgeIaPreflightPennantFeature();
            self::purgeCache($user->user_id);
        });
    }
}
