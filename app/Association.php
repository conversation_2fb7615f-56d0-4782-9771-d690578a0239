<?php

namespace App;

use App\Domains\Association\Models\AssociationFrameworkContractHistory;
use App\Enums\AssociationRoleEnum;
use App\Enums\PharmacyVaccinateStatusEnum;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class Association
 *
 * @mixin IdeHelperAssociation
 */
class Association extends Model
{
    protected $guarded = [];

    use HasFactory;

    public static function boot(): void
    {
        parent::boot();

        self::created(function ($model) {
            AssociationSetting::firstOrCreate([
                'association_id' => $model->id,
            ]);
        });
    }

    public function news(): HasMany
    {
        return $this->hasMany(AssociationNews::class);
    }

    public function assignedUsers(): BelongsToMany
    {
        return $this
            ->belongsToMany(User::class, 'association_role_user')
            ->using(AssociationRoleUser::class)
            ->withPivot(['role_name', 'permissions'])
            ->withTimestamps();
    }

    public function assignedAdmins(): BelongsToMany
    {
        return $this->assignedUsers()->where('role_name', AssociationRoleEnum::ADMIN);
    }

    public function associationFrameworkContractHistories(): HasMany
    {
        return $this->hasMany(AssociationFrameworkContractHistory::class);
    }

    public function currentAssociationFrameworkContractHistory(): HasOne
    {
        return $this->hasOne(AssociationFrameworkContractHistory::class)->latestOfMany('ends_at')->where('ends_at', '>=', now());
    }

    public function members(): HasManyThrough
    {
        return $this->hasManyThrough(
            User::class,
            UserPharmacyProfile::class,
            'association_id',
            'id',
            'id',
            'user_id'
        );
    }

    public function settings(): HasOne
    {
        return $this->hasOne(AssociationSetting::class);
    }

    public function userPharmacyProfiles(): HasMany
    {
        return $this->hasMany(UserPharmacyProfile::class);
    }

    public function userAssociationProfiles(): HasMany
    {
        return $this->hasMany(UserAssociationProfile::class);
    }

    public function healthInsuranceCompanies(): BelongsToMany
    {
        return $this->belongsToMany(HealthInsuranceCompany::class)->withTimestamps();
    }

    public function vaccinations(): HasMany
    {
        return $this->hasMany(Vaccination::class);
    }

    public function brochureCodes(): HasMany
    {
        return $this->hasMany(BrochureCode::class);
    }

    public function pharmacies(): HasMany
    {
        return $this->hasMany(Pharmacy::class);
    }

    /**
     * @param  array  $permissions
     */
    public function assignUser($user, string $role, $permissions = []): void
    {
        $this->assignedUsers()->attach($user, ['role_name' => $role, 'permissions' => $permissions]);
    }

    /**
     * @param  mixed  $user
     */
    public function unassignUser($user): void
    {
        $this->assignedUsers()->detach($user);
    }

    public function reassignUser($user, string $role, $permissions = []): void
    {
        $this->unassignUser($user);
        $this->assignUser($user, $role, $permissions);
    }

    /**
     * @return bool
     */
    public function hasPharmaciesRequestedToVaccinate()
    {
        return Pharmacy::query()
            ->whereHas('settings', function ($query) {
                return $query->where('vaccinate_status', '=', PharmacyVaccinateStatusEnum::REQUESTED);
            })
            ->whereHas('users.pharmacyProfile.association', function ($query) {
                return $query->where('id', '=', $this->id);
            })
            ->exists();
    }

    public function frameworkContractStart(): ?Carbon
    {
        return $this->currentAssociationFrameworkContractHistory?->starts_at;
    }

    public function frameworkContractEnd(): ?Carbon
    {
        return $this->currentAssociationFrameworkContractHistory?->ends_at;
    }
}
