<?php

namespace App;

use App\Enums\Vaccinate\AgeGroupEnum;
use App\Enums\Vaccinate\CovidVaccinationStepEnum;
use App\Enums\Vaccinate\CovidVaccinationType;
use App\Enums\Vaccinate\GenderEnum;
use App\Enums\Vaccinate\InfluenzaVaccinationStepEnum;
use App\Enums\Vaccinate\ReasonToAbortEnum;
use App\Enums\Vaccinate\VaccinationStatus;
use App\Enums\Vaccinate\VaccinationTypeEnum;
use App\Traits\HasLocalizedTimestamp;
use Carbon\Carbon;
use Elastic\ScoutDriverPlus\Searchable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use RuntimeException;

/**
 * Class Vaccination
 *
 * @mixin IdeHelperVaccination
 */
class Vaccination extends Model
{
    use HasFactory, HasLocalizedTimestamp, Searchable;

    public function searchableAs(): string
    {
        return config('scout.prefix').'vaccinations_index';
    }

    public function shouldBeSearchable(): bool
    {
        return (int) $this->status === VaccinationStatus::FINISHED;
    }

    public function toSearchableArray(): array
    {
        $vaccinationType = 'N/A';
        if ($this->type === VaccinationTypeEnum::COVID && $this->covidVaccination) {
            $vaccinationType = CovidVaccinationType::getLabel($this->covidVaccination->vaccination_type);
        }

        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'created_at' => $this->created_at,
            'group' => VaccinationTypeEnum::getLabel($this->type),
            'type' => $vaccinationType,
            'vaccine' => $this->pharmaceutical?->name,
            'status' => $this->status ?? '',
            'reasons_to_abort' => (object) $this->getHumanReadableReasonsToAbort(),
            'pzn' => $this->pharmaceutical?->pzn,
            'inoculation_date' => $this->date?->format('Y-m-d\TH:i:s.u\Z'),
            'pharmacy' => [
                'id' => $this->pharmacy?->id,
                'name' => $this->pharmacy?->name,
                'location' => $this->pharmacy?->city,
                'telematics_id' => $this->pharmacy?->telematicsId?->fullId(),
            ],
            'association' => [
                'id' => $this->association?->id,
                'name' => $this->association?->name,
            ],
            'health_insurance' => [
                'id' => $this->healthInsuranceCompany?->id,
                'name' => $this->healthInsuranceCompany?->name,
            ],
            'patient' => [
                'age_group' => AgeGroupEnum::getLabel($this->age_group),
                'gender' => GenderEnum::getLabel($this->gender),
                'privately_insured' => $this->healthInsuranceCompany?->is_private,
            ],
        ];
    }

    protected $casts = [
        'reasons_to_abort' => 'array',
        'date' => 'datetime',
    ];

    protected $guarded = [];

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    protected static function booted()
    {
        static::creating(function ($vaccination) {
            $vaccination->uuid = Str::uuid();
        });
    }

    public function getLocalizedDateAttribute($value): Carbon
    {
        return $this->getLocalizedCarbonInstance($this->date);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function healthInsuranceCompany(): BelongsTo
    {
        return $this->belongsTo(HealthInsuranceCompany::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function pharmaceutical(): BelongsTo
    {
        return $this->belongsTo(Pharmaceutical::class);
    }

    public function vaccinationPatient(): HasOne
    {
        return $this->hasOne(VaccinationPatient::class);
    }

    public function additionalData(): ?HasOne
    {
        switch ($this->type) {
            case VaccinationTypeEnum::INFLUENZA:
                return $this->influenzaVaccination();

            default:
                return null;
        }
    }

    public function influenzaVaccination(): HasOne
    {
        return $this->hasOne(InfluenzaVaccination::class);
    }

    public function covidVaccination(): HasOne
    {
        return $this->hasOne(CovidVaccination::class);
    }

    public function isDoable(): bool
    {
        if ($this->influenzaVaccination !== null) {
            return $this->influenzaVaccination->isDoable();
        }

        return false;
    }

    public function scopeSuccessfullyFinished(Builder $query): Builder
    {
        return $query
            ->whereNull('reasons_to_abort')
            ->where('status', VaccinationStatus::FINISHED);
    }

    public function getDisplayStepAttribute(): int
    {
        if ($this->type == VaccinationTypeEnum::COVID) {
            return $this->step;
        }

        if ($this->influenzaVaccination->is_model) {
            return $this->step;
        }

        if ($this->step > InfluenzaVaccinationStepEnum::EVAL_USER) {
            return $this->step - 1;
        }

        return $this->step;
    }

    public function setPsnAttribute(array $value): void
    {
        if ($this->type === VaccinationTypeEnum::COVID) {
            $this->covidVaccination->update([
                'psn' => $value,
            ]);

            return;
        }

        if ($this->type === VaccinationTypeEnum::INFLUENZA) {
            $this->influenzaVaccination->update([
                'psn' => $value,
            ]);
        }
    }

    public function moveStepUp(int $step): void
    {
        if ($this->step < $step) {
            $this->update(['step' => $step]);
        }
    }

    public function abort(int $reason): void
    {
        $this->reasons_to_abort = \Arr::wrap($reason);

        if ($this->type === VaccinationTypeEnum::INFLUENZA) {
            $this->step = InfluenzaVaccinationStepEnum::EVAL_USER;
        } else {
            $this->step = CovidVaccinationStepEnum::FINISHED;
        }
        $this->save();
    }

    private function getHumanReadableReasonsToAbort(): array
    {
        if (! $this->reasons_to_abort) {
            return [];
        }

        $reasonsToAbort = [];
        foreach ($this->reasons_to_abort as $reasonToAbortId) {
            $reasonsToAbort[] = ReasonToAbortEnum::getTranslation($reasonToAbortId);
        }

        return $reasonsToAbort;
    }

    public function getActualVaccination(): CovidVaccination|InfluenzaVaccination
    {
        /** @phpstan-ignore-next-line because if a vaccination is null, it is not returned */
        return match (true) {
            (bool) $this->covidVaccination => $this->covidVaccination,
            (bool) $this->influenzaVaccination => $this->influenzaVaccination,
            default => throw new RuntimeException('Vaccination has no actual vaccination'),
        };
    }
}
