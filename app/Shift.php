<?php

namespace App;

use App\Domains\ShiftPlan\Domain\Observers\ShiftObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

#[ObservedBy([ShiftObserver::class])]
class Shift extends Model
{
    use HasFactory;

    protected $guarded = [
        'id',
        'uuid',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    public function plan(): BelongsTo
    {
        return $this->belongsTo(ShiftPlan::class, 'shift_plan_id');
    }

    public function planGroupUser(): BelongsTo
    {
        return $this->belongsTo(ShiftPlanGroupUser::class, 'shift_plan_group_user_id');
    }
}
