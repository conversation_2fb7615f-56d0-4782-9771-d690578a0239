<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property int $id
 * @property int $calendar_topic_group_id
 * @property string $name
 * @property bool $is_public
 * @property-read \App\CalendarTopicGroup $calendarTopicGroup
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Pharmacy> $pharmacies
 * @property-read int|null $pharmacies_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic public()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic whereCalendarTopicGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic whereIsPublic($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CalendarTopic whereName($value)
 *
 * @mixin \Eloquent
 */
class CalendarTopic extends Model
{
    public $timestamps = false;

    protected $guarded = [];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    public function calendarTopicGroup(): BelongsTo
    {
        return $this->belongsTo(CalendarTopicGroup::class);
    }

    public function pharmacies(): BelongsToMany
    {
        return $this->belongsToMany(Pharmacy::class);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function isPublic(): bool
    {
        return $this->is_public === true;
    }

    public function activate(): void
    {
        $this->is_public = true;
        $this->save();
    }

    public function deactivate(): void
    {
        $this->is_public = false;
        $this->save();
    }
}
