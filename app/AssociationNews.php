<?php

namespace App;

use App\Enums\AssociationNews\AssociationNewsNotificationOptionEnum;
use App\Enums\AssociationNews\AssociationNewsVisibilityEnum;
use App\Mail\InformUsersAboutNewAssociationNewsMail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AssociationNews extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [
        'id', 'association_id', 'created_at', 'updated_at',
    ];

    protected $casts = [
        'release_date' => 'datetime',
        'visibility' => AssociationNewsVisibilityEnum::class,
        'notification_option' => 'array',
    ];

    protected static function booted(): void
    {
        static::created(function (AssociationNews $news): void {
            if (in_array(AssociationNewsNotificationOptionEnum::Mail->value, $news->notification_option)) {
                InformUsersAboutNewAssociationNewsMail::informMembers($news);
            }
        });
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function scopeVisible(Builder $query): void
    {
        if (user()?->isOwner() === false) {
            $query->where('visibility', AssociationNewsVisibilityEnum::All);
        }
    }

    public function scopeNotifyInMail(Builder $query): void
    {
        $query->whereJsonContains(
            'notification_option',
            AssociationNewsNotificationOptionEnum::Mail->value
        );
    }

    public function scopeNotifyInNotificationCenter(Builder $query): void
    {
        $query->whereJsonContains(
            'notification_option',
            AssociationNewsNotificationOptionEnum::Notification->value
        );
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('masonry') // @phpstan-ignore-line
            ->width(650)
            ->sharpen(10)
            ->performOnCollections('header');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('header')
            ->singleFile()
            ->withResponsiveImages();
    }
}
