<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property int $feature_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation whereFeatureId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FeatureRecommendation whereUserId($value)
 *
 * @mixin \Eloquent
 */
class FeatureRecommendation extends Model
{
    protected $fillable = [
        'user_id',
        'feature_id',
    ];
}
