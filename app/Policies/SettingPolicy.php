<?php

namespace App\Policies;

use App\Enums\PermissionEnum;
use App\Enums\StaffRoleEnum;
use App\Traits\AuthorizesStaff;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class SettingPolicy
{
    use AuthorizesStaff, HandlesAuthorization, NovaDefaultFunctions;

    /**
     * @return array<string, array<PermissionEnum>>
     */
    public function staffPermissions(): array
    {
        return [
            StaffRoleEnum::SUPPORT => PermissionEnum::except([PermissionEnum::DELETE, PermissionEnum::RESTORE, PermissionEnum::FORCE_DELETE, PermissionEnum::UPDATE, PermissionEnum::CREATE]),
            StaffRoleEnum::OPERATIONS => PermissionEnum::except([PermissionEnum::DELETE, PermissionEnum::RESTORE, PermissionEnum::FORCE_DELETE, PermissionEnum::UPDATE, PermissionEnum::CREATE]),
        ];
    }

    public function updateTos(User $user): bool
    {
        return $user->isOwner() || $user->isSubOwner();
    }
}
