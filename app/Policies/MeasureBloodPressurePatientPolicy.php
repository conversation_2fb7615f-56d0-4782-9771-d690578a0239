<?php

namespace App\Policies;

use App\Domains\Subscription\Application\FeatureAccess\DataAccessPeriodFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\PharmaceuticalServicesFeatureAccess;
use App\Enums\PharmacyPermissionsEnum;
use App\Enums\PharmacyRoleEnum;
use App\MeasureBloodPressurePatient;
use App\Pharmacy;
use App\Settings\PharmaceuticalServiceSettings;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MeasureBloodPressurePatientPolicy
{
    use HandlesAuthorization;

    /**
     * Create a new policy instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    public function viewAny(User $user, Pharmacy $pharmacy)
    {
        return $this->store($user, $pharmacy);
    }

    public function update(User $user, MeasureBloodPressurePatient $measureBloodPressure)
    {
        return $this->store($user, $measureBloodPressure->pharmaceuticalService->pharmacy);
    }

    public function viewResult(User $user, Pharmacy $pharmacy): bool
    {
        return $this->store($user, $pharmacy);
    }

    public function store(User $user, Pharmacy $pharmacy)
    {
        if (! PharmaceuticalServicesFeatureAccess::check($pharmacy)->canUse() && ! DataAccessPeriodFeatureAccess::check($pharmacy)->canUse()) {
            return false;
        }

        $pharmaceuticalServiceSettings = app(PharmaceuticalServiceSettings::class);

        return
            (in_array('all', $pharmaceuticalServiceSettings->access_owner_pharmaceutical_services)
                || in_array(
                    $pharmacy->owner()->id,
                    $pharmaceuticalServiceSettings->access_owner_pharmaceutical_services
                ))
            &&
            (
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::OWNER) ||
                $user->hasPharmacyRole($pharmacy->id, PharmacyRoleEnum::SUB_OWNER) ||
                $user->hasPharmacyPermission($pharmacy->id, PharmacyPermissionsEnum::PHARMACEUTICAL_HIGH_BLOOD_PRESSURE)
            );
    }
}
