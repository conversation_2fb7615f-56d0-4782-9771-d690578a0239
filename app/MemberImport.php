<?php

namespace App;

use App\Domains\Association\Domain\Enums\MemberImportStatus;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class MemberImport
 *
 * @mixin IdeHelperMemberImport
 *
 * @property string $id
 * @property string|null $title
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property array<array-key, mixed> $data
 * @property MemberImportStatus $status
 * @property string|null $status_description
 * @property int $association_id
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Association $association
 *
 * @method static \Database\Factories\MemberImportFactory factory($count = null, $state = [])
 * @method static EloquentBuilder<static>|MemberImport newModelQuery()
 * @method static EloquentBuilder<static>|MemberImport newQuery()
 * @method static EloquentBuilder<static>|MemberImport onlyTrashed()
 * @method static EloquentBuilder<static>|MemberImport query()
 * @method static EloquentBuilder<static>|MemberImport whereAssociationId($value)
 * @method static EloquentBuilder<static>|MemberImport whereCreatedAt($value)
 * @method static EloquentBuilder<static>|MemberImport whereData($value)
 * @method static EloquentBuilder<static>|MemberImport whereDeletedAt($value)
 * @method static EloquentBuilder<static>|MemberImport whereEmail($value)
 * @method static EloquentBuilder<static>|MemberImport whereFirstName($value)
 * @method static EloquentBuilder<static>|MemberImport whereId($value)
 * @method static EloquentBuilder<static>|MemberImport whereLastName($value)
 * @method static EloquentBuilder<static>|MemberImport whereStatus($value)
 * @method static EloquentBuilder<static>|MemberImport whereStatusDescription($value)
 * @method static EloquentBuilder<static>|MemberImport whereTitle($value)
 * @method static EloquentBuilder<static>|MemberImport whereUpdatedAt($value)
 * @method static EloquentBuilder<static>|MemberImport withTrashed()
 * @method static EloquentBuilder<static>|MemberImport withoutTrashed()
 *
 * @mixin \Eloquent
 */
class MemberImport extends Model
{
    use HasFactory;
    use HasUuids;
    use Prunable;
    use SoftDeletes;

    protected $guarded = [
        'id', 'association_id', 'created_at', 'updated_at',
    ];

    protected $casts = [
        'data' => 'json',
        'status' => MemberImportStatus::class,
    ];

    public function prunable(): EloquentBuilder
    {
        return static::query()
            ->whereDate('created_at', '<=', now()->subMonthsNoOverflow(3))
            ->where('status', MemberImportStatus::Failed);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }
}
