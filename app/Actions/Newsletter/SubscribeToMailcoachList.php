<?php

namespace App\Actions\Newsletter;

use App\Data\Newsletter\SubscriberData;
use App\Enums\Newsletter\MailcoachList;
use App\Http\Integrations\Mailcoach\Requests\SubscribeToListRequest;
use App\User;
use Exception;

class SubscribeToMailcoachList
{
    use InteractsWithMailcoachLists;

    public static function execute(User $user, MailcoachList $list): SubscriberData
    {
        $request = new SubscribeToListRequest($user, $list->uuid());

        $response = self::connector()->send($request);

        if ($response->failed()) {
            throw new Exception(
                message: sprintf('Nutzer [%s] konnte Newsletter [%s] nicht abonnieren: %s', $user->id, $list->uuid(), $response->body()),
                previous: $response->toException(),
            );
        }

        $subscriberData = SubscriberData::from($response->json('data'));

        self::updateUserReference($user, $list, $subscriberData);

        return $subscriberData;
    }
}
