<?php

namespace App\Actions\Newsletter;

use App\Http\Integrations\Mailcoach\Requests\UpdateSubscriberRequest;
use App\Rules\GedisaMail;
use App\Settings\MailcoachSettings;
use App\User;
use Exception;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Sentry\Breadcrumb;

use function Sentry\addBreadcrumb;

class UpdateSubscriber
{
    use InteractsWithMailcoachLists;

    public static function execute(User $user): void
    {
        if (MailcoachSettings::enabled() === false) {
            return;
        }

        if (! $user->email) {
            return;
        }

        if (Str::of($user->email)->lower()->endsWith('@apomail.de')) {
            return;
        }

        try {
            addBreadcrumb(
                new Breadcrumb(
                    Breadcrumb::LEVEL_INFO,
                    Breadcrumb::TYPE_DEFAULT,
                    'User', // category
                    null, // message (optional)
                    ['email' => $user->email] // data (optional)
                )
            );

            Validator::make($user->toArray(), [
                'email' => ['required', app(GedisaMail::class)],
            ])->validate();
        } catch (ValidationException $exception) {
            // ValidationException will not be reported
            throw new Exception($exception);
        }

        $user->forEachMailcoachSubscription(function (string $subscriptionId, string $referenceFieldName) use ($user) {
            $request = new UpdateSubscriberRequest($user, $subscriptionId);
            $response = self::connector()->send($request);

            if ($response->failed()) {
                throw new \Exception(
                    message: sprintf('Nutzer [ID: %s] konnte nicht geupdated werden: %s', $subscriptionId, $response->body()),
                    previous: $response->toException()
                );
            }
        });
    }
}
