<?php

namespace App\Actions\Newsletter;

use App\Data\Newsletter\SubscriberData;
use App\Enums\Newsletter\MailcoachList;
use App\Http\Integrations\Mailcoach\MailcoachConnector;
use App\Integrations\IntegrationTypeEnum;
use App\Integrations\MailcoachIntegration;
use App\User;

trait InteractsWithMailcoachLists
{
    protected static function connector(): MailcoachConnector
    {
        return new MailcoachConnector;
    }

    protected static function updateUserReference(User $user, MailcoachList $list, ?SubscriberData $subscriberData = null): void
    {
        $mailcoachIntegrationModel = $user->getIntegration(IntegrationTypeEnum::Mailcoach);
        $mailcoachIntegration = $mailcoachIntegrationModel->settings ?? new MailcoachIntegration;

        $mailcoachIntegration->{$list->referenceFieldName()} = $subscriberData?->uuid;

        assert($mailcoachIntegration instanceof MailcoachIntegration);

        $user->setIntegration($mailcoachIntegration);
    }
}
