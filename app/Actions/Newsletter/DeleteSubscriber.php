<?php

namespace App\Actions\Newsletter;

use App\Enums\Newsletter\MailcoachList;
use App\Http\Integrations\Mailcoach\Requests\DeleteSubscriberRequest;
use App\Integrations\IntegrationTypeEnum;
use App\User;

class DeleteSubscriber
{
    use InteractsWithMailcoachLists;

    public static function execute(User $user, MailcoachList $list): void
    {
        $integration = $user->getIntegration(IntegrationTypeEnum::Mailcoach);

        if ($integration === null) {
            throw new \Exception(sprintf('User [ID: %s] existiert nicht in Mailcoach.', $user->id));
        }

        $subscriberId = $integration->settings?->{$list->referenceFieldName()};

        if ($subscriberId === null) {
            throw new \Exception(sprintf('User [ID: %s] hat keine Mailcoach ID für Liste [%s].', $user->id, $list->name));
        }

        $request = new DeleteSubscriberRequest($subscriberId);
        $response = self::connector()->send($request);

        if ($response->failed()) {
            throw new \Exception(
                message: sprintf('Nutzer [ID: %s] konnte nicht gelöscht werden: %s', $subscriberId, $response->body()),
                previous: $response->toException()
            );
        }

        self::updateUserReference($user, $list);
    }
}
