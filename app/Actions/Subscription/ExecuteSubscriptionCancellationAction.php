<?php

namespace App\Actions\Subscription;

use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Mail\SubscriptionCancelledMail;
use App\Pharmacy;
use App\SubscriptionCancellation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Subscription;

class ExecuteSubscriptionCancellationAction
{
    public function execute(SubscriptionCancellation $cancellation): void
    {
        DB::transaction(function () use ($cancellation) {
            if ($cancellation->status !== SubscriptionCancellationStatusEnum::CONFIRMED) {
                throw new SubscriptionCancellationException(
                    'Nur bestätigte Kündigungen können ausgeführt werden.'
                );
            }

            if ($cancellation->effective_date->isFuture()) {
                throw new SubscriptionCancellationException(
                    'Die Kündigung kann erst am Stichtag ausgeführt werden.'
                );
            }

            $pharmacy = $cancellation->pharmacy;

            $this->cancelStripeSubscriptions($pharmacy);

            $cancellation->update([
                'status' => SubscriptionCancellationStatusEnum::EXECUTED,
                'executed_at' => now(),
            ]);

            $this->sendCancellationExecutedNotification($pharmacy, $cancellation);
        });
    }

    private function cancelStripeSubscriptions(Pharmacy $pharmacy): void
    {
        $pharmacy->subscriptions()
            ->active()
            ->each(function (Subscription $subscription) {
                $subscription->cancelNow();
            });
    }

    private function sendCancellationExecutedNotification(Pharmacy $pharmacy, SubscriptionCancellation $cancellation): void
    {
        $emails = collect();

        // Rechnungsadresse
        if ($pharmacy->billingAddress?->email) {
            $emails->push($pharmacy->billingAddress->email);
        }

        $pharmacy->ownersAndSubowners()
            ->pluck('email')
            ->each(fn ($email) => $emails->push($email));

        $emails->unique()->each(function ($email) use ($pharmacy, $cancellation) {
            Mail::to($email)->queue(new SubscriptionCancelledMail($pharmacy, $cancellation));
        });
    }
}
