<?php

namespace App\Actions\Auth;

use App\Actions\Users\CreateUserAction;
use App\BrochureCode;
use App\Domains\Association\Domain\Enums\MemberImportStatus;
use App\Helper\BrochureCodeHelper;
use App\MemberImport;
use App\RuleSets\RegistrationRuleSet;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class RegistrationAction
{
    public static function getValidationRules(bool $isCompany = false)
    {
        return RegistrationRuleSet::make()
            ->merge('company_name', [Rule::requiredIf($isCompany)])
            ->put('registration_request_id', ['nullable', 'exists:registration_requests,id'])
            ->put('association_id', ['nullable', 'exists:associations,id'])
            ->put('optional_address_line', ['nullable', 'string', 'max:255'])
            ->except('house_number')
            ->get();
    }

    public function createInstantRegistration(array $data, bool $skipValidation = false)
    {
        /** @var MemberImport|null $memberImport */
        $memberImport = MemberImport::query()->find(Arr::get($data, 'member_import_id'));
        $memberImport?->update(['status' => MemberImportStatus::Importing->value]);

        try {
            if (! $skipValidation) {
                Validator::make(
                    $data,
                    self::getValidationRules((bool) Arr::get($data, 'is_company')),
                )->validate();
            }
        } catch (Exception $exception) {
            $memberImport?->update([
                'status' => MemberImportStatus::Failed->value,
                'status_description' => 'Stammdaten unvollständig',
            ]);
        }

        $brochureCodeData = array_merge($data, [
            'code' => BrochureCodeHelper::convertUnreadableCharacter(BrochureCodeHelper::generateCode()),
            'name_affix' => Arr::get($data, 'title'),
            'street' => Arr::get($data, 'street').' '.Arr::get($data, 'house_number'),
        ]);

        unset($brochureCodeData['title'], $brochureCodeData['house_number'], $brochureCodeData['member_import_id'], $brochureCodeData['user_id']);

        DB::beginTransaction();

        try {
            $brochureCode = BrochureCode::forceCreate($brochureCodeData);

            $user = app(CreateUserAction::class)
                ->setSalutation($brochureCode->salutation)
                ->setTitle($brochureCode->name_affix)
                ->setFirstName($brochureCode->first_name)
                ->setLastName($brochureCode->last_name)
                ->setLoginEmail($brochureCode->email)
                ->setNotificationsEmail($brochureCode->email)
                ->setPhone($brochureCode->phone)
                ->setPharmacyUser(['association_id' => $brochureCode->association_id])
                ->setCompany($brochureCode->is_company ? $brochureCode->company_name : null)
                ->createUser();

            if ($memberImport) {
                $memberImport->delete();
            }

            $brochureCode->user_id = $user->id;
            $brochureCode->save();

            DB::commit();
            Log::channel('association-member-import')->info(
                'Registration Action succeeded',
                ['user' => user()?->uuid ?? user()?->id ?? 'N/A']
            );
        } catch (Exception $e) {
            DB::rollBack();

            $memberImport?->update([
                'status' => MemberImportStatus::Failed->value,
                'status_description' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    public function sendInstantRegistrationLink(BrochureCode $brochureCode)
    {
        throw new Exception(
            'Mit der Einführung des RISE IDP, kann der Registrierungslink erstmal nur
            über die Admin Oberfläche bei RISE erneut versendet werden, Task dazu: AP-439'
        );
    }
}
