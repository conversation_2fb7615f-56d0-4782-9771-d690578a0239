<?php

namespace App\Actions\DocSpaces;

use App\DocSpace;
use App\Pharmacy;
use Exception;
use Illuminate\Support\Facades\DB;

class UpdateDocSpaceAction
{
    public array $errors = [];

    public function __construct(
        public DocSpace $docSpace,
        public ?string $description,
        public array $groups,
        public int $hardQuotaInGB
    ) {}

    public function execute(): self
    {
        DB::beginTransaction();

        try {
            $pharmacy = $this->docSpace->pharmacy;

            assert($pharmacy instanceof Pharmacy);

            $action = app()->make(UpdateRiseSDRDocSpaceAction::class, ['docSpace' => $this->docSpace])
                ->setDescription($pharmacy, $this->description)
                ->setHardAndSoftQuotaInGB($this->hardQuotaInGB)
                ->execute();

            $this->docSpace->update([
                'description' => $action->description,
                'soft_quota' => $action->getSoftQuotaInGB() * 1024,
                'hard_quota' => $action->getHardQuotaInGB() * 1024,
                'current_usage' => $action->getLastUsageInGB() * 1024,
                'max_upload_file_size' => $action->maxUploadFileSize,
                'allowed_file_types' => $action->allowedFileTypes,
                'encrypted' => $action->encrypted,
            ]);

            $this->docSpace->docSpaceGroups()->sync($this->groups);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }

        return $this;
    }
}
