<?php

namespace App\Livewire\Subscription;

use App\Actions\Subscription\RequestSubscriptionCancellationAction;
use App\Actions\Subscription\RevokeSubscriptionCancellationAction;
use App\Domains\Payment\Domain\Data\StripePublicRepresentationData;
use App\Domains\Subscription\Domain\Enums\StripeProductTypeEnum;
use App\Enums\SubscriptionCancellationStatusEnum;
use App\Exceptions\SubscriptionCancellationException;
use App\Pharmacy;
use App\SubscriptionCancellation;
use Livewire\Component;

class CancellationManager extends Component
{
    public Pharmacy $pharmacy;

    public ?StripePublicRepresentationData $subscribedBaseProduct;

    public ?SubscriptionCancellation $cancellation = null;

    public string $cancellationReason = '';

    public string $revokeReason = '';

    public bool $confirmCancellation = false;

    public bool $confirmRevoke = false;

    public function mount(): void
    {
        $this->loadBaseStripeProduct();
        $this->loadCancellation();
    }

    public function loadBaseStripeProduct(): void
    {
        $product = collect($this->pharmacy->getProductsInStripeSubscription())
            ->first(fn ($product) => $product::$type === StripeProductTypeEnum::BASE);

        $this->subscribedBaseProduct = $product
            ? $product::make()->getPublicRepresentationData($this->pharmacy)
            : null;
    }

    public function loadCancellation(): void
    {
        $this->cancellation = $this->pharmacy->subscriptionCancellation()
            ->whereIn('status', [
                SubscriptionCancellationStatusEnum::PENDING,
                SubscriptionCancellationStatusEnum::CONFIRMED,
            ])
            ->first();
    }

    public function requestCancellation(): void
    {
        $this->validate(rules: [
            'cancellationReason' => ['nullable', 'string'],
            'confirmCancellation' => ['required', 'accepted'],
        ], messages: [
            'confirmCancellation.accepted' => 'Sie müssen der Kündigung zustimmen.',
        ]);

        try {
            app(RequestSubscriptionCancellationAction::class)->execute(
                $this->pharmacy,
                auth()->user(),
                $this->cancellationReason ?: null
            );

            notify('Ihre Kündigung wurde erfolgreich beantragt.');

            redirect()->route('pharmacies.subscription', $this->pharmacy);
        } catch (SubscriptionCancellationException $e) {
            $this->addError('cancellation', $e->getMessage());
        }
    }

    public function revokeCancellation(): void
    {
        $this->validate(rules: [
            'revokeReason' => ['nullable', 'string'],
            'confirmRevoke' => ['required', 'accepted'],
        ], messages: [
            'confirmRevoke.accepted' => 'Sie müssen dem Widerruf zustimmen.',
        ]);

        try {
            app(RevokeSubscriptionCancellationAction::class)->execute(
                $this->pharmacy,
                auth()->user(),
                $this->revokeReason ?: null
            );

            notify('Ihre Kündigung wurde erfolgreich widerrufen.');

            redirect()->route('pharmacies.subscription', $this->pharmacy);
        } catch (SubscriptionCancellationException $e) {
            $this->addError('revoke', $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.subscription.cancellation-manager');
    }
}
