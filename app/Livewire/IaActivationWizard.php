<?php

namespace App\Livewire;

use App\Features\IhreApothekenPreflight;
use App\Helper\PharmacySessionHelper;
use App\Livewire\Ia\AuthFlow\ApoGuide;
use App\Livewire\Ia\AuthFlow\BeforeWeStart;
use App\Livewire\Ia\AuthFlow\MatchPharmacy;
use App\Livewire\Ia\AuthFlow\MergeData;
use App\Livewire\Ia\AuthFlow\Order;
use App\Livewire\Ia\AuthFlow\OrderConfirmation;
use App\Livewire\Ia\AuthFlow\RolesAndPermissions;
use App\Livewire\Ia\AuthFlow\Success;
use App\Livewire\Ia\AuthFlow\Terms;
use App\Livewire\Ia\AuthFlow\VendorActivationLinkRequest;
use App\Livewire\Ia\AuthFlow\VendorActivationLinkRequestConfirmation;
use App\Livewire\Wizard\Wizard;
use App\Pharmacy;
use Feature;
use Livewire\Attributes\On;

class IaActivationWizard extends Wizard
{
    public string $currentStep = BeforeWeStart::class;

    public int $totalSteps = 5;

    protected array $steps = [
        BeforeWeStart::class,
        MatchPharmacy::class,
        Order::class,
        OrderConfirmation::class,
        RolesAndPermissions::class,
        Success::class,
        Terms::class,
        VendorActivationLinkRequest::class,
        VendorActivationLinkRequestConfirmation::class,
        MergeData::class,
        ApoGuide::class,
    ];

    public function mount(): void
    {
        if (Feature::active(IhreApothekenPreflight::class)) {
            $user = user();

            $user || abort(403);

            $this->currentStep = $user->isPreflightUser() ? BeforeWeStart::class : Order::class;
        }

        parent::mount();
    }

    public function resetState(): void
    {
        if (Feature::active(IhreApothekenPreflight::class)) {
            $user = user();

            $user || abort(403);

            $this->currentStep = $user->isPreflightUser() ? BeforeWeStart::class : Order::class;
        } else {
            $this->currentStep = BeforeWeStart::class;
        }
    }

    #[On('activation-completed')]
    public function redirectToActivatedReservationSystem(int $pharmacyId): void
    {
        $this->resetState();

        $matchedPharmacy = Pharmacy::find($pharmacyId);

        PharmacySessionHelper::set($matchedPharmacy);

        $this->redirectRoute('ia.orders.reservation-system', [
            'pharmacy' => $matchedPharmacy,
        ]);
    }
}
