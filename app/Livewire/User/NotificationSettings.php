<?php

namespace App\Livewire\User;

use App\Actions\Newsletter\DeleteSubscriber;
use App\Actions\Newsletter\SubscribeToMailcoachList;
use App\Enums\Newsletter\MailcoachList;
use App\Traits\Livewire\CanNotify;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use Livewire\Attributes\Computed;
use Livewire\Component;

class NotificationSettings extends Component
{
    use CanNotify;

    public User $user;

    public bool $subscribed = false;

    public string $list;

    public function mount(): void
    {
        assert(user() instanceof User);
        $this->user = user();

        $this->subscribed = $this->isSubscribed();
    }

    public function toggleNewsletter(): void
    {
        DB::beginTransaction();

        try {
            match ($this->isSubscribed()) {
                true => $this->unsubscribe($this->user),
                default => $this->subscribe($this->user),
            };

            DB::commit();
        } catch (\Exception $exception) {
            report($exception);

            $this->notify('Bei der Kommunikation mit dem Newsletteranbieter ist ein Fehler aufgetreten. Bitte kontaktieren Sie den Support.', 'error');

            DB::rollBack();
        }

        $this->subscribed = $this->isSubscribed();
    }

    public function isSubscribed(): bool
    {
        return (bool) $this->user->refresh()->hasMailcoachSubscriptions(MailcoachList::Advertising);
    }

    #[Computed]
    public function isForeignSubscribed(): bool
    {
        return User::query()
            ->where('id', '<>', $this->user->id)
            ->where('email', $this->user->email)
            ->subscribedToMailcoachList(MailcoachList::Advertising)
            ->exists();
    }

    protected function subscribe(User $user): void
    {
        SubscribeToMailcoachList::execute($user, MailcoachList::Advertising);

        $this->notify('Ihre Anmeldung für unseren Newsletter war erfolgreich.');
    }

    protected function unsubscribe(User $user): void
    {
        DeleteSubscriber::execute($user, MailcoachList::Advertising);

        $this->notify('Ihre Abmeldung für unseren Newsletter war erfolgreich.');
    }

    public function render(): View
    {
        return view('livewire.user.notification-settings');
    }
}
