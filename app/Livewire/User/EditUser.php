<?php

namespace App\Livewire\User;

use App\Livewire\Forms\User\AccountForm;
use App\Traits\Livewire\CanNotify;
use App\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use Livewire\Attributes\Computed;
use Livewire\Component;

class EditUser extends Component
{
    use CanNotify;

    public User $user;

    public AccountForm $form;

    public function mount(): void
    {
        $this->form->setUser($this->user);
    }

    public function save(): void
    {
        $this->user = $this->form->save();

        $this->notify(__('notifications.account.updated'));
    }

    public function render(): View
    {
        return view('livewire.user.edit-user');
    }

    #[Computed]
    public function currentlyChangingEmail(): ?string
    {
        $email = Cache::get('emailChanges.user.'.$this->user->id);

        assert(is_string($email) || is_null($email));

        return $email;
    }

    #[Computed]
    public function newChangeEmailSend(): bool
    {
        return (bool) session('newChangeEmailSend');
    }
}
