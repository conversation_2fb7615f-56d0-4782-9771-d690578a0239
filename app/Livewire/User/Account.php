<?php

namespace App\Livewire\User;

use App\User;
use Illuminate\View\View;
use Livewire\Attributes\Url;
use Livewire\Component;

class Account extends Component
{
    public User $user;

    #[Url]
    public string $tab = 'account';

    public function mount(): void
    {
        assert(user() instanceof User);

        $this->user = user();
    }

    public function render(): View
    {
        return view('livewire.user.account')
            ->extends('layouts.app', [
                'novue' => true,
            ])
            ->section('content');
    }
}
