<?php

namespace App\Livewire\Ia\AuthFlow;

use App\Attributes\Persist;
use App\Features\IhreApothekenPreflight;
use App\Helper\IaHelper;
use App\Http\Integrations\Ia\PartnerApi\Exceptions\IaRequestFailedException;
use App\Http\Integrations\Ia\PartnerApi\Exceptions\TokenExpiredOrInvalidException;
use App\Livewire\Wizard\WizardStep;
use App\Traits\InteractsWithSession;
use Illuminate\Contracts\View\View;
use Laravel\Pennant\Feature;
use Livewire\Attributes\Computed;
use Livewire\Attributes\Validate;

class Terms extends WizardStep
{
    use InteractsWithSession;

    public string $title = 'Nutzungsbedingungen und Datenschutzhinweise';

    public ?string $subtitle = 'Um den Online-Shop nutzen zu können, stimmen Sie bitte unseren Nutzungsbedingungen und Datenschutzbestimmungen zu.';

    public ?int $stepNumber = 2;

    #[Persist, Validate(['required', 'accepted', 'bool'])]
    public bool $termsAccepted = false;

    #[Persist, Validate(['required', 'accepted', 'bool'])]
    public bool $privacyPolicyAccepted = false;

    public bool $isZpaCustomer = true;

    public function mount(): void
    {
        try {
            $this->isZpaCustomer = IaHelper::checkIsZpaCustomer();
        } catch (IaRequestFailedException $exception) {
            $this->setFatalError(
                'Entschuldigung. Wir haben Probleme mit der Kommunikation zu IhreApotheken.',
                'Bitte kontaktieren Sie den <NAME_EMAIL>.'
            );
        } catch (TokenExpiredOrInvalidException $exception) {
            $this->setFatalError(
                'Ihr Aktivierungslink ist leider abgelaufen.',
                'Bitte kontaktieren Sie den <NAME_EMAIL>.'
            );
        }
    }

    /** @return array<string, string> */
    public function messages(): array
    {
        return [
            'termsAccepted.accepted' => 'Bitte akzeptieren Sie die Nutzungsbedingungen.',
            'privacyPolicyAccepted.accepted' => 'Bitte stimmen Sie den Datenschutzbestimmungen zu.',
        ];
    }

    public function prevStep(): ?string
    {
        $user = user();

        $user || abort(403);

        if (Feature::active(IhreApothekenPreflight::class) && ! $user->isPreflightUser()) {
            return Order::class;
        }

        return BeforeWeStart::class;
    }

    public function nextStep(): ?string
    {
        $user = user();

        $user || abort(403);

        if (Feature::active(IhreApothekenPreflight::class) && ! $user->isPreflightUser()) {
            return Order::class;
        }

        if (session(IaHelper::authTokenKey())) {
            return MatchPharmacy::class;
        }

        return VendorActivationLinkRequest::class;
    }

    public function submit(): void
    {
        $this->validate();

        $this->next();
    }

    protected function sessionPrefix(): ?string
    {
        return IaHelper::wizardSessionPrefix();
    }

    #[Computed]
    public function termsUrl(): string
    {
        return $this->isZpaCustomer
            ? route('uploads.show', 'nutzungsbedingungen-ia-bestandskunden')
            : route('uploads.show', 'nutzungsbedingungen-ia-neukunden');
    }

    public function render(): View
    {
        return view('livewire.ia.auth-flow.terms');
    }
}
