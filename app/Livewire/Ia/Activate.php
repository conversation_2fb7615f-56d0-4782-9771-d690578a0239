<?php

namespace App\Livewire\Ia;

use App\IaOrder;
use App\Pharmacy;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Livewire\Component;

class Activate extends Component
{
    use AuthorizesRequests;

    public Collection $iaOrders;

    public function mount(Pharmacy $pharmacy): void
    {
        $this->authorize('viewAny', IaOrder::class);

        $user = user();

        $user || abort(403);

        $user->isOwnerOrSubOwnerOfPharmacy() || abort(403);

        $this->iaOrders = $user->pharmacies()->whereHas('iaOrder')->get();
    }

    public function render(): View
    {
        return view('livewire.ia.enable')
            ->extends('layouts.app', [
                'novue' => true,
            ])
            ->section('content');
    }
}
