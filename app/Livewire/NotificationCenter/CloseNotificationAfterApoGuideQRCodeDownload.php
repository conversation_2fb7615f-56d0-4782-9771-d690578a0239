<?php

namespace App\Livewire\NotificationCenter;

use App\Pharmacy;
use App\Support\NotificationCenter\ApoGuideQRCodeNotification;
use App\User;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Livewire\Component;

class CloseNotificationAfterApoGuideQRCodeDownload extends Component
{
    public User $user;

    public Pharmacy $pharmacy;

    public string $route;

    public string $text;

    public function close(): RedirectResponse|\Illuminate\Contracts\Foundation\Application|Redirector|Application
    {
        ApoGuideQRCodeNotification::close($this->pharmacy);

        $this->dispatch('reload-notification-center');

        return redirect($this->route);
    }

    public function render(): View|Application|Factory|\Illuminate\View\View|\Illuminate\Contracts\Foundation\Application
    {
        return view('livewire.close-notification-after-apo-guide-q-r-code-download');
    }
}
