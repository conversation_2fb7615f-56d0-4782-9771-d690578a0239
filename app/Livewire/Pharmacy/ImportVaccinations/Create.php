<?php

namespace App\Livewire\Pharmacy\ImportVaccinations;

use App\Enums\VaccinationImport\AccountingTypeEnum;
use App\Enums\VaccinationImport\VaccineNameEnum;
use App\Pharmacy;
use App\Support\IBMCertificationApi;
use App\VaccinationImport;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Livewire\Component;

class Create extends Component
{
    use AuthorizesRequests;

    public Pharmacy $pharmacy;

    public VaccinationImport $model;

    public bool $editMode = true;

    public bool $importCreated = false;

    public bool $recoveredCertificate = false;

    public ?string $firstName = '';

    public ?string $lastName = '';

    public ?string $birthdate = '';

    public ?string $vaccine = '';

    public ?string $doseNumber = '';

    public ?string $doseCount = '';

    public ?string $vaccinationDate = '';

    public string $vaccineTarget = '';

    public string $vaccineType = '';

    public string $vaccineHolder = '';

    public ?string $qrCodeString = null;

    public array $doseOptions = [];

    public function render()
    {
        return view('livewire.pharmacy.import-vaccinations.create');
    }

    public function submit()
    {
        $this->authorize('create', [VaccinationImport::class, $this->pharmacy]);

        sleep(config('services.ibm.vaccination-center.throttle'));

        $this->validate([
            'firstName' => ['required', 'string', 'max:50', 'regex:/^([^0-9]*)$/'], // regex excludes numbers (ibm api requirement)
            'lastName' => ['required', 'string', 'max:50', 'regex:/^([^0-9]*)$/'],
            'birthdate' => ['required', 'date', 'before_or_equal:'.now(), 'after:01-01-1900', 'before:vaccinationDate'],
            'vaccine' => ['required', Rule::in(VaccineNameEnum::getAll())],
            'doseNumber' => ['required', 'integer', 'min:1', 'max:2'],
            'vaccinationDate' => ['required', 'date', 'before_or_equal:'.now(), 'after:01-01-2020'], // 'after' is requirement of ibm api
        ]);

        $this->vaccineTarget = VaccineNameEnum::getTarget($this->vaccine);
        $this->vaccineType = VaccineNameEnum::getType($this->vaccine);
        $this->vaccineHolder = VaccineNameEnum::getHolder($this->vaccine);

        $this->editMode = false;
    }

    public function edit()
    {
        $this->editMode = true;
        $this->qrCodeString = null;
    }

    public function generateQr()
    {
        sleep(config('services.ibm.vaccination-center.throttle'));
        $this->authorize('create', [VaccinationImport::class, $this->pharmacy]);

        if (! $this->qrCodeString) {
            try {
                $request = app(IBMCertificationApi::class)->issueCertificate(
                    $this->generateImportData(),
                    'code'
                );
            } catch (\Exception $e) {
                $this->notify('Wir bekommen gerade leider kein Feedback von der zertifikatsausstellenden Stelle. Bitte versuchen Sie es noch einmal.', 'error');
                report($e);

                return;
            }

            if ($request->failed()) {
                $this->notify('Wir bekommen gerade leider kein Feedback von der zertifikatsausstellenden Stelle. Bitte versuchen Sie es noch einmal.', 'error');

                return;
            }

            $this->createModelIfNotExistend();

            $this->qrCodeString = $request->body();
        }

        $this->openModal('qr-code-modal');
    }

    public function generatePdf()
    {
        sleep(config('services.ibm.vaccination-center.throttle'));
        $this->authorize('create', [VaccinationImport::class, $this->pharmacy]);

        try {
            $request = app(IBMCertificationApi::class)->issueCertificate(
                $this->generateImportData(),
                'pdf'
            );
        } catch (\Exception $e) {
            $this->notify('Wir bekommen gerade leider kein Feedback von der zertifikatsausstellenden Stelle. Bitte versuchen Sie es noch einmal.', 'error');
            report($e);

            return;
        }

        if ($request->failed()) {
            $this->notify('Wir bekommen gerade leider kein Feedback von der zertifikatsausstellenden Stelle. Bitte versuchen Sie es noch einmal.', 'error');

            return;
        }

        $code = $request->body();

        $this->createModelIfNotExistend();

        $this->dispatch('hide-edit-button');

        return response()->streamDownload(function () use ($code) {
            echo $code;
        }, str_replace(['\\', '/'], '', 'Impfzertifikat_'.$this->firstName.'_'.$this->lastName.'.pdf'));
    }

    public function vaccineChange()
    {
        if ($this->recoveredCertificate) {
            $this->doseOptions = $this->generateDoseOptions(1);

            $this->doseNumber = 1;
            $this->doseCount = 1;

            return;
        }

        $this->doseCount = VaccineNameEnum::getDoseCount($this->vaccine);

        if (! $this->doseNumber) {
            $this->doseNumber = 1;
        }

        if ($this->doseNumber > $this->doseCount) {
            $this->doseNumber = $this->doseCount;
        }

        $this->doseOptions = $this->generateDoseOptions($this->doseCount);
    }

    private function createModelIfNotExistend()
    {
        if (! $this->model->exists) {
            $this->importCreated = true;
            $this->createModel();
        }
    }

    private function createModel()
    {
        $this->model = $this->model->create([
            'pharmacy_id' => $this->pharmacy->id,
            'search_string' => $this->buildSearchString(),
            'search_token' => Hash::make($this->buildTokenString()),
            'accounting_type' => $this->getAccountingType(),
            'telematics_id' => $this->pharmacy->telematicsId->fullId(),
            'ibm_id' => $this->pharmacy->ibm_id,
            'is_recovered' => $this->recoveredCertificate,
        ]);
    }

    private function buildSearchString(): string
    {
        return mb_substr(trim($this->lastName), 0, 1).mb_substr($this->birthdate, 3, 1).mb_substr(trim($this->firstName), -1, 1).mb_substr($this->birthdate, -1, 1);
    }

    private function buildTokenString($doseNumber = null): string
    {
        if ($doseNumber === null) {
            $doseNumber = $this->doseNumber;
        }

        return $doseNumber.$this->lastName.$this->firstName.$this->birthdate;
    }

    private function getAccountingType(): int
    {
        $imports = VaccinationImport::where('pharmacy_id', $this->pharmacy->id)
            ->where('search_string', $this->buildSearchString())
            ->where('id', '!=', $this->model->id ?? 0) // don't use current model if updating
            ->get();

        $patientHadImportBefore = false;
        $currentDoseCounts = []; // Dose counts in the last X minutes (see constant for exact value)

        for ($doseNumber = 1; $doseNumber <= $this->doseCount; $doseNumber++) {
            /** @var VaccinationImport $import */
            foreach ($imports as $import) {
                if (Hash::check($this->buildTokenString($doseNumber), $import->search_token)) {
                    $patientHadImportBefore = true;

                    if ($import->created_at >= Carbon::now()->subMinutes(VaccinationImport::MINUTES_BETWEEN_IMPORTS_TO_COUNT_AS_RECERTIFIED)) {
                        if (! isset($currentDoseCounts[$doseNumber])) {
                            $currentDoseCounts[$doseNumber] = 0;
                        }
                        $currentDoseCounts[$doseNumber]++;
                    }
                }
            }
        }

        if (! $patientHadImportBefore) { // First ever import for patient
            return AccountingTypeEnum::FIRST;
        }

        if (empty($currentDoseCounts)) { // First import in last X minutes -> recertified
            return AccountingTypeEnum::RECERTIFIED;
        }

        if (! isset($currentDoseCounts[$this->doseNumber])) { // First ever nth dose for patient
            return AccountingTypeEnum::FOLLOW;
        }

        if (max($currentDoseCounts) < ($currentDoseCounts[$this->doseNumber] + 1)) {
            return AccountingTypeEnum::RECERTIFIED;
        } else {
            return AccountingTypeEnum::FOLLOW;
        }
    }

    private function generateImportData()
    {
        return [
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'birthdate' => Carbon::parse($this->birthdate)->format('Y-m-d'),
            'vaccination_center_uuid' => (config('services.ibm.vaccination-center.use-pharmacy-uuid') && $this->pharmacy->ibm_id) ? $this->pharmacy->ibm_id : config('services.ibm.vaccination-center.uuid'),
            'disease_id' => $this->vaccineTarget,
            'prophylaxis_id' => $this->vaccineType,
            'medicinal_product_id' => $this->vaccine,
            'auth_holder_id' => $this->vaccineHolder,
            'shot_one_number' => (int) $this->doseNumber,
            'shot_two_number' => (int) $this->doseCount,
            'date_of_shot' => Carbon::parse($this->vaccinationDate)->format('Y-m-d'),
        ];
    }

    private function generateDoseOptions(int $count)
    {
        $options = [];

        for ($dose = 1; $dose <= $count; $dose++) {
            $options[$dose] = $dose.'/'.$count;
        }

        return $options;
    }
}
