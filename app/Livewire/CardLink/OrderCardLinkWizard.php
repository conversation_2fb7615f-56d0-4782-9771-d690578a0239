<?php

namespace App\Livewire\CardLink;

use App\Livewire\CardLink\OrderCardLinkWizardSteps\AcceptTermsStep;
use App\Livewire\CardLink\OrderCardLinkWizardSteps\ActivateApoGuideStep;
use App\Livewire\CardLink\OrderCardLinkWizardSteps\ChoosePackageStep;
use App\Livewire\CardLink\OrderCardLinkWizardSteps\FailedStep;
use App\Livewire\CardLink\OrderCardLinkWizardSteps\SelectPharmaciesStep;
use App\Livewire\Wizard\Wizard;

class OrderCardLinkWizard extends Wizard
{
    public string $currentStep = ChoosePackageStep::class;

    public int $totalSteps = 4;

    protected array $steps = [
        SelectPharmaciesStep::class,
        ChoosePackageStep::class,
        AcceptTermsStep::class,
        ActivateApoGuideStep::class,
        FailedStep::class,
    ];

    public function before(): void
    {
        session()->forget('card-link');
    }

    public function onClose(): void
    {
        session()->forget('card-link');

        $this->currentStep = ChoosePackageStep::class;
    }
}
