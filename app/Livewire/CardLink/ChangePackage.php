<?php

namespace App\Livewire\CardLink;

use App\CardLinkOrder;
use App\Data\CardLink\OrderInformationData;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Features\CardLinkOrderSilent;
use App\Helper\CardLinkOrderHelper;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rules\Enum;
use Laravel\Pennant\Feature;
use Livewire\Component;
use Livewire\Features\SupportRedirects\Redirector;

class ChangePackage extends Component
{
    public CardLinkOrder $cardLinkOrder;

    public ?CardLinkPackageEnum $selectedPackage;

    public function mount(): void
    {
        $this->authorize('view', $this->cardLinkOrder);

        $this->selectedPackage = CardLinkOrderHelper::package($this->cardLinkOrder);
    }

    /**
     * @return array<string, array<string|Enum>>
     */
    public function rules(): array
    {
        return [
            'selectedPackage' => ['required', new Enum(CardLinkPackageEnum::class)],
        ];
    }

    public function render(): View
    {
        return view('livewire.card-link.change-package')
            ->extends('layouts.app', [
                'novue' => true,
            ])
            ->section('content');
    }

    public function submit(): Redirector|RedirectResponse
    {
        $this->validate();
        assert($this->selectedPackage instanceof CardLinkPackageEnum);

        $orderInformation = CardLinkOrderHelper::orderInformation($this->cardLinkOrder);

        assert($orderInformation instanceof OrderInformationData);

        // Downgrade
        if (
            $this->selectedPackage->limit() !== null
            && $this->selectedPackage->limit() < ($orderInformation->package->limit() ?? PHP_INT_MAX)
            && Feature::for($this->cardLinkOrder)->active(CardLinkOrderSilent::class)
        ) {
            return redirect()->route('card-link.confirm-downgrade', ['pharmacy' => $this->cardLinkOrder->pharmacy, 'package' => $this->selectedPackage->name]);
        }

        \App\Actions\CardLink\ChangePackage::execute($this->cardLinkOrder, $this->selectedPackage);
        $this->cardLinkOrder->refresh();
        $this->selectedPackage = CardLinkOrderHelper::package($this->cardLinkOrder);

        return to_route('card-link', $this->cardLinkOrder->pharmacy);
    }
}
