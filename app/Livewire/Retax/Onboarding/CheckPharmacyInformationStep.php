<?php

namespace App\Livewire\Retax\Onboarding;

use App\AccountingCenter;
use App\Attributes\Persist;
use App\Enums\SessionEnum;
use App\Helper\PharmacyHelper;
use App\Livewire\Wizard\WizardStep;
use App\Pharmacy;
use App\Rules\Ownership;
use App\TelematicsId;
use App\Traits\InteractsWithSession;
use App\Traits\InteractsWithWizard;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Livewire\Attributes\Computed;

class CheckPharmacyInformationStep extends WizardStep
{
    use InteractsWithSession;
    use InteractsWithWizard;

    public ?int $stepNumber = 3;

    public ?string $nextStep = WelcomeDocSpaceStep::class;

    /** @var array<int, array<string, string>> */
    #[Persist]
    public array $pharmacyInformation = [];

    public ?int $currentPharmacyId = null;

    public function mount(): void
    {
        $this->pharmacyInformation = $this->pharmacies()->mapWithKeys(fn (Pharmacy $pharmacy) => [
            $pharmacy->id => [
                'fax' => $pharmacy->fax,
                'accounting_center' => $pharmacy->accounting_center_id ?? $pharmacy->custom_accounting_center ?? null,
                'ik_number' => $pharmacy->institute_id,
                'telematik_id' => $pharmacy->telematicsId?->fullId() ?? '',
                'search' => AccountingCenter::find($pharmacy->accounting_center_id)?->company ?? $pharmacy->custom_accounting_center ?? null,
            ],
        ])->toArray();

        $this->persistToSession();
    }

    protected function title(): string
    {
        return 'Retax einrichten';
    }

    /**
     * @return array<string, array<int, Ownership|Exists|string>>
     */
    public function rules(): array
    {
        return [
            'pharmacyInformation' => ['required', 'array', 'min:1'],
            'pharmacyInformation.*.fax' => ['required', 'string', 'min:3', 'max:32'],
            'pharmacyInformation.*.ik_number' => ['required', 'numeric', 'digits:9'],
            'pharmacyInformation.*.accounting_center' => ['required_without:pharmacyInformation.*.search'],
            'pharmacyInformation.*.search' => ['required_without:pharmacyInformation.*.accounting_center'],
            // 'pharmacyInformation.*.telematik_id' => ['required', 'string', 'min:3', 'max:32'],
            'pharmacyInformation.*.validity_accepted' => ['required', 'accepted'],
        ];
    }

    /**
     * @return string[]
     */
    public function messages(): array
    {
        return [
            'pharmacyInformation.required' => 'Es müssen alle Informationen ausgefüllt werden.',
            'pharmacyInformation.min' => 'Es müssen alle Informationen ausgefüllt werden.',
            'pharmacyInformation.*.validity_accepted' => 'Bitte bestätigen Sie, dass die angegebenen Informationen korrekt sind.',
            'pharmacyInformation.*.accounting_center' => 'Es muss ein Abrechnungszentrum ausgewählt werden.',
            'pharmacyInformation.*.search' => 'Es muss ein Abrechnungszentrum ausgewählt werden.',
        ];
    }

    public function submit(): void
    {
        $this->persistToSession();

        $rules = $this->rules();

        foreach ($this->pharmacyInformation as $pharmacyId => $information) {
            $rules['pharmacyInformation.'.$pharmacyId.'.telematik_id'] = [
                'required',
                'string',
                'min:3',
                'max:32',
                ...PharmacyHelper::telematicsIdValidators($pharmacyId),
            ];
        }

        $this->validate($rules);

        foreach ($this->pharmacyInformation as $pharmacyId => $information) {

            try {
                $pharmacy = Pharmacy::findOrFail($pharmacyId);

                DB::beginTransaction();

                try {
                    $accountingCenter = AccountingCenter::findOrFail($information['accounting_center']);
                    $pharmacy->accounting_center_id = $accountingCenter->id;
                    $pharmacy->custom_accounting_center = null;
                } catch (ModelNotFoundException $e) {
                    $pharmacy->custom_accounting_center = $information['accounting_center'];
                    $pharmacy->accounting_center_id = null;
                }

                $pharmacy->fax = $information['fax'];
                $pharmacy->institute_id = $information['ik_number'];
                $pharmacy->save();

                $telematicsId = $pharmacy->telematicsId;
                if ($telematicsId === null) {
                    $telematicsId = new TelematicsId([
                        'pharmacy_id' => $pharmacy->id,
                    ]);
                }
                $telematicsId->setValues($information['telematik_id']);
                $telematicsId->save();

                DB::commit();
            } catch (\Throwable $e) {
                DB::rollBack();

                throw $e;
            }
        }

        $this->next();
    }

    public function sessionPrefix(): ?string
    {
        return SessionEnum::RETAX_ONBOARDING->value;
    }

    protected function nextStepLabel(): string
    {
        return 'Weiter';
    }

    /**
     * @return Collection<int, Pharmacy>
     */
    #[Computed]
    public function pharmacies(): Collection
    {
        $user = user();
        $user || abort(403);

        return $user->pharmacies;
    }

    #[Computed]
    public function accountingCenters(): Collection
    {
        return AccountingCenter::orderBy('company', 'asc')->get();
    }

    public function filteredAccountCenters(int $pharmacyId): Collection
    {
        $search = $this->pharmacyInformation[$pharmacyId]['search'] ?? '';

        $results = AccountingCenter::query()
            ->when(
                $search,
                fn ($query) => $query->where('company', 'like', '%'.$search.'%')
            )
            ->orderBy('company', 'asc')
            ->get();

        if ($search && $results->isEmpty()) {
            return collect([new AccountingCenter(['company' => $search])]);
        }

        return $results;
    }
}
