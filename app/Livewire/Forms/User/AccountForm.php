<?php

namespace App\Livewire\Forms\User;

use App\Rules\GedisaMail;
use App\User;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Livewire\Form;

class AccountForm extends Form
{
    public User $user;

    public ?string $name = null;

    public string $salutation;

    public ?string $title = null;

    public string $first_name;

    public string $last_name;

    public string $phone;

    public string $email;

    public ?string $email_confirmation = null;

    public bool $association_news_active = true;

    public function setUser(User $user): void
    {
        $this->user = $user;
        $this->fill($this->user->toArray());

        if ($this->user->isCompany() && $this->user->companyUser) {
            $this->name = $this->user->companyUser->name;
        }

        $this->email_confirmation = $this->user->email;
    }

    /**
     * @return array<string, array<string|GedisaMail>>
     */
    public function rules(): array
    {
        return [
            'name' => ['nullable', Rule::requiredIf($this->user->isCompany()), 'string', 'max:255'],
            'salutation' => ['required', 'string'],
            'title' => ['nullable', 'string', 'max:255'],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', app(GedisaMail::class)->allowApomail(), 'confirmed'],
            'phone' => ['required', 'string', 'max:255'],
            'association_news_active' => ['nullable', 'boolean'],
        ];
    }

    public function save(): User
    {
        $data = $this->validate();

        $data = Arr::except($data, ['email']);

        if ($this->email !== $this->user->email) {
            $id = Cache::get('emailChanges.email.'.$this->email);

            if ($id && $id !== $this->user->id) {
                throw ValidationException::withMessages(['email' => trans('validation.unique', ['attribute' => trans('messages.email')])]);
            }

            $this->user->sendEmailChangeEmail($this->email);
        }

        DB::transaction(function () use ($data) {
            $this->user->update($data);

            if ($this->user->companyUser) {
                $this->user->companyUser->update([
                    'name' => $this->name,
                ]);
            }
        });

        return $this->user->refresh();
    }
}
