<?php

namespace App\Livewire\Association\Vaccinate\Influenza;

use App\Exports\VaccinationsExport;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class ExportSelector extends Component
{
    public $start = null;

    public $end = null;

    public function render()
    {
        return view('livewire.association.vaccinate.influenza.export-selector');
    }

    public function downloadExportsCustomTime()
    {
        $this->validate([
            'start' => ['required', 'date'],
            'end' => ['required', 'date', 'after:start'],
        ]);

        return Excel::download(new VaccinationsExport(user()->associations->first(), $this->start, $this->end), 'impfungen_'.$this->start.'_'.$this->end.'.xlsx');
    }
}
