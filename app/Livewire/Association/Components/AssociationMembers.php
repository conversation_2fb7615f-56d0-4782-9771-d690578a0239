<?php

namespace App\Livewire\Association\Components;

use App\Association;
use App\Excel\Imports\AssociationMemberImport;
use App\Processes\CreateAssociationMembershipChange;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\File;
use Illuminate\Validation\ValidationException;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\HeadingRowImport;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Validators\Failure;
use Maatwebsite\Excel\Validators\ValidationException as ExcelValidationException;
use RuntimeException;

class AssociationMembers extends Component
{
    use WithFileUploads;

    public Association $association;

    public $search = '';

    public $sortBy = 'last_name';

    public $sortOrder = 'asc';

    public $file = null;

    public bool $modalOpen = false;

    public ?string $removeMemberDate = null;

    public ?User $userToRemove = null;

    public const MODAL_NAME = 'add-association-members';

    public const ALLOWED_FILE_TYPES = [
        '.xls' => 'application/vnd.ms-excel',
        '.xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    public function rules(): array
    {
        return [
            'association' => 'required',
            'modalOpen' => 'boolean',
        ];
    }

    public function updated(string $propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function changeSorting(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortOrder = $this->sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
        }
    }

    public function removeFile(): void
    {
        $this->file = null;
    }

    public function addMembers(): void
    {
        $hasErrors = $this->validateFile();

        if ($hasErrors) {
            return;
        }

        $path = $this->file->storeAs('temp', $this->file->getClientOriginalName(), 'local');

        $this->validateHeader($path);

        if ($this->getErrorBag()->count() > 0) {
            $this->showErrorsClearStorage($path);

            return;
        }

        $this->validateAndImportContent($path);

        if ($this->getErrorBag()->count() > 0) {
            return;
        }

        $this->modalOpen = false;
        $this->dispatch('close-modal');
        $this->openModal('successfully-imported');
    }

    public function validateHeader(string $path): void
    {
        HeadingRowFormatter::default('none');

        $this->validateFirstHeaderRow($path);
        $this->validateSecondHeaderRow($path);

        HeadingRowFormatter::default('slug');
    }

    public function validateFirstHeaderRow(string $path): void
    {
        $headingsFirstRow = Arr::flatten((new HeadingRowImport)->toArray($path));

        if (! Arr::has($headingsFirstRow, 7) || $headingsFirstRow[7] !== 'Adresse der Apotheke') {
            $this->addError('file', 'Der Spaltenname "Adresse der Apotheke" muss ab H1 vorhanden sein.');
        }
    }

    public function validateSecondHeaderRow(string $path): void
    {
        $headingsSecondRow = Arr::flatten((new HeadingRowImport(2))->toArray($path));

        if (! Arr::has($headingsSecondRow, 0) || $headingsSecondRow[0] !== 'Anrede (Auswahlfeld)') {
            $this->addError('file', 'Der Spaltenname "Anrede (Auswahlfeld)" muss in A2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 1) || $headingsSecondRow[1] !== 'Akadem. Grad') {
            $this->addError('file', 'Der Spaltenname "Akadem. Grad" muss in B2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 2) || $headingsSecondRow[2] !== 'Vorname Inhaber') {
            $this->addError('file', 'Der Spaltenname "Vorname Inhaber" muss in C2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 3) || $headingsSecondRow[3] !== 'Nachname Inhaber') {
            $this->addError('file', 'Der Spaltenname "Nachname Inhaber" muss in D2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 4) || $headingsSecondRow[4] !== 'E-Mail-Adresse Inhaber') {
            $this->addError('file', 'Der Spaltenname "E-Mail-Adresse Inhaber" muss in E2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 5) || $headingsSecondRow[5] !== 'OHG Name') {
            $this->addError('file', 'Der Spaltenname "OHG Name" muss in F2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 6) || $headingsSecondRow[6] !== 'Name der Apotheke') {
            $this->addError('file', 'Der Spaltenname "Name der Apotheke" muss in G2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 7) || $headingsSecondRow[7] !== 'Adresszusatz') {
            $this->addError('file', 'Der Spaltenname "Adresszusatz" muss in H2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 8) || $headingsSecondRow[8] !== 'Straße + Hausnr.') {
            $this->addError('file', 'Der Spaltenname "Straße + Hausnr." muss in I2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 9) || $headingsSecondRow[9] !== 'Postleitzahl') {
            $this->addError('file', 'Der Spaltenname "Postleitzahl" muss in J2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 10) || $headingsSecondRow[10] !== 'Ort') {
            $this->addError('file', 'Der Spaltenname "Ort" muss in K2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 11) || $headingsSecondRow[11] !== 'Datum (Format: TT.MM.YYYY)') {
            $this->addError('file', 'Der Spaltenname "Datum (Format: TT.MM.YYYY)" muss in L2 vorhanden sein.');
        }
    }

    public function validateAndImportContent(string $path): void
    {
        try {
            Log::channel('association-member-import')->info('Association Member excel import started', ['user' => user()?->uuid ?? user()?->id ?? 'N/A', 'file' => $path]);
            (new AssociationMemberImport($this->association->id))->import($path);
        } catch (ExcelValidationException $e) {
            $this->modalOpen = true;
            Log::channel('association-member-import')->info('Association Member excel import failed validation', ['reason' => $e->getMessage(), 'user' => user()?->uuid ?? user()?->id ?? 'N/A', 'file' => $path]);
            $this->setErrorMessages($e);
        } finally {
            $this->deleteFileFromStorage($path);
        }

        Log::channel('association-member-import')->info('Association Member excel import finished', ['user' => user()?->uuid ?? user()?->id ?? 'N/A', 'file' => $path]);
    }

    public function deleteFileFromStorage(string $path): void
    {
        if (isset($path)) {
            Storage::delete($path);
        }
    }

    private function validateFile(): bool
    {
        try {
            Validator::make(['file' => $this->file], [
                'file' => ['required', File::types(array_values(self::ALLOWED_FILE_TYPES))],
            ], [
                'file.required' => 'Sie müssen mindestens eine Datei hochladen.',
            ])->validate();
        } catch (ValidationException $e) {
            $this->modalOpen = true;
            $this->addError('file', $e->errors()['file'][0]);

            return true;
        }

        return false;
    }

    private function setErrorMessages(ExcelValidationException $e): void
    {
        collect($e->failures())->each(function (Failure $failure) {
            collect($failure->errors())->each(function (string $error) use ($failure) {
                $this->addError(
                    'file',
                    'Zeile '.$failure->row().': '.$error
                );
            });
        });
    }

    private function showErrorsClearStorage(string $path): void
    {
        $this->modalOpen = true;
        $this->deleteFileFromStorage($path);
    }

    /**
     * @param  User  $member
     *
     * we allow cancelling orders,
     * but we do not allow cancelling already invoiced orders because we don't know what to do then
     */
    public function getRemoveMemberDateOptions(User $member): Collection
    {
        $options = collect();

        $period = CarbonPeriod::create(
            $member->getLastPossibleDateChangingAssociationRetroactively()->format('Y-m'),
            '1 month',
            Carbon::now()->addYearNoOverflow()
        );

        foreach ($period as $dt) {
            $options->put($dt->format('Y-m'), __('months.'.$dt->format('n')).' '.$dt->format('Y'));
        }

        return $options->sortKeys();
    }

    public function removeAssociationMember(string $memberId): void
    {
        $success = false;

        $this->validateOnly('removeMemberDate', [
            'removeMemberDate' => ['required'],
        ], [
            'removeMemberDate.required' => 'Das Austrittsdatum muss ausgefüllt werden.',
        ]);

        try {
            if ($this->userToRemove === null || $this->userToRemove->id !== (int) $memberId) {
                throw new RuntimeException('No user selected to remove.');
            }

            if (empty($this->removeMemberDate)) {
                throw new RuntimeException('No remove date selected.');
            }

            $payload = new CreateAssociationMembershipChangePayload(
                user: $this->userToRemove,
                changeAt: Carbon::parse($this->removeMemberDate)->endOfMonth(),
                old: $this->userToRemove->pharmacyProfile?->association_id,
            );
            $process = new CreateAssociationMembershipChange;
            $success = (bool) $process->run($payload);
        } catch (\Throwable $exception) {
            report($exception);
        }

        $this->userToRemove = null;
        $this->dispatch('close-modal');
        $this->openModal($success ? 'successfully-deleted-association-member' : 'failed-to-delete-association-member');
    }

    public function userCanBeEdited(User $member): bool
    {
        return false;

        /**
         * TODO: Redo this process
         * AP-2406 disable change association membership
        return $member->currentAssociationMembershipChange === null
            || $member->currentAssociationMembershipChange->canceled_at !== null;
        */
    }

    public function openRemoveAssociationMemberModal(string $memberId): bool
    {
        if (empty($memberId)) {
            return false;
        }

        $this->userToRemove = User::find($memberId);
        $this->removeMemberDate = now()->format('Y-m');

        if ($this->userToRemove === null) {
            return false;
        }

        $this->openModal('delete-association-member');

        return true;
    }

    public function setSelectedRemoveMemberDate(): bool
    {
        return now()->format('Y-m');
    }

    public function render()
    {
        return view('livewire.association.components.members', [
            'members' => $this->association->members()
                ->when(
                    strlen($this->search) > 0,
                    fn ($query) => $query->where(function (Builder $whereQuery) { // @phpstan-ignore-line
                        return $whereQuery->where('email', 'like', '%'.$this->search.'%')
                            ->orWhere('title', 'like', '%'.$this->search.'%')
                            ->orWhere('first_name', 'like', '%'.$this->search.'%')
                            ->orWhere('last_name', 'like', '%'.$this->search.'%');
                    })
                )
                ->orderBy($this->sortBy, $this->sortOrder)
                ->get(),
            'modalName' => self::MODAL_NAME,
            'acceptedFileTypes' => "['".implode("', '", array_values(self::ALLOWED_FILE_TYPES))."']",
            'acceptedFileExtensions' => implode(', ', array_keys(self::ALLOWED_FILE_TYPES)),
        ]);
    }
}
