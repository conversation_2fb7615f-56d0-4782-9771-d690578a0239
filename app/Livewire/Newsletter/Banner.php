<?php

namespace App\Livewire\Newsletter;

use App\Enums\Settings\UserSettingTypes;
use App\Settings\MailcoachSettings;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\View\View;
use Livewire\Component;

class Banner extends Component
{
    public User $user;

    public function mount(): void
    {
        assert(user() instanceof User);

        $this->user = user();
    }

    public function canClose(): bool
    {
        $settings = app(MailcoachSettings::class);

        if (Carbon::parse($settings->closeFrom)->isPast()) {
            return true;
        }

        return $this->user->created_at?->addMonth()->isPast() ?? false;
    }

    public function close(): Redirector|RedirectResponse
    {
        $this->user->setGeneralSetting(UserSettingTypes::CLOSE_NEWSLETTER_BANNER, true);

        return redirect()->route('dashboard');
    }

    public function render(): View
    {
        return view('livewire.newsletter.banner');
    }
}
