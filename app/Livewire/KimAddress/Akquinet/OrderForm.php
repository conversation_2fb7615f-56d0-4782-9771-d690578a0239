<?php

namespace App\Livewire\KimAddress\Akquinet;

use App\Actions\Kim\CreateNewKimAddress;
use App\Actions\Kim\CreateNewKimAddressAtAkquinet;
use App\Enums\KimVendorEnum;
use App\Exceptions\Kim\ApiException;
use App\Helper\KimAddressHelper;
use App\KimAddress;
use App\Pharmacy;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Livewire\Component;

class OrderForm extends Component
{
    use AuthorizesRequests;

    public Pharmacy $pharmacy;

    public KimAddress $kimAddress;

    public array $state = [];

    public bool $apiError = false;

    public function mount(Pharmacy $pharmacy, KimAddress $kimAddress = new <PERSON>Address): void
    {
        $this->pharmacy = $pharmacy;
        $this->kimAddress = $kimAddress;

        $email = null;
        if ($kimAddress->exists) {
            $email = \Str::before($kimAddress->email, '@').'@'.KimAddressHelper::getKimAddressDomain($kimAddress->pharmacy);
        }

        $this->state = [
            'email' => $email,
            'tosCheckboxAccepted' => false,
        ];
    }

    /**
     * @return array<string, array<string>>
     */
    public function rules(): array
    {
        return [
            'state.email' => ['required', 'min:3', 'regex:/^[a-zA-Z0-9._-]+$/'],
        ];
    }

    /**
     * @return array<string, array<string, string>>
     */
    public function messages(): array
    {
        return [
            'state.email' => [
                'min' => 'Die angegebene KIM-Adresse ist zu kurz, bitte geben Sie mindestens 3 Zeichen ein.',
                'regex' => 'Die angegebene KIM-Adresse enthält ungültige Zeichen. Umlaute sind nicht erlaubt. Bitte verwenden Sie nur Buchstaben, Zahlen und die Sonderzeichen . - _',
            ],
        ];
    }

    public function submit()
    {
        $this->resetErrorBag();

        $this->validate();

        if ($this->pharmacy->activeCancellation()) {
            throw new \Exception('Aufgrund einer ausstehenden Kündigung können keine neuen Features aktiviert werden.');
        }

        if (KimAddressHelper::getVendor($this->pharmacy) !== KimVendorEnum::AKQUINET->value) {
            throw new \Exception('Kim address vendor is not valid');
        }

        if ($this->kimAddress->exists) {
            $this->state['email'] = \Str::before($this->kimAddress->email, '@').'@'.KimAddressHelper::getKimAddressDomain($this->kimAddress->pharmacy);
        }

        try {
            if ($this->kimAddress->exists) {
                app(CreateNewKimAddressAtAkquinet::class)->execute($this->state, $this->kimAddress);
            } else {
                $this->kimAddress = app(CreateNewKimAddress::class)->execute($this->state, $this->pharmacy);
            }
        } catch (ApiException $exception) {
            report($exception);
            $this->apiError = true;

            return false;
        }

        notify('Die KIM-Adresse wurde erfolgreich erstellt.');

        return redirect()->route('kims.show', [$this->pharmacy, $this->kimAddress])->with('kim_created_successfully', KimAddressHelper::bookingAvailable($this->pharmacy) ? 'ordered' : 'reserved');
    }

    public function render()
    {
        return view('livewire.kim-address.akquinet.order-form');
    }
}
