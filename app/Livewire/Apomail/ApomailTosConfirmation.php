<?php

namespace App\Livewire\Apomail;

use App\Apomail;
use App\Enums\Settings\PharmacySettingTypes;
use App\Pharmacy;
use App\Setting;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Gate;
use Livewire\Component;

class ApomailTosConfirmation extends Component
{
    public bool $tos = false;

    public bool $avv = false;

    public Collection $pharmacies;

    protected $messages = [
        'tos' => 'Bitte bestätigen Sie die Nutzungsbedingungen.',
        'avv' => 'Bitte bestätigen Sie den Auftragsverarbeitungsvertrag.',
    ];

    public function mount(): void
    {
        $this->pharmacies = user()->pharmacies()->acceptedTermsOfUseBeforeApomail()->get();
    }

    public function submit()
    {
        Gate::authorize('updateTos', Setting::class);
        Gate::authorize('updateTos', Apomail::class);

        $this->resetErrorBag();

        $this->validate([
            'tos' => 'accepted',
            'avv' => 'accepted',
        ]);

        $this->pharmacies->each(function (Pharmacy $pharmacy) {
            if (! $pharmacy->hasAcceptedTermsAfterApomail()) {
                $pharmacy->setGeneralSetting(PharmacySettingTypes::TERMS_OF_USE, PharmacySettingTypes::TERMS_OF_USE_ACCEPTED);
                $pharmacy->setGeneralSetting(PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT, PharmacySettingTypes::ORDER_DATA_PROCESSING_CONTRACT_ACCEPTED);
            }

            if (! $pharmacy->uses_apomail) {
                $pharmacy->uses_apomail = true;
                $pharmacy->save();
            }
        });

        return redirect()->route('apomails');
    }

    public function render(): Factory|View|Application
    {
        return view('livewire.apomail.apomail-tos-confirmation');
    }
}
