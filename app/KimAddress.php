<?php

namespace App;

use App\Enums\KimAddressReportStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property array<string, string>|null $additional
 *
 * @mixin IdeHelperKimAddress
 *
 * @property int $id
 * @property string $email
 * @property string|null $order_number
 * @property string|null $vendor
 * @property string $status
 * @property KimAddressReportStatus|null $report_status
 * @property int $notification_count
 * @property \Illuminate\Support\Carbon|null $ngda_notify_at
 * @property int|null $pharmacy_id
 * @property int|null $billing_address_id
 * @property string|null $cancellation_id
 * @property string|null $cancellation_reason
 * @property string|null $deletion_reason
 * @property array<array-key, mixed>|null $log
 * @property string|null $before_move_kim_state
 * @property \Illuminate\Support\Carbon|null $reserved_at
 * @property \Illuminate\Support\Carbon|null $ordered_at
 * @property \Illuminate\Support\Carbon|null $activated_at
 * @property \Illuminate\Support\Carbon|null $deactivated_at
 * @property \Illuminate\Support\Carbon|null $reported_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $canceled_at
 * @property-read \App\BillingAddress|null $billingAddress
 * @property-read \App\Pharmacy|null $pharmacy
 *
 * @method static \Database\Factories\KimAddressFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereActivatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereAdditional($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereBeforeMoveKimState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereBillingAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereCanceledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereCancellationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereCancellationReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereDeactivatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereDeletionReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereLog($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereNgdaNotifyAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereNotificationCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereOrderNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereOrderedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereReportStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereReportedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereReservedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress whereVendor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KimAddress withoutTrashed()
 *
 * @mixin \Eloquent
 */
class KimAddress extends Model
{
    use HasFactory, SoftDeletes;

    public const SOFT_DELETED_SUFFIX = '_deleted';

    protected $guarded = [];

    protected $casts = [
        'report_status' => KimAddressReportStatus::class,
        'additional' => 'array',
        'ordered_at' => 'datetime',
        'activated_at' => 'datetime',
        'reserved_at' => 'datetime',
        'deactivated_at' => 'datetime',
        'reported_at' => 'datetime',
        'ngda_notify_at' => 'datetime',
        'log' => 'array',
        'canceled_at' => 'datetime',
    ];

    public function owner(): User
    {
        return $this->pharmacy->owner();
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class, 'pharmacy_id');
    }

    public function billingAddress(): BelongsTo
    {
        return $this->belongsTo(BillingAddress::class, 'billing_address_id');
    }

    /**
     * @param  array<string, mixed>  $value
     */
    public function log(array $value): void
    {
        $this->update([
            'log' => $value,
        ]);
    }

    /**
     * @param  array<string, mixed>  $value
     */
    public function setLogAttribute(array $value): void
    {
        $this->attributes['log'] = json_encode(array_merge($this->log ?? [], $value));
    }
}
