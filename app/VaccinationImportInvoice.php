<?php

namespace App;

use App\Enums\VaccinationImport\InvoiceStatusEnum;
use App\Mail\VaccinationImportAccountingFinishedMail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property int|null $user_id
 * @property int $month
 * @property int $year
 * @property string $status
 * @property int|null $price
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $from_support
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \Spatie\MediaLibrary\MediaCollections\Models\Media> $media
 * @property-read int|null $media_count
 * @property-read \App\Pharmacy $pharmacy
 * @property-read \App\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereFromSupport($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereMonth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VaccinationImportInvoice whereYear($value)
 *
 * @mixin \Eloquent
 */
class VaccinationImportInvoice extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $guarded = [];

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('invoice')
            ->singleFile()
            ->useDisk('vaccination-import-invocies');
    }

    public function finished(bool $notify = true)
    {
        $this->update([
            'status' => InvoiceStatusEnum::GENERATED,
        ]);

        if ($notify) {
            if ($this->user) {
                $user = $this->user;
            } else {
                $user = $this->pharmacy->owner();
            }

            Mail::to($user->routeEmailsTo)->send(new VaccinationImportAccountingFinishedMail($user, $this));
        }
    }

    public function isFinished()
    {
        return $this->status == InvoiceStatusEnum::GENERATED;
    }
}
