<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PublicTransportStation
 *
 * @mixin IdeHelperPublicTransportStation
 *
 * @property int $id
 * @property int $pharmacy_id
 * @property int $type
 * @property string $stop
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Pharmacy|null $pharmacy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation whereStop($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PublicTransportStation whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PublicTransportStation extends Model
{
    protected $touches = ['pharmacy'];

    public const STOPS = [
        'TRAINS' => [
            'id' => 1,
            'key' => 'trains',
        ], // includes metro, s-train and train
        'TRAM' => [
            'id' => 2,
            'key' => 'tram',
        ],
        'BUS' => [
            'id' => 3,
            'key' => 'bus',
        ],
    ];

    protected $guarded = [];

    public static function getStopOptions()
    {
        return [
            1 => 'trains',
            2 => 'tram',
            3 => 'bus',
        ];
    }

    public function getStopKey()
    {
        return self::getStopOptions()[$this->type];
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
