<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $ps_id
 * @property string|null $pzn_number
 * @property string|null $pharmaceutical
 * @property string|null $inhalation_system
 * @property string|null $inhalation_system_other_text
 * @property array<array-key, mixed>|null $device_condition
 * @property string|null $device_condition_optional_text
 * @property array<array-key, mixed>|null $preparation
 * @property string|null $preparation_optional_text
 * @property array<array-key, mixed>|null $inhalation
 * @property string|null $inhalation_optional_text
 * @property array<array-key, mixed>|null $ending
 * @property string|null $ending_optional_text
 * @property string|null $others
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\PharmaceuticalService $pharmaceuticalService
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereDeviceCondition($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereDeviceConditionOptionalText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereEnding($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereEndingOptionalText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereInhalation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereInhalationOptionalText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereInhalationSystem($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereInhalationSystemOtherText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereOthers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient wherePharmaceutical($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient wherePreparation($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient wherePreparationOptionalText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient wherePsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient wherePznNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InhalationTechniquePatient whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class InhalationTechniquePatient extends Model
{
    use HasFactory;

    protected $primaryKey = 'ps_id';

    public $incrementing = false;

    protected $casts = [
        'device_condition' => 'array',
        'preparation' => 'array',
        'inhalation' => 'array',
        'ending' => 'array',
    ];

    protected $guarded = [];

    public function pharmaceuticalService(): BelongsTo
    {
        return $this->belongsTo(PharmaceuticalService::class, 'ps_id', 'id');
    }
}
