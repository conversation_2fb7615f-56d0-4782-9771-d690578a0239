<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InhalationTechniquePatient extends Model
{
    use HasFactory;

    protected $primaryKey = 'ps_id';

    public $incrementing = false;

    protected $casts = [
        'device_condition' => 'array',
        'preparation' => 'array',
        'inhalation' => 'array',
        'ending' => 'array',
    ];

    protected $guarded = [];

    public function pharmaceuticalService(): BelongsTo
    {
        return $this->belongsTo(PharmaceuticalService::class, 'ps_id', 'id');
    }
}
