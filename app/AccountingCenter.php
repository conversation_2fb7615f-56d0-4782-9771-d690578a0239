<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperAccountingCenter
 *
 * @property int $id
 * @property string|null $company
 * @property string|null $system_id
 * @property string|null $tag
 * @property string|null $city
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Pharmacy> $pharmacies
 * @property-read int|null $pharmacies_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter whereSystemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter whereTag($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AccountingCenter whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class AccountingCenter extends Model
{
    protected $fillable = [
        'company',
    ];

    public function pharmacies()
    {
        return $this->hasMany(Pharmacy::class);
    }
}
