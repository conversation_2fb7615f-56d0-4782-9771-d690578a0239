<?php

namespace App\Observers;

use App\Actions\DocSpaces\DeleteRiseSDRDocSpaceAction;
use App\Actions\DocSpaces\GetRiseSDRDocSpaceGroupsAction;
use App\Actions\DocSpaces\RemoveRiseSDRGroupFromDocSpaceAction;
use App\DocSpace;
use Throwable;

class DocSpaceObserver
{
    public function created(DocSpace $docSpace): void
    {
        //
    }

    public function updated(DocSpace $docSpace): void
    {
        //
    }

    /**
     * @throws Throwable
     */
    public function deleting(DocSpace $docSpace): void
    {
        // docSpaces can only be deleted if no groups are assigned
        $getRiseSDRDocSpaceGroupsAction = app()->make(GetRiseSDRDocSpaceGroupsAction::class, [
            'sdrDocSpaceId' => $docSpace->sdr_doc_space_id,
        ])->execute();
        foreach ($getRiseSDRDocSpaceGroupsAction->results as $docSpaceGroupAssignment) {
            // remove `default-owner-group` wich only exists in Rise SDR
            // all other groups must be deleted explicitly
            if ($docSpaceGroupAssignment['groupName'] === 'default-owner-group') {
                app(RemoveRiseSDRGroupFromDocSpaceAction::class)
                    ->setSDRDocSpaceId($docSpace->sdr_doc_space_id)
                    ->setSDRGroupId($docSpaceGroupAssignment['groupId'])
                    ->execute();
            }
        }

        app(DeleteRiseSDRDocSpaceAction::class)
            ->setDocSpaceId($docSpace->sdr_doc_space_id)
            ->execute();
    }

    public function deleted(DocSpace $docSpace): void
    {
        //
    }

    public function restored(DocSpace $docSpace): void
    {
        //
    }

    public function forceDeleted(DocSpace $docSpace): void
    {
        //
    }
}
