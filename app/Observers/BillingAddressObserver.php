<?php

namespace App\Observers;

use App\BillingAddress;
use App\Pharmacy;

class BillingAddressObserver
{
    public static function updating(BillingAddress $billingAddress): void
    {
        $billingAddress->pharmacies->each(
            static function (Pharmacy $pharmacy) use ($billingAddress) {
                /** @phpstan-ignore-next-line  At this point in the runtime the billingAddress is still the non updated version.
                 * That is why we need to update the property directly here, else UpdateOrganization will be using the old data.
                 * BillingAddressObserverTest::test_billing_address_update_triggers_organization_update insures this behaviour.
                 */
                $pharmacy->billingAddress = $billingAddress;

                if ($pharmacy->stripeId()) {
                    $pharmacy->syncStripeCustomerDetails();
                }
            }
        );
    }
}
