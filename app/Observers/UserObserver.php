<?php

namespace App\Observers;

use App\Actions\TokenService\AddUsersToTokenServiceAction;
use App\Actions\TokenService\DeleteUserFromTokenServiceAction;
use App\Actions\TokenService\SynchronizeUserPermissionsInTokenServiceAction;
use App\Actions\Users\DeleteAssociationUser;
use App\Actions\Users\DeletePharmacyEmployee;
use App\Actions\Users\DeletePharmacyOwner;
use App\Jobs\DeleteMailcoachUser;
use App\Jobs\DoBetaTestChangesJob;
use App\Jobs\UpdateOrCreateMailcoachUser;
use App\ShiftPlanGroupUser;
use App\User;
use Exception;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserObserver
{
    /**
     * Handle the user "created" event.
     */
    public function created(User $user): void
    {
        if (user() && $user->email) {
            $user->email_verified_at = now();
            $user->save();

            UpdateOrCreateMailcoachUser::dispatch($user)->delay(30);
        }

        if (! $user->famedly_uuid) {
            $user->famedly_uuid = Str::uuid()->toString();
            $user->save();
        }

        app(AddUsersToTokenServiceAction::class)->execute($user);
        app(SynchronizeUserPermissionsInTokenServiceAction::class)->execute($user);
    }

    public function creating(User $user): void
    {
        if (is_null($user->password)) {
            $user->password = Hash::make(uniqid());
        }
    }

    /**
     * Handle the user "updated" event.
     */
    public function updated(User $user): void
    {
        if ($user->emailVerified() && ! $user->wasChanged('mailcoach_id') && $user->email) {
            UpdateOrCreateMailcoachUser::dispatch($user)->delay(2);
        }

        if (! $user->email && $user->wasChanged('email') && $user->mailcoach_id) {
            DeleteMailcoachUser::dispatch($user)->delay(5);
        }

        if ($user->wasChanged('is_beta_tester')) {
            DoBetaTestChangesJob::dispatch($user)->delay(2);
        }
    }

    /**
     * Handle the user "deleted" event.
     */
    public function deleted(User $user): void
    {
        app(DeleteUserFromTokenServiceAction::class)->execute($user);
        $user->pharmacies()->searchable();
    }

    /**
     * @throws Exception
     */
    public function deleting(User $user): void
    {
        app(SynchronizeUserPermissionsInTokenServiceAction::class)->execute($user);

        if ($user->isPharmacyUser()) {
            if ($user->isOwner()) {
                //                foreach ($user->pharmacies as $pharmacy) {
                //                    $pharmacy->docSpaces()->delete();
                //                    $pharmacy->docSpaceGroups()->delete();
                //                }

                app(DeletePharmacyOwner::class)->execute($user);
            } else {
                foreach ($user->pharmacies as $pharmacy) {
                    foreach ($pharmacy->docSpaceGroups as $docSpaceGroup) {
                        if ($docSpaceGroup->users->contains($user)) {
                            $docSpaceGroup->users()->detach($user);
                        }
                    }
                }

                app(DeletePharmacyEmployee::class)->execute($user);
            }
        } elseif ($user->isAssociationUser()) {
            app(DeleteAssociationUser::class)->execute($user);
        } else {
            throw new Exception('Unknown user type of user '.$user->id, 500); // Should not be possible
        }

        if ($user->mailcoach_id) {
            DeleteMailcoachUser::dispatch($user)->delay(5);
        }

        // Related Shifts are deleted automatically by using the onDelete('cascade') constraint in the migration
        ShiftPlanGroupUser::query()->where('user_id', $user->id)->delete();
    }

    /**
     * Handle the user "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the user "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }
}
