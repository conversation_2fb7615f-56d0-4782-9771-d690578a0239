<?php

namespace App;

use App\Enums\BannerPosition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property string $title
 * @property string $content
 * @property BannerPosition $position
 * @property \Illuminate\Support\Carbon|null $starts_at
 * @property \Illuminate\Support\Carbon|null $ends_at
 * @property int $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static Builder<static>|Banner active()
 * @method static Builder<static>|Banner bottom()
 * @method static \Database\Factories\BannerFactory factory($count = null, $state = [])
 * @method static Builder<static>|Banner newModelQuery()
 * @method static Builder<static>|Banner newQuery()
 * @method static Builder<static>|Banner published()
 * @method static Builder<static>|Banner query()
 * @method static Builder<static>|Banner top()
 * @method static Builder<static>|Banner whereActive($value)
 * @method static Builder<static>|Banner whereContent($value)
 * @method static Builder<static>|Banner whereCreatedAt($value)
 * @method static Builder<static>|Banner whereEndsAt($value)
 * @method static Builder<static>|Banner whereId($value)
 * @method static Builder<static>|Banner wherePosition($value)
 * @method static Builder<static>|Banner whereStartsAt($value)
 * @method static Builder<static>|Banner whereTitle($value)
 * @method static Builder<static>|Banner whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Banner extends Model
{
    use HasFactory;

    protected $guarded = [
        'id',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'activated_at' => 'datetime',
        'position' => BannerPosition::class,
    ];

    public function scopeActive(Builder $query): void
    {
        $query->where('active', true);
    }

    public function scopePublished(Builder $query): void
    {
        $now = now('Europe/Berlin');

        $query
            ->where(
                fn (Builder $query) => $query->whereNull('starts_at')->orWhere('starts_at', '<=', $now)
            )
            ->where(
                fn (Builder $query) => $query->whereNull('ends_at')->orWhere('ends_at', '>=', $now)
            );
    }

    public function scopeBottom(Builder $query): void
    {
        $query->where('position', BannerPosition::BOTTOM);
    }

    public function scopeTop(Builder $query): void
    {
        $query->where('position', BannerPosition::TOP);
    }
}
