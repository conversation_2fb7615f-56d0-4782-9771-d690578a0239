<?php

namespace App\Features;

use App\Contracts\PennantFeature;
use App\Settings\IaSettings;

class IhreApotheken implements PennantFeature
{
    public function resolve(mixed $scope): bool
    {
        return true;
    }

    public function before(mixed $scope): ?bool
    {
        if (now()->isBefore(app(IaSettings::class)->enabledAt)) {
            return false;
        }

        return null;
    }
}
