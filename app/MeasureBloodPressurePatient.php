<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MeasureBloodPressurePatient extends Model
{
    use HasFactory;

    protected $primaryKey = 'ps_id';

    public $incrementing = false;

    protected $casts = [
        'patient_has_illnesses' => 'array',
        'recommended_solutions' => 'array',
    ];

    protected $guarded = [];

    public function pharmaceuticalService(): BelongsTo
    {
        return $this->belongsTo(PharmaceuticalService::class, 'ps_id', 'id');
    }
}
