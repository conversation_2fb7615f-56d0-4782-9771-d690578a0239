<?php

namespace App;

use App\Enums\InvoiceStatusEnum;
use App\Support\Payment\PaymentApi;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Carbon;
use InvalidArgumentException;

/**
 * @deprecated
 *
 * @property int $id
 * @property string $transaction_id
 * @property string $order_id
 * @property string $invoice_id
 * @property mixed|null $status_code
 * @property int $billing_address_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $resent_as_invoice_id
 * @property-read \App\BillingAddress $billingAddress
 * @property-read Invoice|null $canceledInvoice
 * @property-read \Illuminate\Support\Carbon|null $completed_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Pharmacy> $pharmacies
 * @property-read int|null $pharmacies_count
 * @property-read \App\User|null $recipient
 * @property-read Invoice|null $resentInvoice
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\SubscriptionOrder> $subscriptionOrders
 * @property-read int|null $subscription_orders_count
 *
 * @method static \Database\Factories\InvoiceFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereBillingAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereResentAsInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereStatusCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereTransactionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Invoice whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Invoice extends Model
{
    use HasFactory;

    protected $guarded = ['order_id', 'invoice_id'];

    /**
     * @deprecated
     */
    protected function statusCode(): Attribute
    {
        // SQLite allows us to store strings in an integer column, so we need to validate the value.
        return Attribute::make(
            set: static function (mixed $value) {
                throw_unless(is_int($value), new InvalidArgumentException('Status code must be an integer.'));

                return $value;
            }
        );
    }

    public static function booted(): void
    {
        static::creating(static function (Invoice $invoice) {
            $invoice->order_id = $invoice->getOrderIdAttribute();
            $invoice->invoice_id = $invoice->getInvoiceIdAttribute();
        });
    }

    /**
     * @deprecated
     */
    public function getOrderIdAttribute(): string
    {
        return $this->attributes['order_id'] ?? $this->generateBillingId('B');
    }

    /**
     * @deprecated
     */
    public function getInvoiceIdAttribute(): string
    {
        return $this->attributes['invoice_id'] ?? $this->generateBillingId('R');
    }

    /**
     * @deprecated
     */
    public function getCompletedAtAttribute(): ?Carbon
    {
        return $this->status_code === InvoiceStatusEnum::COMPLETED->value ? $this->updated_at : null;
    }

    /**
     * @deprecated
     */
    private function generateBillingId(string $prefix): string
    {
        $paymentApi = resolve(PaymentApi::class);

        return $prefix.'-'.sprintf('%05d', self::all()->count() + 1).'-'.now()->year.'-'.$paymentApi->billingAddressIdSuffix();
    }

    /**
     * @deprecated
     */
    public function pharmacies(): HasManyThrough
    {
        return $this->hasManyThrough(
            Pharmacy::class,
            SubscriptionOrder::class,
            'invoice_id',
            'id',
            'invoice_id',
            'orderable_id'
        );
    }

    /**
     * @deprecated
     */
    public function recipient(): HasOneThrough
    {
        return $this->hasOneThrough(
            User::class,
            BillingAddress::class,
            'id',
            'id',
            'billing_address_id',
            'user_id'
        );
    }

    /**
     * @deprecated
     */
    public function subscriptionOrders(): HasMany
    {
        return $this->hasMany(SubscriptionOrder::class);
    }

    /**
     * @deprecated
     */
    public function billingAddress(): BelongsTo
    {
        return $this->belongsTo(BillingAddress::class);
    }

    /**
     * @deprecated
     */
    public function resentInvoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'resent_as_invoice_id');
    }

    /**
     * @deprecated
     */
    public function canceledInvoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'id', 'resent_as_invoice_id');
    }

    /**
     * @deprecated
     */
    public function isCanceled(): bool
    {
        return $this->status_code === InvoiceStatusEnum::CANCELED->value;
    }
}
