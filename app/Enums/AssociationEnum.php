<?php

namespace App\Enums;

class AssociationEnum
{
    public const APOTHEKERVERBAND_BRANDENBURG_E_V = 1;

    public const APOTHEKERVERBAND_MECKLENBURG_VORPOMMERN_E_V = 2;

    public const APOTHEKERVERBAND_NORDRHEIN_E_V = 3;

    public const APOTHEKERVERBAND_RHEINLAND_PFALZ_E_V = 4;

    public const APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V = 5;

    public const APOTHEKERVERBAND_SCHLESWIG_HOLSTEIN_E_V = 6;

    public const BAYERISCHER_APOTHEKERVERBAND_E_V = 7;

    public const BERLINER_APOTHEKER_VEREIN = 8;

    public const BREMER_APOTHEKERVERBAND_E_V = 9;

    public const HAMBURGER_APOTHEKERVERBAND_E_V = 10;

    public const HESSISCHER_APOTHEKERVERBAND_E_V = 11;

    public const LANDESAPOTHEKERVERBAND_BADEN_WUERTTEMBERG_E_V = 12;

    public const LANDESAPOTHEKERVERBAND_NIEDERSACHSEN_E_V = 13;

    public const APOTHEKERVERBAND_SACHSEN_ANHALT_E_V = 14;

    public const SAARLAENDISCHER_APOTHEKERVEREIN_E_V = 15;

    public const SAECHSISCHER_APOTHEKERVERBAND_E_V = 16;

    public const THUERINGER_APOTHEKERVERBAND_E_V = 17;

    public const TESTVERBAND = 18;

    public const APOTHEKERVERBAND_TREUTELVIEW = 19;

    public const APOTHEKERVERBAND_KOEPPSIDE = 20;

    /**
     * @return array<int, string>
     */
    public static function getLabels(): array
    {
        return [
            self::APOTHEKERVERBAND_BRANDENBURG_E_V => 'Apothekerverband Brandenburg e.V.',
            self::APOTHEKERVERBAND_KOEPPSIDE => 'Apothekenverband Koeppside',
            self::APOTHEKERVERBAND_MECKLENBURG_VORPOMMERN_E_V => 'Apothekerverband Mecklenburg-Vorpommern e.V.',
            self::APOTHEKERVERBAND_NORDRHEIN_E_V => 'Apothekerverband Nordrhein e.V.',
            self::APOTHEKERVERBAND_RHEINLAND_PFALZ_E_V => 'Apothekerverband Rheinland-Pfalz e.V.',
            self::APOTHEKERVERBAND_SCHLESWIG_HOLSTEIN_E_V => 'Apothekerverband Schleswig-Holstein e.V.',
            self::APOTHEKERVERBAND_TREUTELVIEW => 'Apothekenverband Treutelview',
            self::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V => 'Apothekerverband Westfalen-Lippe e.V.',
            self::BAYERISCHER_APOTHEKERVERBAND_E_V => 'Bayerischer Apothekerverband e.V.',
            self::BERLINER_APOTHEKER_VEREIN => 'Berliner Apotheker-Verein',
            self::BREMER_APOTHEKERVERBAND_E_V => 'Bremer Apothekerverband e.V.',
            self::HAMBURGER_APOTHEKERVERBAND_E_V => 'Hamburger Apothekerverein e.V.',
            self::HESSISCHER_APOTHEKERVERBAND_E_V => 'Hessischer Apothekerverband e.V.',
            self::LANDESAPOTHEKERVERBAND_BADEN_WUERTTEMBERG_E_V => 'Landesapothekerverband Baden-Württemberg e.V.',
            self::LANDESAPOTHEKERVERBAND_NIEDERSACHSEN_E_V => 'Landesapothekerverband Niedersachsen e.V.',
            self::APOTHEKERVERBAND_SACHSEN_ANHALT_E_V => 'Landesapothekerverband Sachsen-Anhalt e.V.',
            self::SAARLAENDISCHER_APOTHEKERVEREIN_E_V => 'Saarländischer Apothekerverein e.V.',
            self::SAECHSISCHER_APOTHEKERVERBAND_E_V => 'Sächsischer Apothekerverband e.V.',
            self::THUERINGER_APOTHEKERVERBAND_E_V => 'Thüringer Apothekerverband e.V.',
            self::TESTVERBAND => 'Testverband',
        ];
    }

    public static function abbreviation(int $id): ?string
    {
        return match ($id) {
            self::APOTHEKERVERBAND_BRANDENBURG_E_V => 'BB',
            self::APOTHEKERVERBAND_MECKLENBURG_VORPOMMERN_E_V => 'MV',
            self::APOTHEKERVERBAND_NORDRHEIN_E_V => 'NW',
            self::APOTHEKERVERBAND_RHEINLAND_PFALZ_E_V => 'RP',
            self::APOTHEKERVERBAND_SCHLESWIG_HOLSTEIN_E_V => 'SH',
            self::APOTHEKERVERBAND_WESTFALEN_LIPPE_E_V => 'WL',
            self::BAYERISCHER_APOTHEKERVERBAND_E_V => 'BY',
            self::BERLINER_APOTHEKER_VEREIN => 'BE',
            self::BREMER_APOTHEKERVERBAND_E_V => 'HB',
            self::HAMBURGER_APOTHEKERVERBAND_E_V => 'HH',
            self::HESSISCHER_APOTHEKERVERBAND_E_V => 'HE',
            self::LANDESAPOTHEKERVERBAND_BADEN_WUERTTEMBERG_E_V => 'BW',
            self::LANDESAPOTHEKERVERBAND_NIEDERSACHSEN_E_V => 'NI',
            self::APOTHEKERVERBAND_SACHSEN_ANHALT_E_V => 'ST',
            self::SAARLAENDISCHER_APOTHEKERVEREIN_E_V => 'SL',
            self::SAECHSISCHER_APOTHEKERVERBAND_E_V => 'SN',
            self::THUERINGER_APOTHEKERVERBAND_E_V => 'TH',
            self::TESTVERBAND => 'ZZ',
            default => null,
        };
    }
}
