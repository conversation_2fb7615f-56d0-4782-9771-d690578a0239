<?php

namespace App\Enums\MeasureBloodPressure;

use Illuminate\Support\Collection;

class PatientAcquisitionFamilyWideEnum
{
    const FALSE = 0;

    const TRUE = 1;

    public static function getAll(): Collection
    {
        return collect([
            self::FALSE,
            self::TRUE,
        ]);
    }

    public static function getLabel($value): string
    {
        if ($value === null) {
            return '';
        }

        switch ($value) {
            case self::FALSE:
                return 'Nein';
            case self::TRUE:
                return 'Ja';
        }

        return '';
    }
}
