<?php

namespace App\Enums;

use Illuminate\Support\Collection;

class RatingProbabilityEnum
{
    const YES = 0;

    const PROBABLY = 1;

    const PROBABLY_NOT = 2;

    const NO = 3;

    const UNKNOWN = 4;

    public static function getAll(): Collection
    {
        return collect([
            self::YES,
            self::PROBABLY,
            self::PROBABLY_NOT,
            self::NO,
            self::UNKNOWN,
        ]);
    }

    public static function getLabel($value): string
    {
        if ($value === null) {
            return '';
        }
        $value = intval($value);

        switch ($value) {
            case self::YES:
                return 'Ja';
            case self::PROBABLY:
                return 'Wahrscheinlich';
            case self::PROBABLY_NOT:
                return '<PERSON>her nein';
            case self::NO:
                return 'Nein';
            case self::UNKNOWN:
                return 'Weiß nicht';
        }

        return '';
    }

    public static function getForDropdownWithTranslation()
    {
        return collect([
            self::YES => __('vaccination.poll.probability.'.self::YES),
            self::PROBABLY => __('vaccination.poll.probability.'.self::PROBABLY),
            self::PROBABLY_NOT => __('vaccination.poll.probability.'.self::PROBABLY_NOT),
            self::NO => __('vaccination.poll.probability.'.self::NO),
            self::UNKNOWN => __('vaccination.poll.probability.'.self::UNKNOWN),
        ]);
    }
}
