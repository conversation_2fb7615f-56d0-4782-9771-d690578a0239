<?php

namespace App\Enums\Newsletter;

use App\Settings\MailcoachSettings;
use Exception;

enum MailcoachList
{
    case Advertising;
    case System;

    public function uuid(): string
    {
        return match ($this) {
            MailcoachList::Advertising => app(MailcoachSettings::class)->advertisingMailingListId,
            MailcoachList::System => app(MailcoachSettings::class)->systemMailingListId,
        };
    }

    /**
     * @throws Exception
     */
    public static function fromUuid(string $uuid): MailcoachList
    {
        return match ($uuid) {
            app(MailcoachSettings::class)->advertisingMailingListId => MailcoachList::Advertising,
            app(MailcoachSettings::class)->systemMailingListId => MailcoachList::System,
            default => throw new Exception('Unknown Mailcoach list UUID: '.$uuid),
        };
    }

    public function referenceFieldName(): string
    {
        return match ($this) {
            MailcoachList::Advertising => 'advertisingListId',
            MailcoachList::System => 'systemListId',
        };
    }

    /**
     * @throws Exception
     */
    public static function fromReferenceFieldName(string $referenceFieldName): MailcoachList
    {
        return match ($referenceFieldName) {
            'advertisingListId' => MailcoachList::Advertising,
            'systemListId' => MailcoachList::System,
            default => throw new Exception('Unknown Mailcoach list reference field name: '.$referenceFieldName),
        };
    }
}
