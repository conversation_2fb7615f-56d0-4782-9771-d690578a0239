<?php

namespace App\Enums;

enum SubscriptionCancellationStatusEnum: string
{
    case PENDING = 'pending';
    case CONFIRMED = 'confirmed';
    case REVOKED = 'revoked';
    case EXECUTED = 'executed';

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Ausstehend',
            self::CONFIRMED => 'Bestätigt',
            self::REVOKED => 'Widerrufen',
            self::EXECUTED => 'Ausgeführt',
        };
    }

    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'yellow',
            self::CONFIRMED => 'orange',
            self::REVOKED => 'gray',
            self::EXECUTED => 'red',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::PENDING => 'Die Kündigung wurde beantragt und wartet auf Bestätigung.',
            self::CONFIRMED => 'Die Kündigung wurde bestätigt und wird zum angegebenen Datum wirksam.',
            self::REVOKED => 'Die Kündigung wurde widerrufen.',
            self::EXECUTED => 'Die Kündigung wurde ausgeführt und ist wirksam.',
        };
    }
}
