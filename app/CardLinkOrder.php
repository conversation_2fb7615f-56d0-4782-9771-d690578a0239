<?php

namespace App;

use App\Data\CardLink\OrderInformationData;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Events\CreatingCardLinkOrder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CardLinkOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'pharmacy_id',
        'user_id',
        'status',
        'order_information',
        'reserved_at',
        'ordered_at',
        'activate_to',
        'activated_at',
    ];

    protected $casts = [
        'status' => CardLinkOrderStatusEnum::class,
        'order_information' => 'array',
        'reserved_at' => 'datetime',
        'ordered_at' => 'datetime',
        'activate_to' => 'date',
        'activated_at' => 'datetime',
    ];

    /**
     * @var array<string, class-string>
     */
    protected $dispatchesEvents = [
        'creating' => CreatingCardLinkOrder::class,
    ];

    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withDefault(fn () => $this->pharmacy?->owner());
    }

    public function canBeTransmittedViaNova(): bool
    {
        return $this->status === CardLinkOrderStatusEnum::Ordered
               && $this->isMarkedForTransmission() === false
               && $this->isTransmitted() === false;
    }

    /** @deprecated this state should no longer be achievable */
    public function isMarkedForTransmission(): bool
    {
        return $this->status === CardLinkOrderStatusEnum::Activating
            && $this->activate_to !== null
            && $this->activate_to >= now()->startOfDay();
    }

    public function isTransmitted(): bool
    {
        $orderInformationData = OrderInformationData::fromCardLinkOrder($this);

        return $this->status === CardLinkOrderStatusEnum::Activated
            && $orderInformationData?->transmittedAt !== null;
    }
}
