<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Pharmacy $pharmacy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CardLinkPartnerPharmacy newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CardLinkPartnerPharmacy newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CardLinkPartnerPharmacy query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CardLinkPartnerPharmacy whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CardLinkPartnerPharmacy whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CardLinkPartnerPharmacy wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CardLinkPartnerPharmacy whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class CardLinkPartnerPharmacy extends Model
{
    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
