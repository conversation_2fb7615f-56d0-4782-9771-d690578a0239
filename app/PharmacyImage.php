<?php

namespace App;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

/**
 * Class PharmacyImage
 *
 * @mixin IdeHelperPharmacyImage
 */
class PharmacyImage extends Model
{
    use HasFactory;

    protected $touches = ['pharmacy'];

    protected $hidden = [
        'created_at',
        'updated_at',
        'pharmacy_id',
    ];

    protected $guarded = [
    ];

    public static function booted()
    {
        static::created(function ($item) {
            $exitingItems = self::query()
                ->where('pharmacy_id', $item->pharmacy_id)
                ->where('is_logo', $item->is_logo)
                ->where('id', '!=', $item->id)
                ->get();

            foreach ($exitingItems as $exitingItem) {
                $exitingItem->delete();
            }
        });
    }

    /**
     * @param  null|string  $disk
     * @return bool|null
     *
     * @throws Exception
     */
    public function delete($disk = null)
    {
        $disk = $disk ?? config('filesystems.default');

        \Sentry\addBreadcrumb(
            new \Sentry\Breadcrumb(
                \Sentry\Breadcrumb::LEVEL_INFO,
                \Sentry\Breadcrumb::TYPE_DEFAULT,
                'Files', // category
                'Deleting single file in PharmacyImage::delete', // message (optional)
                [
                    'disk' => $disk,
                    'image' => $this->path,
                    'pharmacy' => $this->pharmacy?->id,
                ] // data (optional)
            )
        );

        Storage::disk($disk)->delete($this->path);

        return parent::delete();
    }

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
