<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StripeMigrationOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'package',
        'addons',
    ];

    protected $casts = [
        'addons' => 'array',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }
}
