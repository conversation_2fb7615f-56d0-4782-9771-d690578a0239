<?php

namespace App;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class DocSpaceGroupUser extends Pivot
{
    protected $table = 'doc_space_group_user';

    public $timestamps = null;

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function docSpaceGroup(): BelongsTo
    {
        return $this->belongsTo(DocSpaceGroup::class);
    }
}
