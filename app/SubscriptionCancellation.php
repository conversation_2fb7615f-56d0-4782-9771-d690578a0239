<?php

namespace App;

use App\Enums\SubscriptionCancellationStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SubscriptionCancellation extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'requested_by',
        'requested_at',
        'status',
        'effective_date',
        'confirmed_at',
        'revoked_by',
        'revoked_at',
        'executed_at',
        'reason',
        'metadata',
    ];

    protected $casts = [
        'requested_at' => 'datetime',
        'status' => SubscriptionCancellationStatusEnum::class,
        'effective_date' => 'datetime',
        'confirmed_at' => 'datetime',
        'revoked_at' => 'datetime',
        'executed_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function pharmacy(): BelongsTo
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function requestedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function revokedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'revoked_by');
    }

    /**
     * Prüft ob die Kündigung noch widerrufen werden kann
     */
    public function canBeRevoked(): bool
    {
        return in_array($this->status, [
            SubscriptionCancellationStatusEnum::PENDING,
            SubscriptionCancellationStatusEnum::CONFIRMED,
        ]) && $this->effective_date->isFuture();
    }

    /**
     * Prüft ob die Kündigung wirksam ist
     */
    public function isEffective(): bool
    {
        return $this->status === SubscriptionCancellationStatusEnum::EXECUTED
            || ($this->status === SubscriptionCancellationStatusEnum::CONFIRMED && $this->effective_date->isPast());
    }
}
