<?php

namespace App;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class BillingAddress extends Model
{
    use HasFactory;

    protected $guarded = ['id', 'uuid'];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = Str::uuid()->toString();
        });
    }

    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    public function pharmacies(): HasMany
    {
        return $this->hasMany(Pharmacy::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /** @return Collection<User>
     * @throws Exception
     */
    public function editors(): Collection
    {
        $editors = collect();

        $billingAddressOwner = $this->owner;
        if (! $billingAddressOwner) {

            $billingAddressOwner = $this->pharmacies()->first()?->owner();
            if (! $billingAddressOwner) {
                return collect();
            }
        }
        $editors->push($billingAddressOwner);

        $pharmacyOwner = $billingAddressOwner->owner();
        if (! $pharmacyOwner) {
            return $editors;
        }

        $editors->push($pharmacyOwner);

        $pharmacyOwner->getSubOwners()->each(
            fn (User $user) => $editors->push($user)
        );

        return $editors->unique();
    }

    public function isEditable(): bool
    {
        return $this->invoices()->count() === 0;
    }

    public function isDeletable(): bool
    {
        return $this->isEditable() && $this->pharmacies()->count() === 0;
    }
}
