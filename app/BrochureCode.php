<?php

namespace App;

use App\Helper\BrochureCodeHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Laravel\Nova\Actions\Actionable;

/**
 * Class BrochureCode
 *
 * @mixin IdeHelperBrochureCode
 */
class BrochureCode extends Model
{
    use Actionable, HasFactory;

    protected $fillable = [
        'code',
    ];

    protected $casts = [
        'is_company' => 'boolean',
        'reg_link_sent_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function association(): BelongsTo
    {
        return $this->belongsTo(Association::class);
    }

    public function scopeFindForCode($query, $code)
    {
        return $query->where('code', '=', BrochureCodeHelper::convertUnreadableCharacter($code));
    }
}
