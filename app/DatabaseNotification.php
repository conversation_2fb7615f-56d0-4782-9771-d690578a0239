<?php

namespace App;

/**
 * @property int $id
 * @property string $type
 * @property string $notifiable_type
 * @property int $notifiable_id
 * @property array<array-key, mixed> $data
 * @property \Illuminate\Support\Carbon|null $read_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model $notifiable
 *
 * @method static \Illuminate\Notifications\DatabaseNotificationCollection<int, static> all($columns = ['*'])
 * @method static \Illuminate\Notifications\DatabaseNotificationCollection<int, static> get($columns = ['*'])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification query()
 * @method static Builder<static>|DatabaseNotification read()
 * @method static Builder<static>|DatabaseNotification unread()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereNotifiableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereNotifiableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DatabaseNotification whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DatabaseNotification extends \Illuminate\Notifications\DatabaseNotification
{
    protected $keyType = 'integer';

    public $incrementing = true;

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            // prevent Laravel from using UUIDs as PK here
            $model->{$model->getKeyName()} = null;
        });
    }
}
