<?php

namespace App\Jobs;

use App\Actions\Newsletter\DeleteSubscriber;
use App\Enums\Newsletter\MailcoachList;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Spatie\RateLimitedMiddleware\RateLimited;

class DeleteMailcoachUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private User $user;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /** @return array<mixed> */
    public function middleware()
    {
        return [
            (new RateLimited)
                ->enabled(config('queue.default') == 'redis')
                ->allow(60)
                ->everySeconds(1)
                ->releaseAfterOneMinute()
                ->key('mailcoach-api'),
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach (MailcoachList::cases() as $list) {
            if ($this->user->hasMailcoachSubscriptions($list)) {
                DeleteSubscriber::execute($this->user, $list);
            }
        }
    }
}
