<?php

namespace App\Jobs;

use App\Domains\Association\Domain\Enums\MemberImportStatus;
use App\MemberImport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class ImportAndCreateMember implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The data to be imported.
     *
     * @param  array<string, mixed>  $data
     */
    public function __construct(
        protected array $data
    ) {}

    public function handle(): void
    {
        $memberImport = new MemberImport([
            ...Arr::only($this->data, ['first_name', 'last_name', 'email']),
            'data' => $this->data,
            'status' => MemberImportStatus::Pending,
        ]);

        $memberImport->association()->associate($this->data['association_id']);

        $memberImport->save();

        $this->data['member_import_id'] = $memberImport->getKey();

        try {
            dispatch(new InstantRegistrationJob($this->data, true));
        } catch (\Exception $exception) {
            $memberImport->update(['status' => MemberImportStatus::Failed, 'status_description' => $exception->getMessage()]);
        }
    }
}
