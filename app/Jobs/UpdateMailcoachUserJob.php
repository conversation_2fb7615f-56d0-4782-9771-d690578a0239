<?php

namespace App\Jobs;

use App\Actions\Newsletter\UpdateSubscriber;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Spatie\RateLimitedMiddleware\RateLimited;

class UpdateMailcoachUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private User $user;

    /** @return array<mixed> */
    public function middleware(): array
    {
        return [
            (new RateLimited)
                ->enabled(config('queue.default') == 'redis')
                ->allow(60)
                ->everySeconds(1)
                ->releaseAfterOneMinute()
                ->key('mailcoach-api'),
        ];
    }

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        UpdateSubscriber::execute($this->user);
    }
}
