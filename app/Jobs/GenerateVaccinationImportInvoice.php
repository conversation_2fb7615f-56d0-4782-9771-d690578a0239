<?php

namespace App\Jobs;

use App\Enums\VaccinationImport\AccountingTypeEnum;
use App\Enums\VaccinationImport\VaccinationImportTypeEnum;
use App\VaccinationImportInvoice;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GenerateVaccinationImportInvoice implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private VaccinationImportInvoice $importInvoice;

    private bool $notify;

    public function __construct(VaccinationImportInvoice $importInvoice, bool $notify = true)
    {
        $this->importInvoice = $importInvoice;
        $this->notify = $notify;
    }

    public function handle()
    {
        $pdf = app('dompdf.wrapper');
        $path = Storage::disk('local-temp')->path('/pdf').Str::uuid().'.pdf';

        $query = $this->getQuery();

        $priceSummary = $this->calcPriceSummary($query);

        // Update total price on invoice
        $this->importInvoice->update([
            'price' => $priceSummary['total']['price'],
        ]);

        // Generate PDF
        $pdf->loadHTML(view('pharmacy.vaccinationImport.pdf.export', [
            'pharmacy' => $this->importInvoice->pharmacy,
            'query' => $query,
            'month' => $this->importInvoice->month,
            'year' => $this->importInvoice->year,
            'priceSummary' => $this->calcPriceSummary($query),
        ]))->save($path);

        $this->importInvoice->addMedia($path)
            ->toMediaCollection('invoice');

        $this->importInvoice->finished($this->notify);
    }

    public function calcPriceSummary($query)
    {
        $sum = ['total' => ['count' => 0, 'price' => 0], 'total_breakdown' => []];

        foreach (AccountingTypeEnum::getAll() as $type) {
            $sum[$type] = ['total' => ['count' => 0, 'price' => 0], 'breakdown' => []];
        }

        foreach (VaccinationImportTypeEnum::getAll() as $type) {
            $sum[$type] = ['total' => ['count' => 0, 'price' => 0], 'breakdown' => []];
        }

        foreach ($query->cursor() as $item) {
            $sum['total']['count']++;
            $sum['total']['price'] += $item->price;

            if (! array_key_exists($item->price, $sum['total_breakdown'])) {
                $sum['total_breakdown'][$item->price] = ['count' => 0, 'price' => 0];
            }

            $this->addInvoiceToStack($sum, $item, $item->type);

            $sum['total_breakdown'][$item->price]['count']++;
            $sum['total_breakdown'][$item->price]['price'] += $item->price;
        }

        return $sum;
    }

    /**
     * @return Builder
     */
    public function getQuery()
    {
        $startDate = Carbon::createFromFormat('Y-m-d H:i:s', $this->importInvoice->year.'-'.$this->importInvoice->month.'-01 00:00:00', 'Europe/Berlin')->setTimezone('UTC');
        $endDate = Carbon::createFromFormat('Y-m-d H:i:s', $this->importInvoice->year.'-'.($this->importInvoice->month + 1).'-01 00:00:00', 'Europe/Berlin')->setTimezone('UTC');

        if ($this->importInvoice->year === 2021 && $this->importInvoice->month === 8) {
            $toFastGeneratedInvoice = $this->importInvoice->pharmacy->vaccinationImportInvocies()->where('year', 2021)->where('month', 7)->whereBetween('updated_at', [Carbon::parse('2021-07-31 00:00:00'), Carbon::parse('2021-07-31 23:59:59')])->first();

            if ($toFastGeneratedInvoice) {
                return $this
                    ->importInvoice
                    ->pharmacy
                    ->vaccinationImports()
                    ->whereNull('covid_vaccination_id')
                    ->whereBetween('created_at', [
                        $toFastGeneratedInvoice->updated_at->setTimezone('UTC'),
                        Carbon::parse('2021-08-31 23:59:59', 'Europe/Berlin')->setTimezone('UTC'),
                    ]);
            }
        }

        return $this
            ->importInvoice
            ->pharmacy
            ->vaccinationImports()
            ->whereNull('covid_vaccination_id')
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<', $endDate);
    }

    private function addInvoiceToStack(&$sum, $item, $key)
    {
        if (! array_key_exists($item->price, $sum[$key]['breakdown'])) {
            $sum[$key]['breakdown'][$item->price] = ['count' => 0, 'price' => 0];
        }
        $sum[$key]['breakdown'][$item->price]['count']++;
        $sum[$key]['breakdown'][$item->price]['price'] += $item->price;
    }
}
