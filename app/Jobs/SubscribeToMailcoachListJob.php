<?php

namespace App\Jobs;

use App\Actions\Newsletter\SubscribeToMailcoachList;
use App\Enums\Newsletter\MailcoachList;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Spatie\RateLimitedMiddleware\RateLimited;

class SubscribeToMailcoachListJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private User $user;

    private MailcoachList $mailcoachList;

    /** @return array<mixed> */
    public function middleware()
    {
        return [
            (new RateLimited)
                ->enabled(config('queue.default') == 'redis')
                ->allow(60)
                ->everySeconds(1)
                ->releaseAfterOneMinute()
                ->key('mailcoach-api'),
        ];
    }

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user, MailcoachList $mailcoachList)
    {
        $this->user = $user;
        $this->mailcoachList = $mailcoachList;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        SubscribeToMailcoachList::execute($this->user, $this->mailcoachList);
    }
}
