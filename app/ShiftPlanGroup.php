<?php

namespace App;

use App\Domains\ShiftPlan\Domain\Observers\ShiftPlanGroupObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

#[ObservedBy([ShiftPlanGroupObserver::class])]
class ShiftPlanGroup extends Model implements Sortable
{
    use HasFactory;
    use SortableTrait;

    protected $guarded = [];

    public function buildSortQuery(): Builder
    {
        return static::query()->where('shift_plan_id', $this->shift_plan_id);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(ShiftPlan::class, 'shift_plan_id');
    }

    public function shiftPlanGroupUsers(): HasMany
    {
        return $this->hasMany(ShiftPlanGroupUser::class, 'shift_plan_group_id');
    }
}
