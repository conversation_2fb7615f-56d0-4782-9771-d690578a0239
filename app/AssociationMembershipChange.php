<?php

namespace App;

use App\Enums\AssociationMembershipChangeModeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int|null $association_id_before
 * @property int|null $association_id_after
 * @property \Illuminate\Support\Carbon|null $change_at
 * @property \Illuminate\Support\Carbon|null $change_done_at
 * @property \Illuminate\Support\Carbon|null $canceled_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property AssociationMembershipChangeModeEnum $mode
 * @property-read \App\Association|null $associationAfter
 * @property-read \App\Association|null $associationBefore
 * @property-read \App\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereAssociationIdAfter($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereAssociationIdBefore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereCanceledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereChangeAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereChangeDoneAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AssociationMembershipChange whereUserId($value)
 * @method static \Database\Factories\AssociationMembershipChangeFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class AssociationMembershipChange extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'change_at' => 'datetime',
        'change_done_at' => 'datetime',
        'canceled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'mode' => AssociationMembershipChangeModeEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function associationBefore(): BelongsTo
    {
        return $this->belongsTo(Association::class, 'association_id_before');
    }

    public function associationAfter(): BelongsTo
    {
        return $this->belongsTo(Association::class, 'association_id_after');
    }
}
