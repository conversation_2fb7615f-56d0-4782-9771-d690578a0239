<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property int $status
 * @property string $requested_at
 * @property string|null $started_at
 * @property string|null $finished_at
 * @property array<array-key, mixed>|null $additional
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereAdditional($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereFinishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereRequestedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|GuidedDocspaceCreationProcess whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class GuidedDocspaceCreationProcess extends Model
{
    use HasFactory;

    public const RESTART_PROCESS = 'restart';

    public const CREATE_MANUALLY = 'manually';

    protected $guarded = [];

    protected $casts = [
        'additional' => 'array',
    ];
}
