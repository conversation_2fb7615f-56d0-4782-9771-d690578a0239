<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Class RssFeedSource
 *
 * @mixin IdeHelperRssFeedSource
 *
 * @property int $id
 * @property string $url
 * @property string $identifier
 * @property string $name
 * @property string|null $description
 * @property int $enabled
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\RssFeedItem> $items
 * @property-read int|null $items_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereIdentifier($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RssFeedSource whereUrl($value)
 *
 * @mixin \Eloquent
 */
class RssFeedSource extends Model
{
    use HasFactory;

    public function items()
    {
        return $this->hasMany(RssFeedItem::class, 'rss_feed_source_id');
    }
}
