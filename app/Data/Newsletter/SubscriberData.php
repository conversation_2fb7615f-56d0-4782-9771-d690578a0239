<?php

namespace App\Data\Newsletter;

use <PERSON><PERSON>\LaravelData\Attributes\MapName;
use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Mappers\SnakeCaseMapper;

#[MapName(SnakeCaseMapper::class)]
class SubscriberData extends Data
{
    public string $uuid;

    public string $emailListUuid;

    public string $email;

    public ?string $firstName;

    public ?string $lastName;

    /** @var array<string, mixed> */
    public array $extraAttributes;

    /** @var array<string> */
    public array $tags;

    public string $subscribedAt;

    public ?string $unsubscribedAt;

    public string $createdAt;

    public string $updatedAt;
}
