<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use App\TelematicsId;
use Illuminate\Contracts\Validation\Rule;

class SectoralMark implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        switch ($attribute) {
            case 'telematics_id':
                $sectoralMark = TelematicsIdHelper::explode($value)['sectoralMark'];

                break;

            case 'sectoral_mark':
                $sectoralMark = $value;

                break;

            case 'telematicsId.sectoral_mark':
                $sectoralMark = $value;

                break;

            default:
                return false;
        }

        return in_array($sectoralMark, TelematicsId::ALLOWED_SECTORAL_MARKS);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
