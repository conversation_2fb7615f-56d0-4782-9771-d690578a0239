<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use App\TelematicsId;
use Illuminate\Contracts\Validation\Rule;

class ChamberId implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        switch ($attribute) {
            case 'telematics_id':
                $chamberId = TelematicsIdHelper::explode($value)['chamberId'];

                break;

            case 'chamber_id':
                $chamberId = $value;

                break;

            case 'telematicsId.chamber_id':
                $chamberId = $value;

                break;

            default:
                return false;
        }

        return in_array($chamberId, TelematicsId::ALLOWED_CHAMBER_IDS);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
