<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use App\TelematicsId;
use Illuminate\Contracts\Validation\Rule;

class TspIdentifier implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        switch ($attribute) {
            case 'telematics_id':
                $tspIdentifier = TelematicsIdHelper::explode($value)['tspIdentifier'];

                break;

            case 'tsp_identifier':
                $tspIdentifier = $value;

                break;

            case 'telematicsId.tsp_identifier':
                $tspIdentifier = $value;

                break;

            default:
                return false;
        }

        if ($tspIdentifier === null) {
            return true;
        }

        return in_array($tspIdentifier, TelematicsId::ALLOWED_TSP_IDENTIFIERS);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }
}
