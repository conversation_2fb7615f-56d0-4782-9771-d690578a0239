<?php

namespace App\Rules\TelematicsId;

use App\Helper\TelematicsIdHelper;
use App\Pharmacy;
use App\TelematicsId;
use Illuminate\Contracts\Validation\Rule;

class CardType implements Rule
{
    private array $allowedTypes = [];

    private $cardType;

    public function __construct(string $entity)
    {
        switch ($entity) {
            case Pharmacy::class:
                $this->allowedTypes = TelematicsId::ALLOWED_PHARMACY_CARD_TYPES;

                break;
        }
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        switch ($attribute) {
            case 'telematics_id':
                $cardType = TelematicsIdHelper::explode($value)['cardType'];

                break;

            case 'card_type':
                $cardType = $value;

                break;

            case 'telematicsId.card_type':
                $cardType = $value;

                break;

            default:
                return false;
        }

        $this->cardType = $cardType;

        return in_array($cardType, $this->allowedTypes);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        if (in_array($this->cardType, [1, 3])) {
            return trans('validation.tid_card_type_for_human');
        }

        return trans('validation.invalid_format');
    }
}
