<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class BrochureCodeFormat implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (strlen($value) !== 19) {
            return false;
        }

        $parts = str_split($value);
        foreach ($parts as $key => $part) {
            if (in_array($key, [4, 9, 14])) {
                if ($part !== '-') {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Fehlerhaftes Format. Bitte Code im Format "XXXX-XXXX-XXXX-XXXX" eingeben (inklusive Bindestriche).';
    }
}
