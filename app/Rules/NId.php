<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class NId implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (strlen($value) !== 10) {
            return false;
        }

        $prefix = substr($value, 0, 3);
        if ($prefix !== 'APO') {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('validation.invalid_format');
    }

    private function calculateCheckDigit(int $number): int
    {
        return (
            $number % 10 * 6
                + floor($number = $number / 10) % 10 * 5
                + floor($number = $number / 10) % 10 * 4
                + floor($number = $number / 10) % 10 * 3
                + floor($number = $number / 10) % 10 * 2
                + floor($number = $number / 10) % 10
        ) % 11 % 10;
    }
}
