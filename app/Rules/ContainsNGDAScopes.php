<?php

namespace App\Rules;

use App\Enums\NGDAScopes;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Translation\PotentiallyTranslatedString;

class ContainsNGDAScopes implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  Closure(string): PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value)) {
            $fail('Die Scopes müssen ein String sein.');

            return;
        }

        if (! str($value)->contains(NGDAScopes::N_HUB_WRITE->value)) {
            $fail('N-ID gehört zu einer nicht öffentlichen Apotheke');
        }
    }
}
