<?php

namespace App;

use App\Enums\UploadVisibility;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin IdeHelperUpload
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string $type
 * @property string $path
 * @property float|null $size
 * @property UploadVisibility $visibility
 * @property int|null $creator_id
 * @property int|null $updater_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Staff|null $creator
 * @property-read \App\Staff|null $updater
 *
 * @method static \Database\Factories\UploadFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereCreatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereUpdaterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload whereVisibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Upload withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Upload extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [
        'id', 'created_at', 'updated_at', 'deleted_at',
    ];

    protected $casts = [
        'visibility' => UploadVisibility::class,
    ];

    public function creator(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'creator_id');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'updater_id');
    }
}
