<?php

namespace App;

use App\Helper\TelematicsIdHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * Class TelematicsId
 *
 * @mixin IdeHelperTelematicsId
 */
class TelematicsId extends Model
{
    use HasFactory;

    public const ALLOWED_SECTORAL_MARKS = [
        '3',
    ];

    public const ALLOWED_CHAMBER_IDS = [
        '01',
        '02',
        '03',
        '04',
        '05',
        '06',
        '07',
        '08',
        '09',
        '10',
        '11',
        '12',
        '13',
        '14',
        '15',
        '16',
        '17',
    ];

    public const ALLOWED_PHARMACY_CARD_TYPES = [
        '2',
    ];

    public const ALLOWED_MEMBER_ID_SYMBOLS = [
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
        '1', '2', '3', '4', '5', '6', '7', '8', '9', '0',
        '-', '.',
    ];

    public const ALLOWED_TSP_IDENTIFIERS = [
        '10', // Bundesdruckerei / D-TRUST
        '12', // T-Systems / Telesec
        '16', // Medisign / DGNservice
        '17', // SHC/ ATOS
    ];

    protected $guarded = [];

    public function __construct(array $attributes = [])
    {
        if (isset($attributes['fullId'])) {
            $this->setValues($attributes['fullId']);
            unset($attributes['fullId']);
        }
        parent::__construct($attributes);
    }

    public function telematics_idable(): MorphTo
    {
        return $this->morphTo();
    }

    public function fullId(): string
    {
        return $this->sectoral_mark
            .'-'.$this->chamber_id
            .'.'.$this->card_type
            .'.'.$this->member_id
            .($this->tsp_identifier === null ? '' : '.'.$this->tsp_identifier)
            .'.'.$this->random_number;
    }

    public function setValues($value)
    {
        $values = TelematicsIdHelper::explode($value);

        $this->sectoral_mark = $values['sectoralMark'];
        $this->chamber_id = $values['chamberId'];
        $this->card_type = $values['cardType'];
        $this->member_id = $values['memberId'];
        $this->tsp_identifier = $values['tspIdentifier'];
        $this->random_number = $values['randomNumber'];
    }

    public function setPharmacyIdAttribute($value)
    {
        $this->telematics_idable_type = Pharmacy::class;
        $this->telematics_idable_id = $value;
    }

    public function getPharmacyIdAttribute($value)
    {
        if ($this->telematics_idable_type === Pharmacy::class) {
            return $this->telematics_idable_id;
        } else {
            return null;
        }
    }

    public function getCompleteIdAttribute()
    {
        return $this->sectoral_mark.'-'.
            $this->chamber_id.'.'.
            $this->card_type.'.'.
            $this->member_id.'.'.
            ($this->tsp_identifier ? $this->tsp_identifier.'.' : '').
            $this->random_number;
    }
}
