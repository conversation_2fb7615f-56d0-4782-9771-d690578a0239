<?php

namespace App;

use App\Enums\IbmRegistrationStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $pharmacy_id
 * @property string|null $order_pharmacy_name
 * @property string|null $order_ibm_id
 * @property \Illuminate\Support\Carbon|null $order_date
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $registered_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Pharmacy $pharmacy
 *
 * @method static \Database\Factories\IbmRegistrationFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereOrderDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereOrderIbmId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereOrderPharmacyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration wherePharmacyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereRegisteredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IbmRegistration whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class IbmRegistration extends Model
{
    use HasFactory;

    protected $fillable = [
        'pharmacy_id',
        'order_pharmacy_name',
        'order_ibm_id',
        'order_date',
        'status',
        'registered_at',
    ];

    protected $casts = [
        'order_date' => 'date',
        'registered_at' => 'datetime',
    ];

    public function pharmacy()
    {
        return $this->belongsTo(Pharmacy::class);
    }

    public function isIbmRegistered()
    {
        return $this->status === IbmRegistrationStatusEnum::REGISTERED;
    }
}
