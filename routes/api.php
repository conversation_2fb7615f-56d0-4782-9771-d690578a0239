<?php

use App\Domains\ShiftPlan\Application\Controllers\Api\ShiftController;
use App\Domains\ShiftPlan\Application\Controllers\Api\ShiftPlanController;
use App\Domains\ShiftPlan\Application\Controllers\Api\ShiftPlanGroupController;
use App\Domains\Subscription\Application\FeatureAccess\ShiftPlanFeatureAccess;
use App\Domains\User\Application\Controllers\Api\UserController;
use App\Http\Controllers\Api\AccountingCenterController;
use App\Http\Controllers\Api\ApomondoTopicController;
use App\Http\Controllers\Api\CardLinkController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\GoodsManagementSystemController;
use App\Http\Controllers\Api\KimStatusUpdateController;
use App\Http\Controllers\Api\LanguageController;
use App\Http\Controllers\Api\NewsletterWebhookController;
use App\Http\Controllers\Api\OwnerController;
use App\Http\Controllers\Api\PharmacyAddressController;
use App\Http\Controllers\Api\PharmacyController;
use App\Http\Controllers\Api\PharmacyImageController;
use App\Http\Controllers\PaymentPostbackController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::name('api.')->group(function () {
    Route::prefix('/client/v1')->group(function () {
        Route::get('/languages', [LanguageController::class, 'index'])->middleware('client:languages')->name('languages.index');
        Route::get('/languages/{language:code}', [LanguageController::class, 'show'])->middleware('client:languages')->name('languages.show');

        Route::get('/pharmacy-images', [PharmacyImageController::class, 'index'])->middleware('client:pharmacy-images')->name('pharmacy-images.index');
        Route::get('/pharmacy-images/{pharmacyImage}', [PharmacyImageController::class, 'show'])->middleware('client:pharmacy-images')->name('pharmacy-images.show');
        Route::get('/pharmacy-images/{pharmacyImage}/image', [PharmacyImageController::class, 'image'])->middleware('client:pharmacy-images')->name('pharmacy-images.show-image');

        Route::get('/goods-management-systems', [GoodsManagementSystemController::class, 'index'])->middleware('client:goods-management-systems')->name('goods-management-systems.index');
        Route::get('/goods-management-systems/{goodsManagementSystem}', [GoodsManagementSystemController::class, 'show'])->middleware('client:goods-management-systems')->name('goods-management-systems.show');

        Route::get('/accounting-centers', [AccountingCenterController::class, 'index'])->middleware('client:accounting-centers')->name('accounting-centers.index');
        Route::get('/accounting-centers/{accountingCenter}', [AccountingCenterController::class, 'show'])->middleware('client:accounting-centers')->name('accounting-centers.show');

        Route::get('/pharmacies', [PharmacyController::class, 'index'])->middleware('client:pharmacies')->name('pharmacies.index');
        Route::get('/pharmacies/{pharmacy:uuid}', [PharmacyController::class, 'show'])->middleware('client:pharmacies')->name('pharmacies.show');

        Route::get('/address/pharmacies', [PharmacyAddressController::class, 'index'])->middleware('client:pharmacies')->name('address.pharmacies.index');

        Route::get('/apomondo/topics', [ApomondoTopicController::class, 'index'])->middleware('client:apomondo-topics')->name('apomondo-topics.index');

        Route::get('/owners', [OwnerController::class, 'index'])->middleware('client:owners')->name('owners.index');
    });

    Route::post('v1/chat/matrix-bootstrap-done', [ChatController::class, 'matrixBootstrapDone'])->middleware('validate-idp-id-token')->withoutMiddleware('api.dynamic-throttle')->name('chat.matrix-bootstrap-done');

    // TODO: These endpoints (chat-token, pharmacies-token) should be removed after AP-1932 has been released.
    Route::get('v1/users/chat-token', [\App\Http\Controllers\Api\UserController::class, 'chatToken'])->middleware('validate-idp-id-token')->withoutMiddleware('api.dynamic-throttle');
    Route::get('v1/users/pharmacies-token', [\App\Http\Controllers\Api\UserController::class, 'pharmaciesToken'])->middleware('validate-idp-id-token')->withoutMiddleware('api.dynamic-throttle');

    Route::get('v1/services/token/chat', [\App\Http\Controllers\Api\UserController::class, 'chatToken'])->middleware('validate-idp-id-token')->withoutMiddleware('api.dynamic-throttle')->name('users.chat-token');
    Route::get('v1/services/token/pharmacies', [\App\Http\Controllers\Api\UserController::class, 'pharmaciesToken'])->middleware('validate-idp-id-token')->withoutMiddleware('api.dynamic-throttle')->name('users.pharmacies-token');

    Route::get('v1/user-context/retax', [\App\Http\Controllers\Api\UserController::class, 'retaxToken'])
        ->middleware('validate-idp-id-token')
        ->withoutMiddleware('api.dynamic-throttle')
        ->name('users.retax-context-token');

    Route::post('/kim/status-update', [KimStatusUpdateController::class, 'handle'])->withoutMiddleware('api.dynamic-throttle')->name('kim.status-update.handle');

    Route::prefix('/card-link')
        ->middleware('client:cardlink')
        ->withoutMiddleware('api.dynamic-throttle')
        ->group(function () {
            Route::post('/limit-notification', [CardLinkController::class, 'limitNotification'])->name('card-link.limit-notification');
            Route::post('/new-limit-notification', [CardLinkController::class, 'notification'])->name('card-link.new-limit-notification');
        });

    Route::prefix('/v1/')->middleware(['api.bearer-auth', 'ensure-subscribed:'.ShiftPlanFeatureAccess::class])->withoutMiddleware('api.dynamic-throttle')->group(function () {
        Route::prefix('/shift_plan')->name('shift-plan.')->group(function () {
            Route::get('/plans', [ShiftPlanController::class, 'index'])->name('plans.index');
            Route::get('/groups', [ShiftPlanGroupController::class, 'index'])->name('groups.index');
            Route::get('/shifts', [ShiftController::class, 'index'])->name('shifts.index');
        });

        Route::get('/employees', [UserController::class, 'index'])->name('users.index');
    });
});

Route::name('payment.')->prefix('/payment/v1')->group(function () {
    Route::post('/postback', [PaymentPostbackController::class, 'index'])->name('postback')->withoutMiddleware('api.dynamic-throttle');
});

Route::post('/newsletter/webhook', NewsletterWebhookController::class)->withoutMiddleware('api.dynamic-throttle')->name('api.newsletter.webhook');
