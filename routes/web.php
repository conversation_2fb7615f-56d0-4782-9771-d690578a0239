<?php

use App\Domains\ShiftPlan\Application\Controllers\ShiftPlanController;
use App\Domains\Subscription\Application\FeatureAccess\KimFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\PharmaceuticalServicesFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\ShiftPlanFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\VaccinationCertificateFeatureAccess;
use App\Domains\Subscription\Application\FeatureAccess\VaccinationFeatureAccess;
use App\Features\CardLink;
use App\Http\Controllers\Apoguide\ApoguideController;
use App\Http\Controllers\ApomailController;
use App\Http\Controllers\ApomailRegistrationController;
use App\Http\Controllers\AssociationController;
use App\Http\Controllers\AssociationFeedbackController;
use App\Http\Controllers\AssociationMemberController;
use App\Http\Controllers\AssociationUserController;
use App\Http\Controllers\AssociationVaccinateController;
use App\Http\Controllers\AuthorController;
use App\Http\Controllers\BetaTestController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DocspaceController;
use App\Http\Controllers\DocspaceGroupController;
use App\Http\Controllers\FeatureRequirementsController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\IaActivationRedirectController;
use App\Http\Controllers\KimAddressCareCenterController;
use App\Http\Controllers\KimAddressController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\Ngda\NgdaCallbackController;
use App\Http\Controllers\NovaFileController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PharmacyCalendarController;
use App\Http\Controllers\PharmacyController;
use App\Http\Controllers\PharmacyCovidVaccinateController;
use App\Http\Controllers\PharmacyCovidVaccinateInvoiceController;
use App\Http\Controllers\PharmacyCovidVaccination\AbortController;
use App\Http\Controllers\PharmacyCovidVaccination\FinishedController;
use App\Http\Controllers\PharmacyCovidVaccination\OverviewController;
use App\Http\Controllers\PharmacyCovidVaccination\PersonalDataController;
use App\Http\Controllers\PharmacyCovidVaccination\StartController;
use App\Http\Controllers\PharmacyCovidVaccination\VaccinationDataController;
use App\Http\Controllers\PharmacyDownloadApoguideQrCodeController;
use App\Http\Controllers\PharmacyImageApprovalController;
use App\Http\Controllers\PharmacyImageController;
use App\Http\Controllers\PharmacyImageNovaController;
use App\Http\Controllers\PharmacyImportVaccinationAccountingController;
use App\Http\Controllers\PharmacyImportVaccinationController;
use App\Http\Controllers\PharmacyInfluenzaVaccinateController;
use App\Http\Controllers\PharmacyInfluenzaVaccinateInvoiceController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\AbortController as PharmacyInfluenzaVaccinationAbortController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\AcceptanceController as PharmacyInfluenzaVaccinationAcceptanceController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\DocumentationController as PharmacyInfluenzaVaccinationDocumentationController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\DownloadController as PharmacyInfluenzaVaccinationDownloadController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\EditController as PharmacyInfluenzaVaccinationEditController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\EducationalController as PharmacyInfluenzaVaccinationEducationalController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\EvaluationPharmacyController as PharmacyInfluenzaVaccinationEvaluationPharmacyController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\EvaluationShowController as PharmacyInfluenzaVaccinationEvaluationShowController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\EvaluationUserController as PharmacyInfluenzaVaccinationEvaluationUserController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\ExplanationController as PharmacyInfluenzaVaccinationExplanationController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\FinishedController as PharmacyInfluenzaVaccinationFinishedController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\HealthInsuranceController as PharmacyInfluenzaVaccinationHealthInsuranceController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\PersonalDataController as PharmacyInfluenzaVaccinationPersonalDataController;
use App\Http\Controllers\PharmacyInfluenzaVaccination\StikoController as PharmacyInfluenzaVaccinationStikoController;
use App\Http\Controllers\PharmacyInhalationTechnique\ChecklistController;
use App\Http\Controllers\PharmacyInhalationTechnique\FirstDocumentController;
use App\Http\Controllers\PharmacyInhalationTechniqueController;
use App\Http\Controllers\PharmacyMeasureBloodPressure\AcquisitionDataController;
use App\Http\Controllers\PharmacyMeasureBloodPressure\MeasurementController;
use App\Http\Controllers\PharmacyMeasureBloodPressuresController;
use App\Http\Controllers\PharmacyPharmaceuticalServicesController;
use App\Http\Controllers\PharmacySubscriptionController;
use App\Http\Controllers\PharmacyTelepharmacyController;
use App\Http\Controllers\PharmacyUserCompanyController;
use App\Http\Controllers\PharmacyUserController;
use App\Http\Controllers\PharmacyVaccinateController;
use App\Http\Controllers\PrivateFileController;
use App\Http\Controllers\PublicPharmacyImageController;
use App\Http\Controllers\RedirectController;
use App\Http\Controllers\RegistrationRequestController;
use App\Http\Controllers\SubscriptionExportTestController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\TermsOfUseController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\TIGatewayController;
use App\Http\Controllers\TiTesterController;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\UserBillingAddressesController;
use App\Http\Controllers\UserBrochureCodeController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserEmailChangeVerifyController;
use App\Http\Middleware\IaFeatureEnabledMiddleware;
use App\Http\Middleware\IaTokenAuthMiddleware;
use App\Livewire\Association\News\Pages\CreateAssociationNews;
use App\Livewire\Association\News\Pages\EditAssociationNews;
use App\Livewire\Association\News\Pages\ListAssociationNews;
use App\Livewire\Association\News\Pages\Pharmacy\ShowAssociationNews;
use App\Livewire\CardLink\ChangePackage;
use App\Livewire\CardLink\ConfirmDowngrade;
use App\Livewire\CardLink\ReserveCardLinkOrder;
use App\Livewire\CardLink\Success;
use App\Livewire\CardLink\TransactionHistory;
use App\Livewire\Ia\Activate;
use App\Livewire\Ia\WebComponents\AdvertisedProducts;
use App\Livewire\Ia\WebComponents\Assortment;
use App\Livewire\Ia\WebComponents\BookingComponent;
use App\Livewire\Ia\WebComponents\Couponing;
use App\Livewire\Ia\WebComponents\Delivery;
use App\Livewire\Ia\WebComponents\ExpressDelivery;
use App\Livewire\Ia\WebComponents\ExtendedProfile;
use App\Livewire\Ia\WebComponents\ImpressumAndLegal;
use App\Livewire\Ia\WebComponents\IntegrationConfig;
use App\Livewire\Ia\WebComponents\IntegrationInfo;
use App\Livewire\Ia\WebComponents\IntegrationStyle;
use App\Livewire\Ia\WebComponents\MyLifeIndividualisation;
use App\Livewire\Ia\WebComponents\NotificationSettings;
use App\Livewire\Ia\WebComponents\OnlinePayment;
use App\Livewire\Ia\WebComponents\OwnPzn;
use App\Livewire\Ia\WebComponents\PaidOrders;
use App\Livewire\Ia\WebComponents\PharmacyServices;
use App\Livewire\Ia\WebComponents\ProductAvailability;
use App\Livewire\Ia\WebComponents\ReservationSystem;
use App\Livewire\Ia\WebComponents\SpecialOffers;
use App\Livewire\Ia\WebComponents\Telemedizin;
use App\Livewire\Ia\WebComponents\Wawi;
use App\Livewire\Terms\TermsDeadlineWarning;
use Illuminate\Support\Facades\Route;
use Laravel\Pennant\Middleware\EnsureFeaturesAreActive;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Route::get('/vendor/famedly-web/service_worker.js', function () {
//    $file = File::get(public_path('/vendor/famedly-web/service_worker2.js'));
//    $response = Response::make($file, 200);
//    $response->header('Content-Type', 'application/javascript');
//    $response->header('Service-Worker-Allowed', '/');
//    return $response;
// });

Route::get('test/{type}', [TestController::class, 'index']);

Route::get('/activate-2fa', function () {
    session()->put('2fa-activated', true);

    return redirect()->route('sdr.doc-spaces', ['pharmacy' => currentPharmacy()]);
});

Route::get('/subscription-export-test', [SubscriptionExportTestController::class, 'index']);
// Public Routes
Route::get('/', [PageController::class, 'home'])->name('home');
Route::get('/ti-gateway', [TIGatewayController::class, 'gedisaTiGateway'])->name('ti-gateway');
Route::get('/ia-ti-gateway', [TIGatewayController::class, 'iATiGateway'])->name('ia-ti-gateway');
Route::get('/impressum', [PageController::class, 'imprint'])->name('imprint');
Route::get('/datenschutz', [PageController::class, 'privacy'])->name('privacy');
Route::get('/datenschutz/avv.pdf', [PageController::class, 'avv'])->name('avv');
Route::get('/nutzungsbedingungen', [PageController::class, 'termsOfUse'])->name('terms-of-use');
Route::get('/support', [PageController::class, 'support'])->name('support');
Route::get('/support/anmeldeportal', [PageController::class, 'LoginFaq'])->name('login-faq');
Route::get('/projects', [PageController::class, 'projects'])->name('projects');
Route::get('/covidzertifikatsinformationen', [PageController::class, 'covidCertificateInformation'])->name('covid-certificate-information');
Route::get('/templates/association-member-import', [PageController::class, 'associationMemberImport'])->name('association-member-import');

Route::get('/storage/media/{id}/{search}', [PrivateFileController::class, 'showMedia'])->where('search', '.*');
Route::get('/storage/blog-attachments/{search}', [PrivateFileController::class, 'showBlog'])->where('search', '.*');

Route::middleware([])->group(function () {
    Route::get('/{type}', [NewsController::class, 'index'])->where('type', '(blog|news)')->name('news.index');
    Route::get('/{type}/{news:slug}', [NewsController::class, 'show'])->where('type', '(blog|news)')->name('news.show');
    Route::get('/{type}/authors/{author:slug}', [AuthorController::class, 'show'])->where('type', '(blog|news)')->name('authors.show');
    Route::get('/{type}/categories/{category:slug}', [CategoryController::class, 'show'])->where('type', '(blog|news)')->name('categories.show');
    Route::get('/{type}/tags/{tag:slug}', [TagController::class, 'show'])->where('type', '(blog|news)')->name('tags.show');
});

// Default Auth Routes
require __DIR__.'/auth.php';

Route::get('/messenger/app/{path?}', [ChatController::class, 'app'])->where('path', '.*')->name('messenger.app');
Route::middleware(['auth'])->group(function () {
    Route::post('/messenger/auth', [ChatController::class, 'auth'])->name('messenger.auth');
    Route::post('/messenger/oauth', [ChatController::class, 'oauth'])->name('messenger.oauth');
    Route::get('/users/billing-addresses', [UserBillingAddressesController::class, 'index'])->name('users.billing-addresses');
    Route::get('/users/billing-addresses/upsert/{billingAddress?}', [UserBillingAddressesController::class, 'upsert'])->name('users.billing-addresses.upsert');
    Route::get('/users/contracts', [UserController::class, 'contracts'])->name('users.contracts');
    Route::get('/users/check-for-idp-email-change', [UserController::class, 'idpEmailChange'])->name('users.check-for-idp-email-change');
    Route::post('/users/check-for-idp-email-change/set-address', [UserController::class, 'setIdpEmailAddress'])->name('users.check-for-idp-email-change.set-address');
    Route::get('terms-deadline-warning', TermsDeadlineWarning::class)->name('terms-deadline-warning');
});

// Authenticated Users Routes
Route::middleware(['auth', 'check-pharmacy'])->group(function () {
    Route::get('/users/edit', [UserController::class, 'edit'])->name('users.edit');
    Route::put('/users/update', [UserController::class, 'update'])->name('users.update');
    Route::put('/users/update-company', [UserController::class, 'updateCompany'])->name('users.updateCompany');
    Route::put('/users/ensure-company-data-update', [UserController::class, 'ensureCompanyDataUpdate'])->name('users.ensureCompanyDataUpdate');
    Route::get('/users/email/verified', [UserEmailChangeVerifyController::class, 'showVerified'])->name('user.email.showVerified');
    Route::get('/users/email/change', [UserEmailChangeVerifyController::class, 'handle'])->name('user.email.change')->middleware('signed');
    Route::post('/users/email/change', [UserEmailChangeVerifyController::class, 'resend'])->name('user.email.resend');
    Route::get('users/brochure-code/edit', [UserBrochureCodeController::class, 'edit'])->name('user.brochureCode.edit');
    Route::post('users/brochure-code/update', [UserBrochureCodeController::class, 'update'])->name('user.brochureCode.update');

    Route::get('/downloads/{media}/{filename}', [PrivateFileController::class, 'show']);

    Route::get('/association-feedback/index', [AssociationFeedbackController::class, 'index'])->name('associationFeedback.feedback');
    Route::get('/association-feedback/feedback/show', [AssociationFeedbackController::class, 'viewDefaultFeedback'])->name('associationFeedback.feedback.show');
    Route::post('/association-feedback/feedback/send', [AssociationFeedbackController::class, 'sendDefaultFeedback'])->name('associationFeedback.feedback.send');
});

// Verified Users Routes
Route::middleware([
    'auth',
    'check-for-idp-email-change',
    'check-pharmacy',
    'brochure-code',
    'ensure-terms-of-use-are-accepted',
    'check-email-registration',
])->group(function () {
    Route::get('/ensure-company-data', function () {
        return view('user.ensure-company-data');
    })->name('ensure-company-data');

    Route::middleware(['ensure-company-data'])->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

        Route::get('/chat/matrix-bootstrap/{pharmacy?}', [ChatController::class, 'matrixBootstrap'])->middleware('auth')->name('pharmacies.chat.matrix-bootstrap');
        Route::get('/chat', [ChatController::class, 'chat'])->middleware('auth')->name('pharmacies.chat');

        Route::get('/beta-test/registration', [BetaTestController::class, 'registration'])->name('beta-test.registration')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::post('/beta-test/store-registration', [BetaTestController::class, 'storeRegistration'])->name('beta-test.store-registration')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::get('/beta-test/show', [BetaTestController::class, 'show'])->name('beta-test.show')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::post('/beta-test/sign-off', [BetaTestController::class, 'signOff'])->name('beta-test.sign-off')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);

        Route::middleware(['can-view-apomail-administration'])->group(function () {
            Route::get('/apomail', [ApomailController::class, 'index'])->name('apomails');
            Route::get('/apomail/create', [ApomailController::class, 'create'])->name('apomails.create');
            Route::get('/apomail/{apomail}/edit', [ApomailController::class, 'edit'])->name('apomails.edit');
        });
        Route::get('/apo-mail-registration', [ApomailRegistrationController::class, 'create'])->name('apo-mail-registration');

        Route::get('/pharmacies', [PharmacyController::class, 'index'])->name('pharmacies')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::get('/pharmacies/create', [PharmacyController::class, 'create'])->name('pharmacies.create')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::post('/pharmacies/store', [PharmacyController::class, 'store'])->name('pharmacies.store')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::get('/pharmacies/{pharmacy}/terms-of-use', [TermsOfUseController::class, 'show'])->name('pharmacies.termsOfUse')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::get('/pharmacies/{pharmacy}/terms-of-use-deadline', [TermsOfUseController::class, 'showAfterTOSDeadline'])->name('pharmacies.termsOfUseDeadline')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::get('/pharmacies/{pharmacy}/edit', [PharmacyController::class, 'edit'])->name('pharmacies.edit');
        Route::put('/pharmacies/{pharmacy}/update', [PharmacyController::class, 'update'])->name('pharmacies.update');
        Route::delete('/pharmacies/{pharmacy}/destroy', [PharmacyController::class, 'destroy'])->name('pharmacies.destroy');
        Route::put('/pharmacies/{pharmacy}/activate', [PharmacyController::class, 'activate'])->name('pharmacies.activate');
        Route::put('/pharmacies/{pharmacy}/deactivate', [PharmacyController::class, 'deactivate'])->name('pharmacies.deactivate');
        Route::get('/pharmacies/{pharmacy}/switch', [PharmacyController::class, 'switch'])->name('pharmacies.switch')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::put('/pharmacies/{pharmacy}/toggle-sdr', [PharmacyController::class, 'toggleSDR'])->name('pharmacies.toggle-sdr');

        Route::prefix('/my-association')->group(function () {
            Route::get('/news', \App\Livewire\Association\News\Pages\Pharmacy\ListAssociationNews::class)->name('my-association.index');
            Route::get('/news/{news:slug}', ShowAssociationNews::class)->name('my-association.news.show');
        });

        Route::prefix('/pharmacies/{pharmacy}/card-link')->group(function () {
            Route::get('/', \App\Livewire\CardLink\CardLink::class)->name('card-link');
            Route::get('/change-package', ChangePackage::class)->name('card-link.change-package');
            Route::get('/confirm-downgrade', ConfirmDowngrade::class)->name('card-link.confirm-downgrade');
            Route::get('/reserve', ReserveCardLinkOrder::class)->name('card-link.reserve');
            Route::get('/success', Success::class)->name('card-link.success');
            Route::get('/transactions', TransactionHistory::class)->name('card-link.transactions');
        })->middleware(EnsureFeaturesAreActive::using(CardLink::class));

        Route::prefix('/card-link')->group(function () {
            Route::fallback(fn () => redirect()->route('card-link', currentPharmacy()));
        })->middleware(EnsureFeaturesAreActive::using(CardLink::class));

        Route::prefix('/pharmacies/{pharmacy}')->middleware([IaFeatureEnabledMiddleware::class])->group(function () {
            Route::prefix('/online-shop')->name('ia.')->group(function () {
                Route::get('activate', Activate::class)->name('activate');
                Route::middleware([IaTokenAuthMiddleware::class])->group(function () {
                    Route::prefix('/orders')->name('orders.')->group(function () {
                        Route::get('reservation-system', ReservationSystem::class)->name('reservation-system');
                        Route::get('notification-settings', NotificationSettings::class)->name('notification-settings');
                        Route::get('paid-orders', PaidOrders::class)->name('paid-orders');
                        Route::get('telemedizin', Telemedizin::class)->name('telemedizin');
                    });
                    Route::prefix('/einstellungen')->name('einstellungen.')->group(function () {
                        Route::prefix('/preis-sortiment')->name('preis-sortiment.')->group(function () {
                            Route::get('assortment', Assortment::class)->name('assortment');
                            Route::get('product-availability', ProductAvailability::class)->name('product-availability');
                            Route::get('sonderangebote', SpecialOffers::class)->name('sonderangebote');
                            Route::get('beworbene-produkte', AdvertisedProducts::class)->name('beworbene-produkte');
                            Route::get('own-pzn', OwnPzn::class)->name('own-pzn');
                        });
                        Route::prefix('/ihre-website')->name('ihre-website')->group(function () {
                            Route::get('website-einrichten', IntegrationInfo::class)->name('website-einrichten');
                            Route::get('integration-config', IntegrationConfig::class)->name('integration-config');
                            Route::get('integration-style', IntegrationStyle::class)->name('integration-style');
                        });
                        Route::prefix('/konfiguration')->name('konfiguration.')->group(function () {
                            Route::get('botendienst', Delivery::class)->name('botendienst');
                            Route::get('express-botendienst', ExpressDelivery::class)->name('express-botendienst');
                            Route::get('my-life', MyLifeIndividualisation::class)->name('my-life');
                            Route::get('couponing', Couponing::class)->name('couponing');
                            Route::get('erweitertes-profil', ExtendedProfile::class)->name('erweitertes-profil');
                            Route::get('wawi', Wawi::class)->name('wawi');
                            Route::get('online-payment', OnlinePayment::class)->name('online-payment');
                            Route::get('services', PharmacyServices::class)->name('services');
                            Route::get('terms', ImpressumAndLegal::class)->name('terms');
                            Route::get('card-link', \App\Livewire\Ia\WebComponents\CardLink::class)->name('card-link');
                            Route::get('booking-component', BookingComponent::class)->name('booking-component');
                        });
                    });
                });
            });
        });
        Route::get('/pharmacies/{pharmacy}/ngda-callback', NgdaCallbackController::class)->name('pharmacies.ngda-callback');

        Route::middleware('ensure-subscribed')->group(function () {
            Route::get('/pharmacies/{pharmacy}', [PharmacyController::class, 'overview'])->name('pharmacies.overview');
        });
        Route::get('/pharmacies/{pharmacy}/calendar/redirect', [PharmacyCalendarController::class, 'redirector'])->name('pharmacies.access-calendar');
        Route::get('/pharmacies/{pharmacy}/telepharmacy/redirect', [PharmacyTelepharmacyController::class, 'redirector'])->name('pharmacies.access-telepharmacy');

        Route::get('/pharmacies/{pharmacy}/users', [PharmacyController::class, 'users'])->name('pharmacies.users');
        Route::get('/pharmacies/{pharmacy}/users/create', [PharmacyUserController::class, 'create'])->name('pharmacies.users.create');
        Route::post('/pharmacies/{pharmacy}/users/store', [PharmacyUserController::class, 'store'])->name('pharmacies.users.store');
        Route::get('/pharmacies/{pharmacy}/users/{user}/edit', [PharmacyUserController::class, 'edit'])->name('pharmacies.users.edit');
        Route::put('/pharmacies/{pharmacy}/users/{user}/update', [PharmacyUserController::class, 'update'])->name('pharmacies.users.update');
        Route::delete('/pharmacies/{pharmacy}/users/{user}/destroy', [PharmacyUserController::class, 'destroy'])->name('pharmacies.users.destroy');

        Route::get('/pharmacy-company/users', [PharmacyUserCompanyController::class, 'owners'])->name('pharmacies.company.users');
        Route::get('/pharmacy-company/users/create', [PharmacyUserCompanyController::class, 'create'])->name('pharmacies.company.users.create');
        Route::post('/pharmacy-company/users/store', [PharmacyUserCompanyController::class, 'store'])->name('pharmacies.company.users.store');
        Route::get('/pharmacy-company/users/{user}/edit', [PharmacyUserCompanyController::class, 'edit'])->name('pharmacies.company.users.edit');
        Route::put('/pharmacy-company/users/{user}/update', [PharmacyUserCompanyController::class, 'update'])->name('pharmacies.company.users.update');
        Route::get('/pharmacies/{pharmacy}/download/apoguide-qr-code', [PharmacyDownloadApoguideQrCodeController::class, 'downloadApoguideQRCode'])->name('pharmacies.apoguide-qr-code');
        Route::get('/pharmacies/{pharmacy}/download/apoguide-poster', [PharmacyDownloadApoguideQrCodeController::class, 'downloadApoguidePoster'])->name('pharmacies.apoguide-poster');

        Route::get('/pharmacies/{pharmacy}/logo', [PharmacyImageController::class, 'logo'])->name('pharmacies.logo');
        Route::get('/pharmacies/{pharmacy}/image', [PharmacyImageController::class, 'image'])->name('pharmacies.image');

        Route::get('/pharmacies/{pharmacy}/data', [PharmacyController::class, 'data'])->name('pharmacies.data.index');
        Route::get('/pharmacies/{pharmacy}/subscription', [PharmacySubscriptionController::class, 'show'])->name('pharmacies.subscription')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);
        Route::get('/pharmacies/{pharmacy}/subscription/invoices', [PharmacySubscriptionController::class, 'invoices'])->name('pharmacies.invoices')->withoutMiddleware(['ensure-terms-of-use-are-accepted']);

        Route::middleware('ensure-subscribed:'.KimFeatureAccess::class)->group(function () {
            Route::get('/pharmacies/{pharmacy}/kim-addresses', [KimAddressController::class, 'index'])->name('kims');
            Route::get('/pharmacies/{pharmacy}/kim-address/create', [KimAddressController::class, 'create'])->name('kims.create');
            Route::get('/pharmacies/{pharmacy}/kim-address/{kimAddress}', [KimAddressController::class, 'show'])->name('kims.show');
            Route::get('/pharmacies/{pharmacy}/kim-addresses/care-center', [KimAddressCareCenterController::class, 'index'])->name('kims.care-center');
            Route::delete('/pharmacies/{pharmacy}/kim-address/{kimAddress}/destroy', [KimAddressController::class, 'destroy'])->name('kims.destroy');
        });

        Route::middleware('ensure-subscribed:'.PharmaceuticalServicesFeatureAccess::class)->group(function () {
            Route::get('/pharmacies/{pharmacy}/services', [PharmacyPharmaceuticalServicesController::class, 'index'])->name('pharmacies.pharmaceutical-services.index');

            Route::get('/pharmacies/{pharmacy}/services/accounting', [PharmacyPharmaceuticalServicesController::class, 'accounting'])->name('pharmacies.pharmaceutical-services.accounting');

            Route::get('/pharmacies/{pharmacy}/pharmaceutical-service-invoices/invoice/{pharmaceuticalServiceInvoice}/download', [PharmacyPharmaceuticalServicesController::class, 'download'])->name('pharmacies.pharmaceutical-services.download');

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure', [PharmacyMeasureBloodPressuresController::class, 'index'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.index');
            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/edit', [PharmacyMeasureBloodPressuresController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.edit');

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/start', [PharmacyMeasureBloodPressuresController::class, 'start'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.start');
            Route::post('/pharmacies/{pharmacy}/services/measure-blood-pressure/start', [PharmacyMeasureBloodPressuresController::class, 'store']);

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/edit', [PharmacyMeasureBloodPressuresController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.edit');

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/personal-data', [\App\Http\Controllers\PharmacyMeasureBloodPressure\PersonalDataController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.personal-data');
            Route::post('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/personal-data', [\App\Http\Controllers\PharmacyMeasureBloodPressure\PersonalDataController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/first-document', [\App\Http\Controllers\PharmacyMeasureBloodPressure\FirstDocumentController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.first-document');
            Route::post('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/first-document', [\App\Http\Controllers\PharmacyMeasureBloodPressure\FirstDocumentController::class, 'update']);
            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/first-document/download', [\App\Http\Controllers\PharmacyMeasureBloodPressure\FirstDocumentController::class, 'generateAndDownload'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.first-document.download');

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/acquisition-data', [AcquisitionDataController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.acquisition-data');
            Route::post('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/acquisition-data', [AcquisitionDataController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/measurement', [MeasurementController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.measuring-data');
            Route::post('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/measurement', [MeasurementController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/overview', [\App\Http\Controllers\PharmacyMeasureBloodPressure\OverviewController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.overview');
            Route::post('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/overview', [\App\Http\Controllers\PharmacyMeasureBloodPressure\OverviewController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/abort/{reasons}', [\App\Http\Controllers\PharmacyMeasureBloodPressure\AbortController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.abort');

            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/finished', [\App\Http\Controllers\PharmacyMeasureBloodPressure\FinishedController::class, 'edit'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.finished');
            Route::get('/pharmacies/{pharmacy}/services/measure-blood-pressure/{pharmaceuticalService}/download-result', [\App\Http\Controllers\PharmacyMeasureBloodPressure\FinishedController::class, 'generateAndDownload'])->name('pharmacies.pharmaceutical-services.measure-blood-pressures.download-result');

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique', [PharmacyInhalationTechniqueController::class, 'index'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.index');
            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/edit', [PharmacyInhalationTechniqueController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.edit');

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/start', [PharmacyInhalationTechniqueController::class, 'start'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.start');
            Route::post('/pharmacies/{pharmacy}/services/inhalation-technique/start', [PharmacyInhalationTechniqueController::class, 'store']);

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/edit', [PharmacyInhalationTechniqueController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.edit');

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/personal-data', [\App\Http\Controllers\PharmacyInhalationTechnique\PersonalDataController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.personal-data');
            Route::post('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/personal-data', [\App\Http\Controllers\PharmacyInhalationTechnique\PersonalDataController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/first-document', [FirstDocumentController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.first-document');
            Route::post('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/first-document', [FirstDocumentController::class, 'update']);
            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/first-document/download', [FirstDocumentController::class, 'generateAndDownload'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.first-document.download');

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/checklist', [ChecklistController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.checklist');
            Route::post('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/checklist', [ChecklistController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/overview', [\App\Http\Controllers\PharmacyInhalationTechnique\OverviewController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.overview');
            Route::post('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/overview', [\App\Http\Controllers\PharmacyInhalationTechnique\OverviewController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/abort/{reasons}', [\App\Http\Controllers\PharmacyInhalationTechnique\AbortController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.abort');

            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/finished', [\App\Http\Controllers\PharmacyInhalationTechnique\FinishedController::class, 'edit'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.finished');
            Route::get('/pharmacies/{pharmacy}/services/inhalation-technique/{pharmaceuticalService}/download-result', [\App\Http\Controllers\PharmacyInhalationTechnique\FinishedController::class, 'generateAndDownload'])->name('pharmacies.pharmaceutical-services.inhalation-techniques.download-result');
        });

        Route::middleware('ensure-subscribed:'.VaccinationFeatureAccess::class)->group(function () {
            Route::get('/pharmacies/{pharmacy}/vaccinate', [PharmacyVaccinateController::class, 'index'])->name('pharmacies.vaccinate.index');

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/invoice', [PharmacyInfluenzaVaccinateInvoiceController::class, 'index'])->name('pharmacies.vaccinate-influenza.invoice.index');
            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/invoice/{influenzaVaccinationInvoice}/download', [PharmacyInfluenzaVaccinateInvoiceController::class, 'download'])->name('pharmacies.vaccinate-influenza.invoice.download');

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza', [PharmacyInfluenzaVaccinateController::class, 'index'])->name('pharmacies.vaccinate');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza', [PharmacyInfluenzaVaccinateController::class, 'start']);
            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/request', [PharmacyInfluenzaVaccinateController::class, 'viewRequest'])->name('pharmacies.vaccinate.request');
            Route::put('/pharmacies/{pharmacy}/vaccinate-influenza/request', [PharmacyInfluenzaVaccinateController::class, 'request']);
            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}', [PharmacyInfluenzaVaccinateController::class, 'edit'])->name('pharmacies.vaccinate.edit');
        });

        Route::get('/login-from-extern', fn () => false)->name('loginFromExtern');

        Route::middleware('ensure-subscribed:'.VaccinationCertificateFeatureAccess::class)->group(function () {
            // TODO: Remove when working on AP-1057
            if (isCovidVaccinationCertificateCenterActive()) {
                Route::get('/pharmacies/{pharmacy}/import-vaccination/redirect', [PharmacyImportVaccinationController::class, 'redirect'])->name('pharmacies.importVaccination.redirect');
            }

            Route::get('/pharmacies/{pharmacy}/import-vaccination', [PharmacyImportVaccinationController::class, 'index'])->name('pharmacies.importVaccination');
            Route::get('/pharmacies/{pharmacy}/import-vaccination/accounting', [PharmacyImportVaccinationAccountingController::class, 'index'])->name('pharmacies.importVaccination.accounting');
            Route::get('/pharmacies/{pharmacy}/import-vaccination/accounting/{invoice}/download', [PharmacyImportVaccinationAccountingController::class, 'download'])->name('pharmacies.importVaccination.accounting.download');
            Route::get('/pharmacies/{pharmacy}/import-vaccination/create', fn ($pharmacy) => redirect(linkToVaccinationImportPortal(route('pharmacies.importVaccination.create', $pharmacy, false))))->name('pharmacies.importVaccination.create');
            Route::put('/pharmacies/{pharmacy}/import-vaccination/update-pharmacy', [PharmacyImportVaccinationController::class, 'updatePharmacy'])->name('pharmacies.importVaccination.updatePharmacy');
            Route::get('/pharmacies/{pharmacy}/import-vaccination/{vaccinationImport}/certificate', [PharmacyImportVaccinationController::class, 'certificate'])->name('pharmacies.importVaccination.certificate');
            Route::put('/pharmacies/{pharmacy}/import-vaccination/request-recovered-certificate', [PharmacyImportVaccinationController::class, 'requestRecoveredCertificate'])->name('pharmacies.importVaccination.requestRecoveredCertificate');
        });

        Route::get('/pharmacies/{pharmacy}/feature-requirements/{feature}/{requirement}', [FeatureRequirementsController::class, 'requirements'])->name('feature-requirements');
        Route::middleware('feature-sdr')->group(function () {
            Route::get('/pharmacies/{pharmacy}/sdr/groups', [DocspaceGroupController::class, 'index'])->name('sdr.groups');
            Route::get('/pharmacies/{pharmacy}/sdr/docspaces', [DocspaceController::class, 'index'])->name('sdr.doc-spaces');
            Route::get('/pharmacies/{pharmacy}/sdr/docspaces/create', [DocspaceController::class, 'create'])->name('sdr.doc-spaces.create');
            Route::get('/pharmacies/{pharmacy}/sdr/docspaces/{docSpace}', [DocspaceController::class, 'edit'])->name('sdr.doc-spaces.show');
            Route::post('/pharmacies/{pharmacy}/sdr/create-guided', [DocspaceController::class, 'storeGuidedProcess'])->withoutMiddleware('feature-sdr')->name('sdr.create-guided');
            Route::get('/pharmacies/{pharmacy}/sdr/create-guided-progress/{restart?}', [DocspaceController::class, 'createGuided'])->name('sdr.create-guided-progress');
        });

        Route::middleware(['vaccinate-form', 'ensure-subscribed:'.VaccinationFeatureAccess::class])->group(function () {
            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/health-insurance-company', [PharmacyInfluenzaVaccinationHealthInsuranceController::class, 'edit'])->name('pharmacies.vaccinate.healthInsuranceCompany');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/health-insurance-company', [PharmacyInfluenzaVaccinationHealthInsuranceController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/personal-data', [PharmacyInfluenzaVaccinationPersonalDataController::class, 'edit'])->name('pharmacies.vaccinate.personalData');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/personal-data', [PharmacyInfluenzaVaccinationPersonalDataController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/educational', [PharmacyInfluenzaVaccinationEducationalController::class, 'edit'])->name('pharmacies.vaccinate.educational');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/educational', [PharmacyInfluenzaVaccinationEducationalController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/explanation', [PharmacyInfluenzaVaccinationExplanationController::class, 'edit'])->name('pharmacies.vaccinate.explanation');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/explanation', [PharmacyInfluenzaVaccinationExplanationController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/stiko', [PharmacyInfluenzaVaccinationStikoController::class, 'edit'])->name('pharmacies.vaccinate.stiko');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/stiko', [PharmacyInfluenzaVaccinationStikoController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/acceptance', [PharmacyInfluenzaVaccinationAcceptanceController::class, 'edit'])->name('pharmacies.vaccinate.acceptance');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/acceptance', [PharmacyInfluenzaVaccinationAcceptanceController::class, 'update'])->name('pharmacies.vaccinate.acceptance-update');

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/documentation', [PharmacyInfluenzaVaccinationDocumentationController::class, 'edit'])->name('pharmacies.vaccinate.documentation');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/documentation', [PharmacyInfluenzaVaccinationDocumentationController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/evaluation-show', [PharmacyInfluenzaVaccinationEvaluationShowController::class, 'show'])->name('pharmacies.vaccinate.eval-show');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/evaluation-show', [PharmacyInfluenzaVaccinationEvaluationShowController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/evaluation-user', [PharmacyInfluenzaVaccinationEvaluationUserController::class, 'edit'])->name('pharmacies.vaccinate.eval-user');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/evaluation-user', [PharmacyInfluenzaVaccinationEvaluationUserController::class, 'store']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/evaluation-pharmacy', [PharmacyInfluenzaVaccinationEvaluationPharmacyController::class, 'edit'])->name('pharmacies.vaccinate.eval-pharmacy');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/evaluation-pharmacy', [PharmacyInfluenzaVaccinationEvaluationPharmacyController::class, 'store']);
        });

        Route::middleware('ensure-subscribed:'.VaccinationFeatureAccess::class)->group(function () {
            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/', [PharmacyCovidVaccinateController::class, 'index'])->name('pharmacies.vaccinate-covid.index');
            Route::post('/pharmacies/{pharmacy}/vaccinate-covid/', [PharmacyCovidVaccinateController::class, 'start'])->name('pharmacies.vaccinate-covid.start');
            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/edit', [PharmacyCovidVaccinateController::class, 'edit'])->name('pharmacies.vaccinate-covid.edit');

            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/start', [StartController::class, 'edit'])->name('pharmacies.vaccinate-covid.start-information');
            Route::post('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/start', [StartController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/personal-data', [PersonalDataController::class, 'edit'])->name('pharmacies.vaccinate-covid.personal-data');
            Route::post('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/personal-data', [PersonalDataController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/vaccination-data', [VaccinationDataController::class, 'edit'])->name('pharmacies.vaccinate-covid.vaccination-data');
            Route::post('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/vaccination-data', [VaccinationDataController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/overview', [OverviewController::class, 'edit'])->name('pharmacies.vaccinate-covid.overview');
            Route::post('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/overview', [OverviewController::class, 'update']);

            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/finished', [FinishedController::class, 'edit'])->name('pharmacies.vaccinate-covid.finished');

            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/{vaccination}/abort/{reasons}', [AbortController::class, 'edit'])->name('pharmacies.vaccinate-covid.abort');

            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/invoice', [PharmacyCovidVaccinateInvoiceController::class, 'index'])->name('pharmacies.vaccinate-covid.invoice.index');
            Route::get('/pharmacies/{pharmacy}/vaccinate-covid/invoice/{covidVaccinationInvoice}/download', [PharmacyCovidVaccinateInvoiceController::class, 'download'])->name('pharmacies.vaccinate-covid.invoice.download');

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/edit-later', [PharmacyInfluenzaVaccinationEditController::class, 'edit'])->name('pharmacies.vaccinate.edit-later');
            Route::post('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/edit-later', [PharmacyInfluenzaVaccinationEditController::class, 'store'])->name('pharmacies.vaccinate.edit-later-store');

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/finished', [PharmacyInfluenzaVaccinationFinishedController::class, 'show'])->name('pharmacies.vaccinate.finished');

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/download/explanation-influenza-vaccination', [PharmacyInfluenzaVaccinationDownloadController::class, 'explanationfluShot'])->name('pharmacies.vaccinate.document.explanationfluShot');
            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/download/documentation-influenza-vaccination', [PharmacyInfluenzaVaccinationDownloadController::class, 'documentationfluShot'])->name('pharmacies.vaccinate.document.documentationfluShot');
            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/download/replacement-form-vaccination', [PharmacyInfluenzaVaccinationDownloadController::class, 'replacementFormVaccination'])->name('pharmacies.vaccinate.document.replacementFormVaccination');

            Route::get('/pharmacies/{pharmacy}/vaccinate-influenza/{vaccination}/abort/{reasons}', [PharmacyInfluenzaVaccinationAbortController::class, 'abort'])->name('pharmacies.vaccinate.abort');
        });

        Route::post('/files/upload-temp', [FileController::class, 'uploadTemp']);
        Route::get('/files/temp/{filename}', [FileController::class, 'getTemp']);
        Route::get('/pharmacy/approvals/image/{approvableChange}', [PharmacyImageController::class, 'approval'])->name('approvals.image.show');

        Route::get('/associations/edit', [AssociationController::class, 'edit'])->name('associations.edit');
        Route::put('/associations/edit', [AssociationController::class, 'update']);
        Route::get('/associations/overview', [AssociationController::class, 'overview'])->name('associations.overview');
        Route::get('/associations/vaccination/show', [AssociationVaccinateController::class, 'show'])->name('associations.vaccination');
        Route::post('/associations/vaccination/show', [AssociationVaccinateController::class, 'switchStatus']);
        Route::get('/associations/vaccination', [AssociationVaccinateController::class, 'index'])->name('associations.vaccination.index');
        Route::get('/associations/vaccination/export', [AssociationVaccinateController::class, 'export'])->name('associations.vaccination.export');
        Route::get('/associations/vaccination/approval', [AssociationVaccinateController::class, 'approval'])->name('associations.vaccination.approval');
        Route::get('/associations/vaccination/health-insurance-companies', [AssociationVaccinateController::class, 'healthInsuranceCompanies'])->name('associations.vaccination.healthInsuranceCompanies');
        Route::get('/associations/vaccination/pharmacies', [AssociationVaccinateController::class, 'pharmacies'])->name('associations.vaccination.pharmacies');
        Route::get('/associations/news', ListAssociationNews::class)->name('associations.news.index');
        Route::get('/associations/news/create', CreateAssociationNews::class)->name('associations.news.create');
        Route::get('/associations/news/{news:slug}/edit', EditAssociationNews::class)->name('associations.news.edit');

        Route::get('/associations/members', [AssociationMemberController::class, 'index'])->name('associations.members.index');

        Route::get('/associations/users', [AssociationUserController::class, 'index'])->name('associations.users.index');
        Route::get('/associations/users/create', [AssociationUserController::class, 'create'])->name('associations.users.create');
        Route::post('/associations/users/store', [AssociationUserController::class, 'store'])->name('associations.users.store');
        Route::get('/associations/users/{user}/edit', [AssociationUserController::class, 'edit'])->name('associations.users.edit');
        Route::put('/associations/users/{user}/update', [AssociationUserController::class, 'update'])->name('associations.users.update');

        Route::get('/associations/health-insurance-companies', [AssociationController::class, 'healthInsuranceCompanies'])->name('associations.healthInsuranceCompanies');

        Route::get('/ti-tester', [TiTesterController::class, 'redirect']);
        Route::get('/ti-tester/{pharmacy}', [TiTesterController::class, 'run'])->name('ti-tester');
        Route::post('/ti-tester/response', [TiTesterController::class, 'response']);

        Route::prefix('/shift-plans')->middleware(['ensure-subscribed:'.ShiftPlanFeatureAccess::class])->group(function () {
            Route::get('/', [ShiftPlanController::class, 'index'])->name('shiftplans.index');
            Route::get('/{shiftPlan:uuid}', [ShiftPlanController::class, 'view'])->name('shiftplans.view');
            Route::get('/{shiftPlan:uuid}/print/{cw}/{y}', [ShiftPlanController::class, 'printShiftPlan'])->name('shiftplan.print');
        });

        Route::get('/apoguide-shop', [ApoguideController::class, 'shop'])->name('apoguide-shop');
    });

    Route::prefix('/online-shop')->name('ia.')->group(function () {
        Route::get('activate', IaActivationRedirectController::class)->name('entrypoint');

        // IA Preorder Form (Remove when IA is fully implemented)
        Route::get('/', App\Livewire\Ia\Activate::class)->name('ihre-apotheken.index');
        Route::get('/order', App\Livewire\Ia\IaOrderFlow\Order::class)->name('ihre-apotheken.order');
        Route::get('/success', App\Livewire\Ia\IaOrderFlow\Success::class)->name('ihre-apotheken.success');
    });
});

// Backoffice Routes
Route::middleware('auth:staff')->group(function () {
    Route::get('/nova/pharmacies/{pharmacy}/logo', [PharmacyImageController::class, 'logo'])->name('nova.pharmacies.logo');
    Route::get('/nova/pharmacies/{pharmacy}/image', [PharmacyImageController::class, 'image'])->name('nova.pharmacies.image');

    Route::get('/nova/import-invoice/{invoice}/download', [NovaFileController::class, 'vaccinationImportInvoiceDownload'])->name('nova.vaccination-import-invoice.download');
    Route::get('/nova/covid-vaccination-invoice/{invoice}/download', [NovaFileController::class, 'CovidVaccinationInvoiceDownload'])->name('nova.covid-vaccination-invoice.download');
    Route::get('nova/pharmacies/{pharmacy}/download/apoguide-qr-code', [PharmacyDownloadApoguideQrCodeController::class, 'downloadApoguideQRCodeForNova'])->name('pharmacies.apoguide-qr-code-for-nova');
    Route::get('nova/pharmacies/{pharmacy}/download/apoguide-poster', [PharmacyDownloadApoguideQrCodeController::class, 'downloadApoguidePosterForNova'])->name('pharmacies.apoguide-poster-for-nova');

    Route::get('/nova/import-invoice/{invoice}/download', [NovaFileController::class, 'vaccinationImportInvoiceDownload'])->name('nova.vaccination-import-invoice.download');
    Route::get('/nova/covid-vaccination-invoice/{invoice}/download', [NovaFileController::class, 'CovidVaccinationInvoiceDownload'])->name('nova.covid-vaccination-invoice.download');

    Route::get('/approvals/image/{approvableChange}', [PharmacyImageApprovalController::class, 'show'])->name('nova.approvals.image.show');
    Route::get('/pharmacies/logo/{pharmacy}', [PharmacyImageNovaController::class, 'logo'])->name('nova.pharmacies.image.logo');
    Route::get('/pharmacies/image/{pharmacy}', [PharmacyImageNovaController::class, 'image'])->name('nova.pharmacies.image.show');

    Route::prefix('/nova/registration-request/{registrationRequest}/download')->name('nova.registration-request.download.')->group(function () {
        Route::get('/operating-license', [RegistrationRequestController::class, 'downloadOperatingLicense'])
            ->name('operating-license');

        Route::get('/activity-certificate', [RegistrationRequestController::class, 'downloadActivityCertificate'])
            ->name('activity-certificate');

        Route::get('/association-proof', [RegistrationRequestController::class, 'downloadAssociationProof'])
            ->name('association-proof');
    });

    Route::get('/nova/nova-sql-query/download/{novaSqlQuery:id}', [NovaFileController::class, 'downloadNovaSqlQueryResult'])->name('nova.nova-sql-query.download');
});

// Public pharmacy image route
Route::get('/public/pharmacy/{pharmacyImage}', [PublicPharmacyImageController::class, 'show'])->name('public.pharmacy-image.show');

Route::get('/.well-known/change-password', [RedirectController::class, 'passwordChange']);

Route::get('/uploads/{upload:slug}', UploadController::class)->name('uploads.show');
