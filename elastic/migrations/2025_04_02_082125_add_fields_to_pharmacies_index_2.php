<?php

declare(strict_types=1);

use Elastic\Adapter\Indices\Mapping;
use Elastic\Migrations\Facades\Index;
use Elastic\Migrations\MigrationInterface;

final class AddFieldsToPharmaciesIndex2 implements MigrationInterface
{
    /**
     * Run the migration.
     */
    public function up(): void
    {
        Index::putMapping('pharmacies_index', function (Mapping $mapping) {
            $mapping->text('main_pharmacy');
            $mapping->text('association_name');
            $mapping->text('t_id');
            $mapping->text('ik_number');
            $mapping->text('street');
            $mapping->text('house_number');
            $mapping->text('optional_address_line');

            $mapping->object('owner', [
                'properties' => [
                    'title' => [
                        'type' => 'text',
                    ],
                    'salutation' => [
                        'type' => 'text',
                    ],
                    'first_name' => [
                        'type' => 'text',
                    ],
                    'last_name' => [
                        'type' => 'text',
                    ],
                ],
            ]);

            $mapping->boolean('uses_retax');
            $mapping->text('docspace_id');
        });
    }

    /**
     * Reverse the migration.
     */
    public function down(): void
    {
        //
    }
}
