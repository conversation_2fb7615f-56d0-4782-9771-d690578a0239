<?php

// @formatter:off
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. <PERSON>l <<EMAIL>>
 */

// @formatter:off
/**
 * A helper file for your Eloquent Models
 * Copy the phpDocs from this file to the correct Model,
 * And remove them from this file, to prevent double declarations.
 *
 * <AUTHOR> vd. He<PERSON>l <<EMAIL>>
 */

namespace App{
    /**
     * App\AccountingCenter
     *
     * @mixin IdeHelperAccountingCenter
     *
     * @property int $id
     * @property string|null $company
     * @property string|null $system_id
     * @property string|null $tag
     * @property string|null $city
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Pharmacy[] $pharmacies
     * @property-read int|null $pharmacies_count
     *
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter query()
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter whereCity($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter whereCompany($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter whereSystemId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter whereTag($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AccountingCenter whereUpdatedAt($value)
     */
    class IdeHelperAccountingCenter extends \Eloquent {}
}

namespace App{
    /**
     * App\ApprovableChange
     *
     * @mixin IdeHelperApprovableChange
     *
     * @property int $id
     * @property string $attribute
     * @property string $type
     * @property array $change
     * @property int $pharmacy_id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Pharmacy $pharmacy
     *
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange query()
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange whereAttribute($value)
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange whereChange($value)
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|ApprovableChange whereUpdatedAt($value)
     */
    class IdeHelperApprovableChange extends \Eloquent {}
}

namespace App{
    /**
     * Class Association
     *
     * @mixin IdeHelperAssociation
     *
     * @property int $id
     * @property string $name
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property string|null $street
     * @property string|null $house_number
     * @property string|null $optional_address_line
     * @property string|null $postcode
     * @property string|null $city
     * @property string|null $privacy_person_name
     * @property-read Collection|\App\User[] $assignedUsers
     * @property-read int|null $assigned_users_count
     * @property-read Collection|\App\AssociationNews[] $associationNews
     * @property-read int|null $association_news_count
     * @property-read Collection|\App\BrochureCode[] $brochureCodes
     * @property-read int|null $brochure_codes_count
     * @property-read Collection|\App\HealthInsuranceCompany[] $healthInsuranceCompanies
     * @property-read int|null $health_insurance_companies_count
     * @property-read \App\AssociationSetting|null $settings
     * @property-read Collection|\App\UserAssociationProfile[] $userAssociationProfiles
     * @property-read int|null $user_association_profiles_count
     * @property-read Collection|\App\UserPharmacyProfile[] $userPharmacyProfiles
     * @property-read int|null $user_pharmacy_profiles_count
     * @property-read Collection|\App\Vaccination[] $vaccinations
     * @property-read int|null $vaccinations_count
     *
     * @method static \Database\Factories\AssociationFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Association newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Association newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Association query()
     * @method static \Illuminate\Database\Eloquent\Builder|Association whereCity($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association whereHouseNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association wherePostcode($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association wherePrivacyPersonName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association whereStreet($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Association whereUpdatedAt($value)
     */
    class IdeHelperAssociation extends \Eloquent {}
}

namespace App{
    /**
     * Class AssociationNews
     *
     * @mixin IdeHelperAssociationNews
     *
     * @property int $id
     * @property string $slug
     * @property string $title
     * @property string $excerpt
     * @property string $text
     * @property int $status
     * @property \Illuminate\Support\Carbon $release_date
     * @property int $association_id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Association $association
     * @property-read mixed $content
     * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection|Media[] $media
     * @property-read int|null $media_count
     *
     * @method static \Database\Factories\AssociationNewsFactory factory(...$parameters)
     * @method static Builder|AssociationNews newModelQuery()
     * @method static Builder|AssociationNews newQuery()
     * @method static Builder|AssociationNews query()
     * @method static Builder|AssociationNews released()
     * @method static Builder|AssociationNews whereAssociationId($value)
     * @method static Builder|AssociationNews whereCreatedAt($value)
     * @method static Builder|AssociationNews whereExcerpt($value)
     * @method static Builder|AssociationNews whereId($value)
     * @method static Builder|AssociationNews whereReleaseDate($value)
     * @method static Builder|AssociationNews whereSlug($value)
     * @method static Builder|AssociationNews whereStatus($value)
     * @method static Builder|AssociationNews whereText($value)
     * @method static Builder|AssociationNews whereTitle($value)
     * @method static Builder|AssociationNews whereUpdatedAt($value)
     */
    class IdeHelperAssociationNews extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App{
    /**
     * App\AssociationRoleUser
     *
     * @mixin IdeHelperAssociationRoleUser
     *
     * @property int $user_id
     * @property int $association_id
     * @property string $role_name
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property array|null $permissions
     *
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser query()
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser wherePermissions($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser whereRoleName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationRoleUser whereUserId($value)
     */
    class IdeHelperAssociationRoleUser extends \Eloquent {}
}

namespace App{
    /**
     * Class AssociationSetting
     *
     * @mixin IdeHelperAssociationSetting
     *
     * @property int $association_id
     * @property int $can_vaccinate
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Association $association
     *
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationSetting newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationSetting newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationSetting query()
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationSetting whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationSetting whereCanVaccinate($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationSetting whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|AssociationSetting whereUpdatedAt($value)
     */
    class IdeHelperAssociationSetting extends \Eloquent {}
}

namespace App{
    /**
     * Class Author
     *
     * @mixin IdeHelperAuthor
     *
     * @property int $id
     * @property string $slug
     * @property int $salutation
     * @property string|null $title
     * @property string $first_name
     * @property string $last_name
     * @property string $email
     * @property string|null $website
     * @property string|null $description
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read string $full_name
     * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection|Media[] $media
     * @property-read int|null $media_count
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\News[] $news
     * @property-read int|null $news_count
     *
     * @method static \Database\Factories\AuthorFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Author newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Author newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Author query()
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereDescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereFirstName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereLastName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereSalutation($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereSlug($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereTitle($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Author whereWebsite($value)
     */
    class IdeHelperAuthor extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App{
    /**
     * Class BrochureCode
     *
     * @mixin IdeHelperBrochureCode
     *
     * @property int $id
     * @property string|null $code
     * @property int|null $user_id
     * @property int|null $association_id
     * @property string|null $last_export
     * @property string|null $salutation
     * @property string|null $name_affix
     * @property string|null $first_name
     * @property string|null $last_name
     * @property string|null $pharmacy_name
     * @property string|null $street
     * @property string|null $optional_address_line
     * @property string|null $postcode
     * @property string|null $city
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Nova\Actions\ActionEvent[] $actions
     * @property-read int|null $actions_count
     * @property-read \App\Association|null $association
     * @property-read \App\User|null $user
     *
     * @method static \Database\Factories\BrochureCodeFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode findForCode($code)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode query()
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereCity($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereCode($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereFirstName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereLastExport($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereLastName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereNameAffix($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode wherePharmacyName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode wherePostcode($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereSalutation($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereStreet($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BrochureCode whereUserId($value)
     */
    class IdeHelperBrochureCode extends \Eloquent {}
}

namespace App{
    /**
     * Class BusinessHour
     *
     * @mixin IdeHelperBusinessHour
     *
     * @property int $id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property int $pharmacy_id
     * @property int $day_of_week
     * @property string $opens
     * @property string $closes
     * @property-read \App\Pharmacy $pharmacy
     *
     * @method static \Database\Factories\BusinessHourFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour query()
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour whereCloses($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour whereDayOfWeek($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour whereOpens($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|BusinessHour whereUpdatedAt($value)
     */
    class IdeHelperBusinessHour extends \Eloquent {}
}

namespace App{
    /**
     * Class Category
     *
     * @mixin IdeHelperCategory
     *
     * @property int $id
     * @property string $slug
     * @property string $title
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read Collection|\App\News[] $news
     * @property-read int|null $news_count
     *
     * @method static \Database\Factories\CategoryFactory factory(...$parameters)
     * @method static Builder|Category forFilter($type = null)
     * @method static Builder|Category newModelQuery()
     * @method static Builder|Category newQuery()
     * @method static Builder|Category query()
     * @method static Builder|Category whereCreatedAt($value)
     * @method static Builder|Category whereId($value)
     * @method static Builder|Category whereSlug($value)
     * @method static Builder|Category whereTitle($value)
     * @method static Builder|Category whereUpdatedAt($value)
     */
    class IdeHelperCategory extends \Eloquent {}
}

namespace App{
    /**
     * Class CompanyUser
     *
     * @mixin IdeHelperCompanyUser
     *
     * @property int $user_id
     * @property string $name
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\User $user
     *
     * @method static \Illuminate\Database\Eloquent\Builder|CompanyUser newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|CompanyUser newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|CompanyUser query()
     * @method static \Illuminate\Database\Eloquent\Builder|CompanyUser whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|CompanyUser whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|CompanyUser whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|CompanyUser whereUserId($value)
     */
    class IdeHelperCompanyUser extends \Eloquent {}
}

namespace App{use App\Enums\Kim\FailedKimReportStatus;

    /**
     * Class FailedKimReport
     *
     * @property FailedKimReportStatus $status
     * @property-read \App\Pharmacy|null $pharmacy
     *
     * @method static \Illuminate\Database\Eloquent\Builder|FailedKimReport newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|FailedKimReport newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|FailedKimReport query()
     */
    class IdeHelperFailedKim extends \Eloquent {}
}

namespace App{
    /**
     * Class FocusArea
     *
     * @mixin IdeHelperFocusArea
     *
     * @property int $id
     * @property string $name
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read Collection|\App\Pharmacy[] $pharmacies
     * @property-read int|null $pharmacies_count
     *
     * @method static \Database\Factories\FocusAreaFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|FocusArea newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|FocusArea newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|FocusArea query()
     * @method static \Illuminate\Database\Eloquent\Builder|FocusArea whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|FocusArea whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|FocusArea whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|FocusArea whereUpdatedAt($value)
     */
    class IdeHelperFocusArea extends \Eloquent {}
}

namespace App{
    /**
     * App\FocusAreaPharmacy
     *
     * @mixin IdeHelperFocusAreaPharmacy
     *
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property int $pharmacy_id
     * @property int $focus_area_id
     *
     * @method static \Illuminate\Database\Eloquent\Builder|FocusAreaPharmacy newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|FocusAreaPharmacy newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|FocusAreaPharmacy query()
     * @method static \Illuminate\Database\Eloquent\Builder|FocusAreaPharmacy whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|FocusAreaPharmacy whereFocusAreaId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|FocusAreaPharmacy wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|FocusAreaPharmacy whereUpdatedAt($value)
     */
    class IdeHelperFocusAreaPharmacy extends \Eloquent {}
}

namespace App{
    /**
     * App\GoodsManagementSystem
     *
     * @mixin IdeHelperGoodsManagementSystem
     *
     * @property int $id
     * @property string|null $company
     * @property string|null $system_id
     * @property string|null $system_name
     * @property string|null $tag
     * @property string|null $city
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Pharmacy[] $pharmacies
     * @property-read int|null $pharmacies_count
     *
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem query()
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereCity($value)
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereCompany($value)
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereSystemId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereSystemName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereTag($value)
     * @method static \Illuminate\Database\Eloquent\Builder|GoodsManagementSystem whereUpdatedAt($value)
     */
    class IdeHelperGoodsManagementSystem extends \Eloquent {}
}

namespace App{
    /**
     * Class HealthInsuranceCompany
     *
     * @mixin IdeHelperHealthInsuranceCompany
     *
     * @property int $id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property string $name
     * @property int $vaccinate_enabled
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Association[] $associations
     * @property-read int|null $associations_count
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Vaccination[] $vaccinations
     * @property-read int|null $vaccinations_count
     *
     * @method static \Database\Factories\HealthInsuranceCompanyFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany query()
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|HealthInsuranceCompany whereVaccinateEnabled($value)
     */
    class IdeHelperHealthInsuranceCompany extends \Eloquent {}
}

namespace App{
    /**
     * Class InfluenzaVaccination
     *
     * @mixin IdeHelperInfluenzaVaccination
     *
     * @property int $vaccination_id
     * @property int|null $stiko_health_related
     * @property int|null $stiko_job_related
     * @property array|null $severe_vaccination_reactions
     * @property int|null $emergency_measures_initiated
     * @property array|null $poll_vaccinated_before
     * @property array|null $poll_where_found_out
     * @property string|null $poll_where_found_out_other
     * @property int|null $poll_had_alternative
     * @property array|null $poll_why_pharmacy
     * @property string|null $poll_why_pharmacy_other
     * @property int|null $poll_rate_information
     * @property int|null $poll_rate_satisfaction
     * @property int|null $poll_rate_do_again
     * @property int|null $poll_rate_others_too
     * @property string|null $comments
     * @property int $notify_user
     * @property int|null $patient_has_illness
     * @property int|null $patient_has_illness_vaccination_possible
     * @property int|null $patient_has_allergy
     * @property int|null $patient_has_allergy_vaccination_possible
     * @property int|null $patient_had_reaction
     * @property int|null $patient_had_reaction_vaccination_possible
     * @property int|null $patient_has_operation
     * @property int|null $patient_has_operation_vaccination_possible
     * @property int|null $patient_takes_marcumar
     * @property int|null $patient_is_pregnant
     * @property int $patient_no_further_questions
     * @property int $patient_consent
     * @property int $patient_not_consent
     * @property int|null $vaccine_correct_color
     * @property int|null $vaccine_free_of_particles
     * @property string|null $allergy
     * @property array|null $psn
     * @property bool|null $high_dose_vaccine_bottleneck
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property array|null $belated_reactions
     * @property-read \App\Vaccination $vaccination
     *
     * @method static \Database\Factories\InfluenzaVaccinationFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination query()
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereAllergy($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereBelatedReactions($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereComments($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereEmergencyMeasuresInitiated($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereNotifyUser($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientConsent($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHadReaction($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHadReactionVaccinationPossible($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHasAllergy($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHasAllergyVaccinationPossible($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHasIllness($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHasIllnessVaccinationPossible($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHasOperation($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientHasOperationVaccinationPossible($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientIsPregnant($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientNoFurtherQuestions($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientNotConsent($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePatientTakesMarcumar($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollHadAlternative($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollRateDoAgain($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollRateInformation($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollRateOthersToo($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollRateSatisfaction($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollVaccinatedBefore($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollWhereFoundOut($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollWhereFoundOutOther($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollWhyPharmacy($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination wherePollWhyPharmacyOther($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereSevereVaccinationReactions($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereStikoHealthRelated($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereStikoJobRelated($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereVaccinationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereVaccineCorrectColor($value)
     * @method static \Illuminate\Database\Eloquent\Builder|InfluenzaVaccination whereVaccineFreeOfParticles($value)
     */
    class IdeHelperInfluenzaVaccination extends \Eloquent {}
}

namespace App{
    /**
     * App\KimAddress
     *
     * @property array<string, string>|null $additional
     * @property int $id
     * @property string $email
     * @property string|null $order_number
     * @property string|null $vendor
     * @property string $status
     * @property \App\Enums\KimAddressReportStatus|null $report_status
     * @property int $notification_count
     * @property string|null $ngda_notify_at
     * @property int|null $pharmacy_id
     * @property int|null $billing_address_id
     * @property string|null $cancellation_id
     * @property string|null $cancellation_reason
     * @property string|null $deletion_reason
     * @property array|null $log
     * @property \Illuminate\Support\Carbon|null $reserved_at
     * @property \Illuminate\Support\Carbon|null $ordered_at
     * @property \Illuminate\Support\Carbon|null $activated_at
     * @property \Illuminate\Support\Carbon|null $deactivated_at
     * @property \Illuminate\Support\Carbon|null $reported_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $deleted_at
     * @property-read \App\BillingAddress|null $billingAddress
     * @property-read \App\Pharmacy|null $pharmacy
     *
     * @method static \Database\Factories\KimAddressFactory factory($count = null, $state = [])
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress onlyTrashed()
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress query()
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereActivatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereAdditional($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereBillingAddressId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereCancellationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereCancellationReason($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereDeactivatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereDeletedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereDeletionReason($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereLog($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereNgdaNotifyAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereNotificationCount($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereOrderNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereOrderedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereReportStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereReportedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereReservedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress whereVendor($value)
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress withTrashed()
     * @method static \Illuminate\Database\Eloquent\Builder|KimAddress withoutTrashed()
     *
     * @mixin \Eloquent
     */
    class IdeHelperKimAddress extends \Eloquent {}
}

namespace App{
    /**
     * Class Language
     *
     * @mixin IdeHelperLanguage
     *
     * @property int $id
     * @property string $code
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read Collection|\App\Pharmacy[] $pharmacies
     * @property-read int|null $pharmacies_count
     *
     * @method static \Database\Factories\LanguageFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Language newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Language newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Language query()
     * @method static \Illuminate\Database\Eloquent\Builder|Language whereCode($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Language whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Language whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Language whereUpdatedAt($value)
     */
    class IdeHelperLanguage extends \Eloquent {}
}

namespace App{
    /**
     * App\LanguagePharmacy
     *
     * @mixin IdeHelperLanguagePharmacy
     *
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property int $pharmacy_id
     * @property int $language_id
     *
     * @method static \Illuminate\Database\Eloquent\Builder|LanguagePharmacy newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|LanguagePharmacy newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|LanguagePharmacy query()
     * @method static \Illuminate\Database\Eloquent\Builder|LanguagePharmacy whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|LanguagePharmacy whereLanguageId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|LanguagePharmacy wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|LanguagePharmacy whereUpdatedAt($value)
     */
    class IdeHelperLanguagePharmacy extends \Eloquent {}
}

namespace App\Misc{
    /**
     * App\Misc\PassportClient
     *
     * @mixin IdeHelperPassportClient
     *
     * @property string $id
     * @property int|null $user_id
     * @property string $name
     * @property string|null $secret
     * @property string|null $provider
     * @property array|null $allowed_scopes
     * @property string $redirect
     * @property bool $personal_access_client
     * @property bool $password_client
     * @property bool $revoked
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property int $requests_per_minute
     * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Passport\AuthCode[] $authCodes
     * @property-read int|null $auth_codes_count
     * @property-read string|null $plain_secret
     * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Passport\Token[] $tokens
     * @property-read int|null $tokens_count
     * @property-read \App\User|null $user
     *
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient query()
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereAllowedScopes($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient wherePasswordClient($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient wherePersonalAccessClient($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereProvider($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereRedirect($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereRequestsPerMinute($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereRevoked($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereSecret($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PassportClient whereUserId($value)
     */
    class IdeHelperPassportClient extends \Eloquent {}
}

namespace App{
    /**
     * Class News
     *
     * @mixin IdeHelperNews
     *
     * @property int $id
     * @property string $slug
     * @property string $title
     * @property string $excerpt
     * @property string $text
     * @property string $seo_title
     * @property string $seo_description
     * @property int $author_id
     * @property int $status
     * @property \Illuminate\Support\Carbon $release_date
     * @property bool $visible_extern
     * @property bool $visible_intern
     * @property bool $with_login_wall
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Author $author
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Category[] $categories
     * @property-read int|null $categories_count
     * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection|Media[] $media
     * @property-read int|null $media_count
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Tag[] $tags
     * @property-read int|null $tags_count
     *
     * @method static \Database\Factories\NewsFactory factory(...$parameters)
     * @method static Builder|News newModelQuery()
     * @method static Builder|News newQuery()
     * @method static Builder|News public()
     * @method static Builder|News query()
     * @method static Builder|News released()
     * @method static Builder|News visibility($type = null)
     * @method static Builder|News whereAuthorId($value)
     * @method static Builder|News whereCreatedAt($value)
     * @method static Builder|News whereExcerpt($value)
     * @method static Builder|News whereId($value)
     * @method static Builder|News whereReleaseDate($value)
     * @method static Builder|News whereSeoDescription($value)
     * @method static Builder|News whereSeoTitle($value)
     * @method static Builder|News whereSlug($value)
     * @method static Builder|News whereStatus($value)
     * @method static Builder|News whereText($value)
     * @method static Builder|News whereTitle($value)
     * @method static Builder|News whereUpdatedAt($value)
     * @method static Builder|News whereVisibleExtern($value)
     * @method static Builder|News whereVisibleIntern($value)
     * @method static Builder|News whereWithLoginWall($value)
     */
    class IdeHelperNews extends \Eloquent implements \Spatie\MediaLibrary\HasMedia {}
}

namespace App{
    /**
     * Class Pharmaceutical
     *
     * @mixin IdeHelperPharmaceutical
     *
     * @property int $id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property string $name
     * @property int $pzn
     * @property int $active
     * @property-read Collection|\App\Vaccination[] $vaccinations
     * @property-read int|null $vaccinations_count
     *
     * @method static \Database\Factories\PharmaceuticalFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical query()
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical whereActive($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical wherePzn($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmaceutical whereUpdatedAt($value)
     */
    class IdeHelperPharmaceutical extends \Eloquent {}
}

namespace App{
    /**
     * Class Pharmacy
     *
     * @mixin IdeHelperPharmacy
     *
     * @property int $id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property string $name
     * @property string|null $institute_id
     * @property string|null $n_id
     * @property string|null $pharmacy_id
     * @property string $street
     * @property string $house_number
     * @property string|null $optional_address_line
     * @property string $postcode
     * @property string $city
     * @property string $phone
     * @property string $fax
     * @property string $email
     * @property string|null $website
     * @property bool $courier_service
     * @property int|null $courier_service_radius
     * @property int $has_near_parking_space
     * @property float|null $latitude
     * @property float|null $longitude
     * @property int|null $goods_management_system_id
     * @property int|null $accounting_center_id
     * @property int $active
     * @property int $verification_status
     * @property bool $webapp
     * @property int $is_main
     * @property int|null $pharmacy_type
     * @property string|null $custom_goods_management_system
     * @property string|null $custom_accounting_center
     * @property string|null $uuid
     * @property int $corona_rapid_test
     * @property bool $export_added_value_to_apothekenmananger
     * @property bool $export_added_value_to_gematik
     * @property string|null $corona_rapid_test_booking_url
     * @property bool $shipping_pharmacy_enabled
     * @property string|null $shipping_pharmacy_name
     * @property string|null $shipping_pharmacy_website
     * @property int $vaccination_import
     * @property int $accepts_e_prescription
     * @property-read \App\AccountingCenter|null $accountingCenter
     * @property-read Collection|\Laravel\Nova\Actions\ActionEvent[] $actions
     * @property-read int|null $actions_count
     * @property-read Collection|\App\ApprovableChange[] $approvableChanges
     * @property-read int|null $approvable_changes_count
     * @property-read Collection|\App\BusinessHour[] $businessHours
     * @property-read int|null $business_hours_count
     * @property-read Collection|\App\FocusArea[] $focusAreas
     * @property-read int|null $focus_areas_count
     * @property-read mixed $address
     * @property-read mixed $pending
     * @property-read \App\GoodsManagementSystem|null $goodsManagementSystem
     * @property-read Collection|\App\PharmacyImage[] $images
     * @property-read int|null $images_count
     * @property-read Collection|\App\Language[] $languages
     * @property-read int|null $languages_count
     * @property-read Collection|\App\PharmacyAddress[] $pharmacyAddresses
     * @property-read int|null $pharmacy_addresses_count
     * @property-read Collection|\App\PharmacyImage[] $pharmacyImages
     * @property-read int|null $pharmacy_images_count
     * @property-read \App\PharmacyType|null $pharmacyType
     * @property-read Collection|\App\PublicTransportStation[] $publicTransportStations
     * @property-read int|null $public_transport_stations_count
     * @property-read \App\PharmacySetting|null $settings
     * @property-read \App\TelematicsId|null $telematicsId
     * @property-read Collection|\App\User[] $users
     * @property-read int|null $users_count
     * @property-read Collection|\App\VaccinationImport[] $vaccinationImports
     * @property-read int|null $vaccination_imports_count
     * @property-read Collection|\App\Vaccination[] $vaccinations
     * @property-read int|null $vaccinations_count
     *
     * @method static \Database\Factories\PharmacyFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy query()
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereAcceptsEPrescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereAccountingCenterId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereActive($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCity($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCoronaRapidTest($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCoronaRapidTestBookingUrl($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCourierService($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCourierServiceRadius($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCustomAccountingCenter($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereCustomGoodsManagementSystem($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereExportAddedValueToApothekenmananger($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereExportAddedValueToGematik($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereFax($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereGoodsManagementSystemId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereHasNearParkingSpace($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereHouseNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereInstituteId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereIsMain($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereLatitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereLongitude($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereNId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy wherePharmacyType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy wherePhone($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy wherePostcode($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereShippingPharmacyEnabled($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereShippingPharmacyName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereShippingPharmacyWebsite($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereStreet($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereUuid($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereVaccinationImport($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereVerificationStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereWebapp($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Pharmacy whereWebsite($value)
     */
    class IdeHelperPharmacy extends \Eloquent {}
}

namespace App{
    /**
     * Class PharmacyAddress
     *
     * @mixin IdeHelperPharmacyAddress
     *
     * @property int $id
     * @property int $pharmacy_id
     * @property int $type
     * @property string $street
     * @property string $house_number
     * @property string|null $optional_address_line
     * @property string $postcode
     * @property string $city
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Pharmacy $pharmacy
     *
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress query()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress whereCity($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress whereHouseNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress wherePostcode($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress whereStreet($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyAddress whereUpdatedAt($value)
     */
    class IdeHelperPharmacyAddress extends \Eloquent {}
}

namespace App{
    /**
     * Class PharmacyImage
     *
     * @mixin IdeHelperPharmacyImage
     *
     * @property int $id
     * @property int $pharmacy_id
     * @property string $path
     * @property int $is_logo
     * @property int $is_validated
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Pharmacy $pharmacy
     *
     * @method static \Database\Factories\PharmacyImageFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage query()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage whereIsLogo($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage whereIsValidated($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage wherePath($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyImage whereUpdatedAt($value)
     */
    class IdeHelperPharmacyImage extends \Eloquent {}
}

namespace App{
    /**
     * App\PharmacyRoleUser
     *
     * @mixin IdeHelperPharmacyRoleUser
     *
     * @property int $user_id
     * @property int $pharmacy_id
     * @property string $role_name
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property array|null $permissions
     *
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser query()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser wherePermissions($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser whereRoleName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyRoleUser whereUserId($value)
     */
    class IdeHelperPharmacyRoleUser extends \Eloquent {}
}

namespace App{
    /**
     * Class PharmacySetting
     *
     * @mixin IdeHelperPharmacySetting
     *
     * @property int $pharmacy_id
     * @property int $vaccinate_status
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Pharmacy $pharmacy
     *
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacySetting newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacySetting newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacySetting query()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacySetting whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacySetting wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacySetting whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacySetting whereVaccinateStatus($value)
     */
    class IdeHelperPharmacySetting extends \Eloquent {}
}

namespace App{
    /**
     * Class PharmacyType
     *
     * @mixin IdeHelperPharmacyType
     *
     * @property int $id
     * @property string $name
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read Collection|\App\Pharmacy[] $pharmacies
     * @property-read int|null $pharmacies_count
     *
     * @method static \Database\Factories\PharmacyTypeFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyType newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyType newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyType query()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyType whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyType whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyType whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmacyType whereUpdatedAt($value)
     */
    class IdeHelperPharmacyType extends \Eloquent {}
}

namespace App{
    /**
     * Class PublicTransportStation
     *
     * @mixin IdeHelperPublicTransportStation
     *
     * @property int $id
     * @property int $pharmacy_id
     * @property int $type
     * @property string $stop
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Pharmacy $pharmacy
     *
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation query()
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation whereStop($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PublicTransportStation whereUpdatedAt($value)
     */
    class IdeHelperPublicTransportStation extends \Eloquent {}
}

namespace App{
    /**
     * Class RssFeedItem
     *
     * @mixin IdeHelperRssFeedItem
     *
     * @property int $id
     * @property string $title
     * @property string $link
     * @property string|null $description
     * @property \Illuminate\Support\Carbon|null $modification_date
     * @property string|null $category
     * @property int $rss_feed_source_id
     * @property array|null $extra
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\RssFeedSource $source
     *
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem query()
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereCategory($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereDescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereExtra($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereLink($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereModificationDate($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereRssFeedSourceId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereTitle($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedItem whereUpdatedAt($value)
     */
    class IdeHelperRssFeedItem extends \Eloquent {}
}

namespace App{
    /**
     * Class RssFeedSource
     *
     * @mixin IdeHelperRssFeedSource
     *
     * @property int $id
     * @property string $url
     * @property string $identifier
     * @property string $name
     * @property string|null $description
     * @property int $enabled
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\RssFeedItem[] $items
     * @property-read int|null $items_count
     *
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource query()
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereDescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereEnabled($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereIdentifier($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|RssFeedSource whereUrl($value)
     */
    class IdeHelperRssFeedSource extends \Eloquent {}
}

namespace App{
    /**
     * App\Staff
     *
     * @mixin IdeHelperStaff
     *
     * @property int $id
     * @property string $name
     * @property string $email
     * @property string $password
     * @property string|null $remember_token
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property string $role
     *
     * @method static \Database\Factories\StaffFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Staff newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Staff query()
     * @method static \Illuminate\Database\Eloquent\Builder|Staff whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff wherePassword($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff whereRememberToken($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff whereRole($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Staff whereUpdatedAt($value)
     */
    class IdeHelperStaff extends \Eloquent {}
}

namespace App{
    /**
     * Class Tag
     *
     * @mixin IdeHelperTag
     *
     * @property int $id
     * @property string $slug
     * @property string $title
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read Collection|\App\News[] $news
     * @property-read int|null $news_count
     *
     * @method static \Database\Factories\TagFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Tag newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Tag newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Tag query()
     * @method static \Illuminate\Database\Eloquent\Builder|Tag whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Tag whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Tag whereSlug($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Tag whereTitle($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Tag whereUpdatedAt($value)
     */
    class IdeHelperTag extends \Eloquent {}
}

namespace App{
    /**
     * Class TelematicsId
     *
     * @mixin IdeHelperTelematicsId
     *
     * @property int $id
     * @property int $sectoral_mark
     * @property string $chamber_id
     * @property int $card_type
     * @property string $member_id
     * @property int|null $tsp_identifier
     * @property string $random_number
     * @property int $telematics_idable_id
     * @property string $telematics_idable_type
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read mixed $complete_id
     * @property mixed $pharmacy_id
     * @property-read Model|\Eloquent $telematics_idable
     *
     * @method static \Database\Factories\TelematicsIdFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId query()
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereCardType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereChamberId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereMemberId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereRandomNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereSectoralMark($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereTelematicsIdableId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereTelematicsIdableType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereTspIdentifier($value)
     * @method static \Illuminate\Database\Eloquent\Builder|TelematicsId whereUpdatedAt($value)
     */
    class IdeHelperTelematicsId extends \Eloquent {}
}

namespace App{
    /**
     * Class Upload
     *
     * @mixin IdeHelperUpload
     *
     * @property int $id
     * @property string $name
     * @property string $slug
     * @property string|null $type
     * @property string $path
     * @property float|null $size
     * @property \App\Enums\UploadVisibility $visibility
     * @property int|null $creator_id
     * @property int|null $updater_id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $deleted_at
     * @property-read \App\Staff|null $creator
     * @property-read \App\Staff|null $updater
     *
     * @method static \Database\Factories\UploadFactory factory($count = null, $state = [])
     * @method static \Illuminate\Database\Eloquent\Builder|Upload newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Upload newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Upload onlyTrashed()
     * @method static \Illuminate\Database\Eloquent\Builder|Upload query()
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereCreatorId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereDeletedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload wherePath($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereSize($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereSlug($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereUpdaterId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload whereVisibility($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Upload withTrashed()
     * @method static \Illuminate\Database\Eloquent\Builder|Upload withoutTrashed()
     */
    class IdeHelperUpload extends \Eloquent {}
}

namespace App{
    /**
     * Class MemberImport
     *
     * @mixin IdeHelperMemberImport
     *
     * @property string $id
     * @property string|null $title
     * @property string $first_name
     * @property string $last_name
     * @property string $email
     * @property array $data
     * @property MemberImportStatus $status
     * @property string|null $status_description
     * @property int $association_id
     * @property int|null $user_id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Association $association
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Pharmacy> $pharmacies
     * @property-read int|null $pharmacies_count
     * @property-read \App\User|null $user
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport query()
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereData($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereFirstName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereLastName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereStatusDescription($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereTitle($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|MemberImport whereUserId($value)
     * @mixin \Eloquent
     */

    class IdeHelperMemberImport extends \Eloquent {}
}

namespace App{
    /**
     * Class User
     *
     * @mixin IdeHelperUser
     *
     * @property int $id
     * @property int|null $salutation
     * @property string|null $first_name
     * @property string|null $last_name
     * @property string|null $email
     * @property \Illuminate\Support\Carbon|null $email_verified_at
     * @property string $password
     * @property string $phone
     * @property string|null $remember_token
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $last_login
     * @property string|null $title
     * @property string|null $username
     * @property string|null $search_name
     * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Nova\Actions\ActionEvent[] $actions
     * @property-read int|null $actions_count
     * @property-read \App\UserAssociationProfile|null $associationProfile
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Association[] $associations
     * @property-read int|null $associations_count
     * @property-read \App\BrochureCode|null $brochureCode
     * @property-read \App\CompanyUser|null $companyUser
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\UserPharmacyProfile[] $companyUserProfiles
     * @property-read int|null $company_user_profiles_count
     * @property-read mixed $association_id
     * @property-read mixed $greeting
     * @property-read mixed $name
     * @property-read mixed $route_emails_to
     * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
     * @property-read int|null $notifications_count
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Pharmacy[] $ownedPharmacies
     * @property-read int|null $owned_pharmacies_count
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Pharmacy[] $pharmacies
     * @property-read int|null $pharmacies_count
     * @property-read \App\UserPharmacyProfile|null $pharmacyProfile
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Vaccination[] $vaccinations
     * @property-read int|null $vaccinations_count
     *
     * @method static \Database\Factories\UserFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|User forAssociation(\App\Association $association)
     * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|User query()
     * @method static \Illuminate\Database\Eloquent\Builder|User verified()
     * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereFirstName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereLastLogin($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereLastName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User wherePhone($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereSalutation($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereSearchName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereTitle($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|User whereUsername($value)
     */
    class IdeHelperUser extends \Eloquent implements \Illuminate\Contracts\Auth\MustVerifyEmail {}
}

namespace App{
    /**
     * Class UserAssociationProfile
     *
     * @mixin IdeHelperUserAssociationProfile
     *
     * @property int $user_id
     * @property int|null $association_id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Association|null $association
     * @property-read \App\User $user
     *
     * @method static \Illuminate\Database\Eloquent\Builder|UserAssociationProfile newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|UserAssociationProfile newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|UserAssociationProfile query()
     * @method static \Illuminate\Database\Eloquent\Builder|UserAssociationProfile whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserAssociationProfile whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserAssociationProfile whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserAssociationProfile whereUserId($value)
     */
    class IdeHelperUserAssociationProfile extends \Eloquent {}
}

namespace App{
    /**
     * Class UserPharmacyProfile
     *
     * @mixin IdeHelperUserPharmacyProfile
     *
     * @property int $user_id
     * @property int|null $association_id
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property int $announcement_modal_dismiss
     * @property int|null $company_user_id
     * @property-read \App\Association|null $association
     * @property-read \App\User|null $companyHeadUser
     * @property-read \App\User $user
     *
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile query()
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile whereAnnouncementModalDismiss($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile whereCompanyUserId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|UserPharmacyProfile whereUserId($value)
     */
    class IdeHelperUserPharmacyProfile extends \Eloquent {}
}

namespace App{
    /**
     * Class Vaccination
     *
     * @mixin IdeHelperVaccination
     *
     * @property int $id
     * @property string $uuid
     * @property \Illuminate\Support\Carbon|null $date
     * @property int|null $association_id
     * @property int|null $pharmacy_id
     * @property int|null $health_insurance_company_id
     * @property int|null $user_id
     * @property int $type
     * @property int|null $duration
     * @property int|null $age_group
     * @property int|null $gender
     * @property int|null $pharmaceutical_id
     * @property string|null $batch_number
     * @property string $status
     * @property int $step
     * @property array|null $reasons_to_abort
     * @property string|null $notes
     * @property int $valid
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Association|null $association
     * @property-read \App\HealthInsuranceCompany|null $healthInsuranceCompany
     * @property-read \App\InfluenzaVaccination|null $influenzaVaccination
     * @property-read \App\CovidVaccination|null $covidVaccination
     * @property-read \App\Pharmaceutical|null $pharmaceutical
     * @property-read \App\Pharmacy|null $pharmacy
     * @property-read \App\User|null $user
     * @property-read \App\VaccinationPatient|null $vaccinationPatient
     *
     * @method static \Database\Factories\VaccinationFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination query()
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereAgeGroup($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereBatchNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereDate($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereDuration($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereGender($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereHealthInsuranceCompanyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereNotes($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination wherePharmaceuticalId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereReasonsToAbort($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereStep($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereUserId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereUuid($value)
     * @method static \Illuminate\Database\Eloquent\Builder|Vaccination whereValid($value)
     */
    class IdeHelperVaccination extends \Eloquent {}
}

namespace App{
    /**
     * Class VaccinationImport
     *
     * @mixin IdeHelperVaccinationImport
     *
     * @property int $id
     * @property int|null $pharmacy_id
     * @property string $search_string
     * @property string $search_token
     * @property int $accounting_type
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Pharmacy|null $pharmacy
     *
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport query()
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport whereAccountingType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport whereSearchString($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport whereSearchToken($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationImport whereUpdatedAt($value)
     */
    class IdeHelperVaccinationImport extends \Eloquent {}
}

namespace App{
    /**
     * Class VaccinationPatient
     *
     * @mixin IdeHelperVaccinationPatient
     *
     * @property int $vaccination_id
     * @property string|null $birthdate
     * @property string|null $first_name
     * @property string|null $last_name
     * @property string|null $insurance_number
     * @property string|null $street
     * @property string|null $house_number
     * @property string|null $optional_address_line
     * @property string|null $postcode
     * @property string|null $city
     * @property string|null $email
     * @property string|null $phone
     * @property string|null $gender
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Vaccination $vaccination
     *
     * @method static \Database\Factories\VaccinationPatientFactory factory(...$parameters)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient query()
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereBirthdate($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereCity($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereFirstName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereGender($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereHouseNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereInsuranceNumber($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereLastName($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient wherePhone($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient wherePostcode($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereStreet($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|VaccinationPatient whereVaccinationId($value)
     */
    class IdeHelperVaccinationPatient extends \Eloquent {}
}

namespace App{
    /**
     * App\PharmaceuticalService
     *
     * @property int $id
     * @property string $uuid
     * @property \Illuminate\Support\Carbon|null $date
     * @property int|null $association_id
     * @property int|null $pharmacy_id
     * @property int|null $user_id
     * @property int|null $type
     * @property int|null $age_group
     * @property bool $further_information_allowed
     * @property string $status
     * @property int $step
     * @property array|null $reasons_to_abort
     * @property string|null $notes
     * @property int $valid
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property-read \App\Association|null $association
     * @property-read \Carbon\Carbon $localized_created_at
     * @property-read \Carbon\Carbon $localized_date
     * @property-read \Carbon\Carbon $localized_updated_at
     * @property-read mixed $price
     * @property-read mixed $spzn
     * @property-read mixed $user_name
     * @property-read \App\HealthInsuranceCompany|null $healthInsuranceCompany
     * @property-read \App\InhalationTechniquePatient|null $inhalationTechniquePatient
     * @property-read \App\MeasureBloodPressurePatient|null $measureBloodPressurePatient
     * @property-read \App\Pharmaceutical|null $pharmaceutical
     * @property-read \App\PharmaceuticalServicePatient|null $pharmaceuticalServicePatient
     * @property-read \App\Pharmacy|null $pharmacy
     * @property-read \App\User|null $user
     *
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService query()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService successfullyFinished()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService successfullyFinishedByInhalationTechnique()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService successfullyFinishedByMeasureBloodPressure()
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereAgeGroup($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereAssociationId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereDate($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereFurtherInformationAllowed($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereNotes($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService wherePharmacyId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereReasonsToAbort($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereStatus($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereStep($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereType($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereUserId($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereUuid($value)
     * @method static \Illuminate\Database\Eloquent\Builder|PharmaceuticalService whereValid($value)
     *
     * @mixin \Eloquent
     */
    class IdeHelperPharmaceuticalService {}
}
