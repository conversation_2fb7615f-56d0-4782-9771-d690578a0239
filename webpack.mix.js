const mix = require('laravel-mix');
const tailwindcss = require('tailwindcss');
require('laravel-mix-purgecss');
const del = require('del');


mix.js('resources/js/app.js', 'public/assets/js')
    .sass('resources/sass/app.scss', 'public/assets/css')
    .options({
        processCssUrls: false,
        postCss: [tailwindcss('./tailwind.config.js')],
    })
    .copy('node_modules/@famedly/apothekenportal-messenger', 'public/vendor/famedly-web')
    .copy('resources/js/lib/famedly/config.json', 'public/vendor/famedly-web')
    // .copy('public/vendor/famedly-web/service_worker.js', 'public/vendor/famedly-web/service_worker2.js')
    // .then(() => {
    //     del('public/vendor/famedly-web/service_worker.js');
    // })
    .version()
    .browserSync({
        host: process.env.MIX_BROWSERSYNC_URL,
        proxy: {
            target: process.env.MIX_BROWSERSYNC_URL + ':8000',
        },
        open: 'external',
    })
    .purgeCss()
;
