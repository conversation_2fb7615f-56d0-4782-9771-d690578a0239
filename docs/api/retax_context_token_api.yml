openapi: '3.0.0'
info:
  version: '1.0.0'
  title: 'Retax User Context Token API'

components:
  schemas:
    RetaxUserContextToken:
      type: object
      properties:
        exp:
          type: integer
          description: Expiration timestamp (Unix)
          example: 1680003600
        iat:
          type: integer
          description: Issued-at timestamp (Unix)
          example: 1679996400
        iss:
          type: string
          format: uri
          description: Issuer
          example: https://keycloak.example.com/auth/realms/myrealm
        aud:
          type: string
          description: Audience
          example: verbandportal
        sub:
          type: string
          description: Subject (User ID)
          example: user-def-456
        resource_access:
          type: object
          properties:
            retax:
              type: object
              properties:
                pharmacies:
                  type: object
                  additionalProperties:
                    type: object
                    properties:
                      roles:
                        type: array
                        items:
                          type: string
                          enum: [ "owner", "subowner", "branch_manager", "employee" ]
                      permissions:
                        type: array
                        items:
                          type: string
                          enum: [ "retax_employee", "retax_owner" ]
                associations:
                  type: object
                  additionalProperties:
                    type: object
                    properties:
                      roles:
                        type: array
                        items:
                          type: string
                          enum: [ "admin", "employee" ]
                      permissions:
                        type: array
                        items:
                          type: string
                          enum: [ "retax_key_user", "retax_employee", "retax_leader" ]
                      custom:
                        type: object
                        properties:
                          details:
                            type: object
                            properties:
                              name:
                                type: string
                                example: Apothekerverband Westfalen-Lippe
                              abbreviation:
                                type: string
                                description: Abbreviation (Enum-Wert)
                                example: "[enum wert]"

    ErrorMessage:
      type: object
      properties:
        message:
          type: string

    TokenResponse:
      type: object
      properties:
        token:
          type: string
          description: 'JWT token'

paths:
  /api/v1/user-context/retax:
    get:
      summary: 'Get retax user context token'
      description: 'This endpoint is used to get retax user context token.'
      parameters:
        - in: 'header'
          name: 'Authorization'
          schema:
            type: 'string'
          required: true
          description: 'Bearer token for the user.'
      responses:
        '200':
          description: 'Successful operation'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '400':
          description: 'Bad request'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                message: 'No Bearer Token provided'
        '401':
          description: 'Unauthorized'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                no_token:
                  value:
                    message: 'Token not provided.'
                expired:
                  value:
                    message: 'Provided token is expired.'
        '403':
          description: 'Forbidden'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              examples:
                no_pharmacy:
                  value:
                    message: 'User has no pharmacy.'
        '404':
          description: 'Not found'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorMessage'
              example:
                message: 'User not found.'

