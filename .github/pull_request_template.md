
- [ ] I wrote automated tests
- [ ] I manually tested my implementation and documented the results (in the task)
- [ ] I added information for QA (in the task) so that QA can fully test the task including edge cases
- [ ] I checked that all acceptance criteria are met
- [ ] I added comments in the PR to help the reviewer

> [!TIP]
> [Development Guidelines](https://gedisa.atlassian.net/wiki/spaces/A/pages/85065781/Development+Guidelines)
> 
> [Review Guidelines](https://gedisa.atlassian.net/wiki/spaces/A/pages/213352489/Code+Review+Guidelines)
> 
> [Release Note Guidelines](https://gedisa.atlassian.net/wiki/spaces/A/pages/654966789/Release+Note+Guidelines)
