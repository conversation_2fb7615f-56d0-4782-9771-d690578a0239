name: Run Tests

on:
  push:
    branches:
      - 'dev'
      - 'rc'
      - 'master'
  pull_request:
    types:
      - synchronize
      - opened
      - reopened
      - ready_for_review

jobs:
  phpstan:
    name: PHPStan Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: dependencies-composer-${{ runner.os }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            dependencies-composer-${{ runner.os }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, mysql, mysqli, pdo_mysql, bcmath, soap, intl, gd, exif, iconv, imagick
          coverage: none
          ini-values: |
            zend.assertions=1
            xdebug.mode=off

      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_KEY_GEDISA_ID_PHP }}

      - name: Configure Composer credentials
        run: |
          composer config http-basic.nova.laravel.com ${{ secrets.NOVA_USERNAME }} ${{ secrets.NOVA_API_KEY }}
          composer config http-basic.composer.fluxui.dev ${{ secrets.FLUX_USERNAME }} ${{ secrets.FLUX_KEY }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist -n --ignore-platform-reqs --no-interaction --no-scripts

      - name: Cache PHPStan
        uses: actions/cache@v4
        with:
          path: storage/phpstan # same as in phpstan.neon
          key: phpstan-result-cache-${{ github.run_id }}
          restore-keys: |
            phpstan-result-cache-

      - name: "Run PHPStan"
        run: php ./vendor/bin/phpstan analyse --memory-limit=1G

  tests:
    name: Run PHPUnit Tests (Sharded)
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false

    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}

    strategy:
      fail-fast: false
      matrix:
        shard: [1, 2, 3, 4]  # Define the number of shards

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: dependencies-composer-${{ runner.os }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            dependencies-composer-${{ runner.os }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, mysql, mysqli, pdo_mysql, bcmath, soap, intl, gd, exif, iconv, imagick
          coverage: none
          ini-values: |
            zend.assertions=1
            xdebug.mode=off

      - name: Copy .env
        run: php -r "file_exists('.env') || copy('.env.example', '.env');"

      - name: Edit .env
        run: |
          sed -i 's/PLACEHOLDER_STRIPE_KEY/${{ secrets.STRIPE_KEY }}/g' .env
          sed -i 's/PLACEHOLDER_STRIPE_SECRET/${{ secrets.STRIPE_SECRET }}/g' .env
          sed -i 's/PLACEHOLDER_STRIPE_WEBHOOK_SECRET/${{ env.STRIPE_WEBHOOK_SECRET }}/g' .env

      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_KEY_GEDISA_ID_PHP }}

      - name: Configure Composer credentials
        run: |
          composer config http-basic.nova.laravel.com ${{ secrets.NOVA_USERNAME }} ${{ secrets.NOVA_API_KEY }}
          composer config http-basic.composer.fluxui.dev ${{ secrets.FLUX_USERNAME }} ${{ secrets.FLUX_KEY }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist -n --ignore-platform-reqs --no-interaction --no-scripts

      - name: Add NPM credentials for installation
        run: printf '${{ secrets.NPM_NPMRC }}' >>  $GITHUB_WORKSPACE/.npmrc

      - name: Install npm dependencies and build frontend assets
        uses: actions/setup-node@v4
        with:
          node-version: 21.6.2 # production version
          cache: 'npm'
      - run: npm ci
      - run: npm run build

      - name: Directory Permissions
        run: chmod -R 777 storage bootstrap/cache

      - name: Create Database
        run: |
          mkdir -p database
          touch database/database.sqlite

      - name: Create folders for each shard
        run: |
          mkdir -p shard_1 shard_2 shard_3 shard_4

      - name: Split test files into shard files
        run: |
          find ./tests/Feature ./tests/Unit -name "*Test.php" | split -n r/4 - shard_ --additional-suffix=".txt"

      - name: Move split files to shard directories
        run: |
          mv shard_aa.txt shard_1.txt
          mv shard_ab.txt shard_2.txt
          mv shard_ac.txt shard_3.txt
          mv shard_ad.txt shard_4.txt

      - name: Create symbolic links for test files in shard folders
        run: |
          for shard in 1 2 3 4; do
            mkdir -p shard_${shard}
            cat shard_${shard}.txt | xargs -I {} ln -sf $PWD/{} shard_${shard}/
          done

      - name: Prepare Tests for Current Shard
        run: |
          cat shard_${{ matrix.shard }}.txt > current_shard_tests.txt

          # Find all test files in tests/Unit and tests/Integration directories
          find ./tests/Unit ./tests/Feature -name "*Test.php" > unit_and_integration_tests.txt

          # Remove test files from Unit and Integration that are NOT in the current shard
          grep -Fxv -f current_shard_tests.txt unit_and_integration_tests.txt | xargs rm -f

      - name: Deploy Passport
        run: cp tests/storage/oauth-private-test.key storage/oauth-private.key && cp tests/storage/oauth-public-test.key storage/oauth-public.key

      - name: Run PHPUnit Tests for this shard (with parallel testing)
        env:
          APP_ENV: testing
          DB_CONNECTION: sqlite
          DB_DATABASE: database/database.sqlite
          BRANCH_NAME: ${{ env.BRANCH_NAME }}
        run: php artisan test --parallel --no-coverage

  linting:
    name: Lint and Style Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: vendor
          key: dependencies-composer-${{ runner.os }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            dependencies-composer-${{ runner.os }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, mysql, mysqli, pdo_mysql, bcmath, soap, intl, gd, exif, iconv, imagick
          coverage: none
          ini-values: |
            zend.assertions=1
            xdebug.mode=off

      - name: Setup SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: |
            ${{ secrets.SSH_KEY_GEDISA_ID_PHP }}

      - name: Configure Composer credentials
        run: |
          composer config http-basic.nova.laravel.com ${{ secrets.NOVA_USERNAME }} ${{ secrets.NOVA_API_KEY }}
          composer config http-basic.composer.fluxui.dev ${{ secrets.FLUX_USERNAME }} ${{ secrets.FLUX_KEY }}

      - name: Install Composer dependencies
        run: composer install --prefer-dist -n --ignore-platform-reqs --no-interaction --no-scripts

      - name: Cache Pint
        uses: actions/cache@v4
        with:
          path: /tmp/pint.cache
          key: pint-cache-${{ runner.os }}-${{ hashFiles('**/*.php') }}
          restore-keys: |
            pint-cache-${{ runner.os }}

      - name: Run Pint Code Style
        run: php ./vendor/bin/pint

      - name: Commit Style Changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: apply pint code style
