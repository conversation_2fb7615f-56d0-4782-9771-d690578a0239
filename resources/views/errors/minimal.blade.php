<!DOCTYPE html>
<html class="h-full" lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>@yield('title')</title>

        {{-- Styles --}}
        @vite(['resources/css/app.css'])

        {{-- Tracking --}}
        @if(config('app.tracking_enabled'))
            <script async defer data-domain="mein-apothekenportal.de" src="https://webstats.cyrano-services.de/js/plausible.js"></script>
            <script>window.plausible = window.plausible || function() { (window.plausible.q = window.plausible.q || []).push(arguments) }</script>
            <script>plausible("@yield('code')",{ props: { path: document.location.pathname } });</script>
        @endif
    </head>
    <body class="antialiased h-full">
        <div class="min-h-full pt-16 pb-12 flex flex-col bg-white">
            <main class="flex-grow flex flex-col justify-center max-w-3xl w-full mx-auto px-4 sm:px-6 lg:px-8">
                <div class="fixed top-0 left-0 p-4">
                    <a href="{{ route('home') }}" class="inline-flex">
                        <x-logo size="lg" class="h-7 sm:h-9"/>
                    </a>
                </div>
                <div class="py-16">
                    <div class="sm:flex">
                        <p class="text-4xl leading-10 font-extrabold text-red-600 sm:text-5xl">
                            @yield('code')
                        </p>
                        <div class="sm:ml-6">
                            <div class="sm:border-l sm:border-gray-200 sm:pl-6">
                                <h1 class="text-4xl leading-10 font-extrabold text-gray-900 tracking-tight sm:text-5xl">
                                    @yield('message')
                                </h1>
                                <p class="mt-2 text-base text-gray-500">
                                    @yield('submessage', __('errors.default_description'))
                                </p>
                            </div>

                            @if(!isset($home_link))
                                <div class="mt-10 flex space-x-3 sm:border-l sm:border-transparent sm:pl-6">
                                    <a href="{{ route('home') }}" class="text-base font-medium text-gray-700 hover:text-gray-900">
                                        Zurück zur Startseite<span aria-hidden="true"> →</span>
                                    </a>
                                </div>
                            @endif

                            @yield('bottom-message')
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </body>
</html>
