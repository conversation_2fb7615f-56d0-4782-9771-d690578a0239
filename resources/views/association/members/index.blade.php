@extends('layouts.app', ['novue' => true])

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                @lang('navigation.memberAdministration')
            </x-slot>
            <x-slot name="section">
                {{ $association->name }}
            </x-slot>
            <x-slot name="action">
                <x:button href="{{route('associations.members.export')}}">
                    <svg class="-ml-1 mr-2 h-5 w-5">
                        <use href="/icons.svg#arrow-down-tray"/>
                    </svg>
                    Download Export
                </x:button>
                <x:button x-data="{}"
                          x-on:click="$dispatch('change-modal-state', {name: '{{ \App\Livewire\Association\Components\AssociationMembers::MODAL_NAME }}', state: 'open'})">
                    <svg class="-ml-1 mr-2 h-5 w-5">
                        <use href="/icons.svg#plus"/>
                    </svg>
                    @lang('messages.addMembers')
                </x:button>
            </x-slot>
        </x:header>

        @can('administrateMembers', $association)
            @livewire('association.components.association-members', [
                'association' => $association,
            ])
        @endcan
    </x:content>
@endsection
