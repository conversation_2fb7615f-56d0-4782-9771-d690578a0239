@extends('layouts.news.' . request()->route('type') ?? 'news')

@section('innerContent')
    <x:pageHeader :realClass="request()->route('type') == 'blog' ? 'mb-12 mt-4' : ''">
        @unless(request()->route('type') == 'blog')
            <x-slot name="breadcrump">
                <x:breadcrumpItem position="1" isMobileBackButton href="{{ route('home') }}">@lang('messages.home')</x:breadcrumpItem>
                <x:breadcrumpItem position="2" isCurrentPage href="{{ route('news.index', ['type' => request()->route('type')]) }}">@lang('messages.news')</x:breadcrumpItem>
            </x-slot>
        @endunless

        <x-slot name="title">
            @if(request()->route('type') == 'blog')
                GEDISA Digital – Der BLOG
            @else
                GEDISA Digital - News
            @endif
        </x-slot>

        <x-slot name="subtitle">
            Für Apotheken. Für Zuku<PERSON>ft.
        </x-slot>

        <x-slot name="actions">
            @include('news.partials.filter')
        </x-slot>
    </x:pageHeader>

    <main class="flex-grow">
        @include('news.partials.grid')
    </main>
@endsection
