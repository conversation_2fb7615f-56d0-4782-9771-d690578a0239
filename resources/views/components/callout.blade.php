@props([
    'close' => null,
    'buttons' => null,
    'title' => null,
    'variant' => null,
])

<div class="bg-white ring-1 ring-gray-200 px-5 py-4 text-sm rounded-lg">
    <div class="flex items-start gap-4">
        <div class="ring-1 ring-gray-200 p-1.5 rounded-md">
            <flux:icon.information-circle class="size-4" />
        </div>

        <div>
            <p class="font-semibold">{{ $title }}</p>
            <div class="mt-1">
                {{ $slot }}
            </div>
        </div>

        @if ($close)
            <div>
                {{ $close }}
            </div>
        @endif
    </div>


</div>
