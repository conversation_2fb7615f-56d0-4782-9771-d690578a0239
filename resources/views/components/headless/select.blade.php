@props([
    'label', // Used for the aria-label attribute
    'options', // Array of objects to show as options
    'key' => 'id', // Name of the field that provides a unique key for each option
    'valueKey' => 'value', // Name of the field that provides the value to show
    'disabledKey' => 'disabled', // Name of the field that provides a boolean to disable an option
    'placeholder' => $label, // Placeholder text

    'multiple' => false, // Whether to allow multiple selections

    'dropdownContainerClasses' =>
        'absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm',
    'optionClasses' => 'relative cursor-default select-none py-2 pl-8 pr-4 text-left rounded mx-1',
    'optionActiveClasses' => 'bg-primary-50',
    'optionInactiveClasses' => 'text-gray-900',
    'optionDisabledClasses' => 'opacity-50 cursor-not-allowed',

    // Slots
    'trigger' => null,
    'optionTemplate' => null,
])

<div
    x-data="{
        selectedOption: @entangle($attributes->wire('model')),
        options: @js($options)
    }"
    wire:ignore
    {{ $attributes->class([]) }}
>
    <div
        class="relative"
        x-listbox
        x-model="selectedOption"
        @if ($multiple) multiple @endif
    >
        <label
            class="sr-only"
            x-listbox:label
        >{{ $label }}</label>

        @if ($trigger)
            {{ $trigger }}
        @else
            <button
                class="relative w-full cursor-default rounded-md bg-white py-1.5 pl-3 pr-10 text-left text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-600 sm:text-sm sm:leading-6"
                x-listbox:button
            >
                @if ($multiple)
                    <span
                        class="block truncate"
                        x-text="selectedOption.length > 0 ? selectedOption.map((selectedOption) => selectedOption.{{ $valueKey }}).join(', ') : @js($placeholder)"
                        :class="{ 'text-gray-400': selectedOption === [] }"
                    ></span>
                @else
                    <span
                        class="block truncate"
                        x-text="selectedOption ? selectedOption.{{ $valueKey }} : @js($placeholder)"
                        :class="{ 'text-gray-400': selectedOption === null }"
                    ></span>
                @endif
                <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                    <svg
                        class="h-5 w-5 text-gray-400"
                        aria-hidden="true"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                    >
                        <path
                            fill-rule="evenodd"
                            d="M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z"
                            clip-rule="evenodd"
                        />
                    </svg>
                </span>
            </button>
        @endif

        <ul
            x-listbox:options
            x-transition.origin.top.right
            x-cloak
            @class([$dropdownContainerClasses])
        >
            <template
                x-for="option in options"
                x-bind:key="option.{{ $key }}"
            >
                <li
                    x-listbox:option
                    x-bind:value="option"
                    x-bind:disabled="option.{{ $disabledKey }}"
                    x-bind:class="{
                        @if ($optionActiveClasses) @js($optionActiveClasses): $listboxOption.isActive, @endif
                        @if ($optionInactiveClasses) @js($optionInactiveClasses): ! $listboxOption.isActive, @endif
                        @if ($optionDisabledClasses) @js($optionDisabledClasses): $listboxOption.isDisabled, @endif
                    }"
                    @class([$optionClasses])
                >
                    @if ($optionTemplate)
                        {{ $optionTemplate }}
                    @else
                        <span
                            class="block truncate"
                            x-text="option.{{ $valueKey }}"
                            x-bind:class="{
                                'font-semibold': $listboxOption.isSelected,
                                'font-normal': !$listboxOption.isSelected
                            }"
                        ></span>

                        <span
                            class="absolute inset-y-0 left-0 flex items-center pl-1.5 text-primary-600"
                            x-show="$listboxOption.isSelected"
                        >
                            <svg
                                class="h-5 w-5"
                                aria-hidden="true"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                    clip-rule="evenodd"
                                />
                            </svg>
                        </span>
                    @endif
                </li>
            </template>
        </ul>
    </div>
</div>
