<div>
    <x:subscriptions.section-headline
        headline="Auftragsverarbeitungsvertrag akzeptieren"
    />

    <div class="mt-2">
        <p class="leading-5">
            Den aktuellen Auftragsverarbeitungsvertrag finden Sie hier:
        </p>
    </div>

    <div class="mt-2">
        <p class="leading-5">
            <a href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}" target="_blank"
               class="font-medium underline flex items-center hover:no-underline">
                Auftragsverarbeitungsvertrag
                <svg class="w-4 h-4 ml-1"><use href="/icons.svg#arrow-long-top-right-on-square"/></svg>
            </a>
        </p>
    </div>

    <div class="mt-3">
        <p class="leading-5">
            Datenschutzrechtlich sind die zertifikatsausstellenden und impfenden (COVID-19 und Grippeschutz) Apotheken
            verantwortlich für die Übermittlung der Patientendaten an das Robert-Koch-Institut. Die GEDISA und die von
            ihr beauftragten Unterauftragnehmer agieren dabei als Auftragsverarbeiter. Der entsprechende
            Auftragsverarbeitungsvertrag, der zwischen der GEDISA und den Apotheken zu schließen ist, wird über das
            Portal bereitgestellt. Das datenschutzrechtlich erforderliche Einverständnis der zertifikatsausstellenden
            Apotheken mit diesen Vertragsbedingungen ist im Registrierungsbereich des Portals durch Anklicken des
            entsprechenden Feldes zu erklären. Für die apothekeninterne Dokumentation kann der GEDISA-seitig bereits
            unterschriebene <a href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                               target="_blank" class="underline text-gray-600">Vertrag</a> über das Portal
            heruntergeladen werden.
        </p>
    </div>

    <div class="mt-2">
        <ul class="list-disc pl-5">
            <li>
                Dieser Vertrag sollte von der Apotheke aufbewahrt werden.
            </li>
            <li>
                Eine Übersendung einer seitens der Apotheke unterzeichneten Fassung an die GEDISA ist nicht erforderlich.
            </li>
        </ul>
    </div>

    <form wire:submit="acceptAvv">
        <div class="mt-4">
            <x:input.checkbox
                wire:model.live="avvCheckboxAccepted"
                id="data-processing-contract-checkbox"
                :error="$errors->first('avvCheckboxAccepted')"
            >
                Ich erkläre mein Einverständnis mit dem <a
                    href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}" target="_blank"
                    class="underline text-gray-600">Vertrag zur Auftragsverarbeitung</a> zwischen der GEDISA und mir.
            </x:input.checkbox>
        </div>

        <div class="mt-4 flex justify-end">
            <x:button type="submit">Auftragsverarbeitungsvertrag akzeptieren</x:button>
        </div>
    </form>
</div>
