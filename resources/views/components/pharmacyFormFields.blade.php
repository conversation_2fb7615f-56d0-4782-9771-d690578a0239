@php
    use App\Features\IhreApotheken;
    use App\Integrations\IntegrationTypeEnum;
    use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
    use App\Domains\Subscription\Application\StripeProducts\AddOns\TIMStripeProduct;
@endphp
@if ($errors->any())
    <div class="mb-8">
        <x-alert-validation-error/>
    </div>
@endif

{{-- Basisdaten --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">

        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Basisdaten
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'name',
            'labelText' => trans('validation.attributes.pharmacy_name'),
            'initialValue' => old('name') ?? (isset($model) ? $model->name : null),
            'config' => [
                'required' => true,
            ],
            'helpText' =>
                isset($model) && $model->getPendingAttribute('name')
                    ? 'Wartet noch auf Bestätigung: ' . $model->getPendingAttribute('name')->change['newValue']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'number',
            'inputName' => 'institute_id',
            'labelText' => trans('validation.attributes.institute_id'),
            'initialValue' => old('institute_id') ?? (isset($model) ? $model->institute_id : null),
            'config' => [
                'required' => false,
            ],
            'helpText' =>
                isset($model) && $model->getPendingAttribute('institute_id')
                    ? 'Wartet noch auf Bestätigung: ' .
                        $model->getPendingAttribute('institute_id')->change['newValue']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'commercial_register',
            'labelText' => trans('validation.attributes.commercial_register'),
            'initialValue' => old('commercial_register') ?? (isset($model) ? $model->commercial_register : null),
            'config' => [
                'required' => false,
            ],
            'helpText' =>
                isset($model) && $model->getPendingAttribute('commercial_register')
                    ? 'Wartet noch auf Bestätigung: ' .
                        $model->getPendingAttribute('commercial_register')->change['newValue']
                    : '',
        ])

        @if (
            (isset($model) && $model->owner() && $model->owner()->association_id == '12') ||
                (!isset($model) && user()->association_id == '12'))
            @include('components.inputField', [
                'type' => 'text',
                'inputName' => 'pharmacy_id',
                'labelText' => trans('validation.attributes.pharmacy_id'),
                'initialValue' => old('pharmacy_id') ?? (isset($model) ? $model->pharmacy_id : null),
                'config' => [
                    'required' => false,
                ],
                'helpText' =>
                    isset($model) && $model->getPendingAttribute('pharmacy_id')
                        ? 'Wartet noch auf Bestätigung: ' .
                            $model->getPendingAttribute('pharmacy_id')->change['newValue']
                        : '',
            ])
        @endif

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'optional_address_line',
            'labelText' => trans('validation.attributes.optional_address_line'),
            'initialValue' =>
                old('optional_address_line') ?? (isset($model) ? $model->optional_address_line : null),
            'config' => [],
            'helpText' =>
                isset($model) &&
                $model->getPendingAttribute('address') &&
                $model->getPendingAttribute('address')->change['optional_address_line'] !=
                    $model->optional_address_line
                    ? 'Wartet noch auf Bestätigung: ' .
                        $model->getPendingAttribute('address')->change['optional_address_line']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'street',
            'labelText' => trans('validation.attributes.street'),
            'initialValue' => old('street') ?? (isset($model) ? $model->street : null),
            'config' => [
                'required' => true,
            ],
            'helpText' =>
                isset($model) &&
                $model->getPendingAttribute('address') &&
                $model->getPendingAttribute('address')->change['street'] != $model->street
                    ? 'Wartet noch auf Bestätigung: ' . $model->getPendingAttribute('address')->change['street']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'house_number',
            'labelText' => trans('validation.attributes.house_number'),
            'initialValue' => old('house_number') ?? (isset($model) ? $model->house_number : null),
            'config' => [
                'required' => true,
            ],
            'helpText' =>
                isset($model) &&
                $model->getPendingAttribute('address') &&
                $model->getPendingAttribute('address')->change['house_number'] != $model->house_number
                    ? 'Wartet noch auf Bestätigung: ' .
                        $model->getPendingAttribute('address')->change['house_number']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'number',
            'inputName' => 'postcode',
            'labelText' => trans('validation.attributes.postcode'),
            'initialValue' => old('postcode') ?? (isset($model) ? $model->postcode : null),
            'config' => [
                'required' => true,
            ],
            'helpText' =>
                isset($model) &&
                $model->getPendingAttribute('address') &&
                $model->getPendingAttribute('address')->change['postcode'] != $model->postcode
                    ? 'Wartet noch auf Bestätigung: ' . $model->getPendingAttribute('address')->change['postcode']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'city',
            'labelText' => trans('validation.attributes.city'),
            'initialValue' => old('city') ?? (isset($model) ? $model->city : null),
            'config' => [
                'required' => true,
            ],
            'helpText' =>
                isset($model) &&
                $model->getPendingAttribute('address') &&
                $model->getPendingAttribute('address')->change['city'] != $model->city
                    ? 'Wartet noch auf Bestätigung: ' . $model->getPendingAttribute('address')->change['city']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'phone',
            'labelText' => trans('validation.attributes.phone'),
            'initialValue' => old('phone') ?? (isset($model) ? $model->phone : null),
            'config' => [
                'required' => true,
            ],
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'fax',
            'labelText' => trans('validation.attributes.fax'),
            'initialValue' => old('fax') ?? (isset($model) ? $model->fax : null),
            'config' => [
                'required' => true,
            ],
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'email',
            'labelText' => trans('validation.attributes.email'),
            'initialValue' => old('email') ?? (isset($model) ? $model->email : null),
            'config' => [
                'required' => true,
            ],
        ])

        <div class="border-b border-gray-200">
            <div class="text flex flex-wrap px-6 py-4 leading-5">
                <div class="flex w-full flex-col py-1 pr-4 font-medium leading-5 sm:w-1/2">
                    <span>Rechnungsadresse *</span>
                </div>
                <div class="mt-1 w-full sm:w-1/2">
                    @if (isset($model))
                        @can('selectBillingAddress', $model)
                            <x-input.select
                                    id="billing_address_id"
                                    name="billing_address_id"
                                    :options="$billingAddresses"
                                    :selected="$model->billingAddress->id"
                                    :error="$errors->first('billing_address')"
                            />
                        @else
                            <div class="text-sm text-gray-600">
                                Sie sind nicht berechtigt, die Rechnungsadresse zu ändern.
                            </div>
                        @endcan
                        @can('viewAny', \App\BillingAddress::class)
                            <a
                                    class="mt-2 block text-sm text-red-500"
                                    href="{{ route('users.billing-addresses') }}"
                            >
                                Rechnungsadressen verwalten
                            </a>
                        @endcan
                    @else
                        <div class="text-sm text-gray-600">
                            Zunächst wird die oben angegebene Adresse verwendet. Nach Hinzufügen der Apotheke,
                            können
                            Sie <a
                                    class="text-red-500"
                                    href="{{ route('users.billing-addresses') }}"
                            >hier</a>
                            eine abweichende Rechnungsadresse angeben.
                        </div>
                    @endif
                </div>
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'website',
            'labelText' => trans('validation.attributes.website'),
            'initialValue' => old('website') ?? (isset($model) ? $model->website : null),
            'config' => [
                'infoText' =>
                    trans('messages.websiteFormatInfo') .
                    '<br>' .
                    trans('messages.example_short') .
                    ' ' .
                    config('app.url'),
            ],
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'facebook',
            'labelText' => trans('validation.attributes.facebook'),
            'initialValue' => old('facebook') ?? (isset($model) ? $model->facebook : null),
            'config' => [
                'required' => false,
            ],
            'helpText' =>
                isset($model) && $model->getPendingAttribute('facebook')
                    ? 'Wartet noch auf Bestätigung: ' .
                        $model->getPendingAttribute('facebook')->change['newValue']
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'instagram',
            'labelText' => trans('validation.attributes.instagram'),
            'initialValue' => old('instagram') ?? (isset($model) ? $model->instagram : null),
            'config' => [
                'required' => false,
            ],
            'helpText' =>
                isset($model) && $model->getPendingAttribute('instagram')
                    ? 'Wartet noch auf Bestätigung: ' .
                        $model->getPendingAttribute('instagram')->change['newValue']
                    : '',
        ])

    </div>
</div>

{{-- NGDA --}}
@if (isset($model) &&
        \Laravel\Pennant\Feature::for(\App\Enums\FeatureScope::App->value)->active(\App\Features\IntegrateNgda::class))
    @php
        $integration = $model->getIntegration(\App\Integrations\IntegrationTypeEnum::NGDA);
    @endphp
    <div class="mb-8 rounded-lg bg-white shadow">
        <div
                class="@if (showTermsOfUseHeaderBanner()) -top-32 @endif invisible relative -top-20"
                id="ngda"
        ></div>
        <div class="px-4 pt-1 sm:px-6 sm:pt-2">
            <div class="border-b border-gray-200 px-6 py-4">
                <div
                        class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700"
                        id="activate-ngda"
                >
                    N-ID-Verknüpfung
                </div>
            </div>
            <div>
                <div class="border-b border-gray-200">
                    <div class="text flex flex-wrap px-6 py-4 leading-5">
                        <p
                                class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                for="corona_rapid_test2"
                        >
                            N-ID Ihrer Betriebsstätte
                            <span
                                    class="tooltip"
                                    data-tippy-content="N-ID der Registrierung Ihrer Betriebstätte (Sie finden diese auf der Rechnung der NGDA)"
                            >
                                <svg class="inline-block h-4 w-4 text-gray-400 hover:text-gray-500">
                                    <use href="/icons.svg#question-mark-circle"/>
                                </svg>
                            </span>
                        </p>
                        <div class="mt-1 w-full sm:w-1/2">
                            @if ($integration?->settings->refreshAt > now())
                                <p class="mt-1 text-sm font-bold text-gray-600">
                                    {{ $integration->settings->id }}
                                </p>
                                <p
                                        class="mt-2 text-sm text-gray-600"
                                        role="alert"
                                >
                                    Ihre Apotheke ist mit Ihrer N-ID der Betriebsstätte verknüpft.
                                </p>
                                <x-ngda.detach-modal buttonTitle="Entkoppeln"/>
                            @else
                                <x-ngda.ngda-modal
                                        buttonTitle="N-ID-Account verknüpfen und NNF-Übermittlung aktivieren"/>

                                <div
                                        class="mt-2 text-sm text-gray-600"
                                        role="alert"
                                >
                                    Ich erlaube der GEDISA die Bestätigung meiner KIM-Adresse beim Nacht- und
                                    Notdienstfonds des DAV e. V. (NNF).
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @if ($integration?->settings->refreshAt > now())
                <div>
                    <div class="border-b border-gray-200">
                        <div class="text flex flex-wrap px-6 py-4 leading-5">
                            <label
                                    class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                    for="corona_rapid_test2"
                            >
                                NNF-Übermittlung aktivieren
                            </label>
                            <div class="mt-1 w-full sm:w-1/2">
                                <div>
                                    <x-input.toggle
                                            id="accepted_nnf"
                                            name="accepted_nnf"
                                            x-model="accepted_nnf"
                                            checked="{{ $integration->settings->acceptedNnf }}"
                                            helpText="Ich erlaube der GEDISA die Bestätigung meiner KIM-Adresse beim Nacht- und Notdienstfonds des DAV e. V. (NNF)."
                                            :error="$errors->first('accepted_nnf')"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
            <div>
                <div class="border-b border-gray-200">
                    <div class="text px-6 py-4 leading-5">
                        <p class="mt-2 text-sm text-gray-600">Achtung: Ihre Verknüpfung ist maximal 90
                            Tage gültig. Nach Ablauf dieser Frist ist eine erneute Verknüpfung
                            notwendig.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif

{{-- Inländischer Versandhandel --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Inländischer Versandhandel
            </div>
        </div>

        <div x-data="{
            shipping_pharmacy_enabled: {{ (old('shipping_pharmacy_enabled') ? !!old('shipping_pharmacy_enabled') : (isset($model) ? $model->shipping_pharmacy_enabled : false)) ? 'true' : 'false' }}
        }">
            <div class="border-b border-gray-200">
                <div class="text flex flex-wrap px-6 py-4 leading-5">
                    <label
                            class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                            for="corona_rapid_test2"
                    >
                        Meine Apotheke ermöglicht einen inländischen Versandhandel
                    </label>
                    <div class="mt-1 w-full sm:w-1/2">
                        <div>
                            <x-input.toggle
                                    id="shipping_pharmacy_enabled"
                                    name="shipping_pharmacy_enabled"
                                    x-model="shipping_pharmacy_enabled"
                                    :error="$errors->first('shipping_pharmacy_enabled')"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div x-show="shipping_pharmacy_enabled">
                @include('components.inputField', [
                    'type' => 'text',
                    'inputName' => 'shipping_pharmacy_name',
                    'labelText' => trans('validation.attributes.shipping_pharmacy_name'),
                    'initialValue' =>
                        old('shipping_pharmacy_name') ?? (isset($model) ? $model->shipping_pharmacy_name : null),
                    'config' => [
                        'required' => false,
                    ],
                ])

                @include('components.inputField', [
                    'type' => 'text',
                    'inputName' => 'shipping_pharmacy_website',
                    'labelText' => trans('validation.attributes.shipping_pharmacy_website'),
                    'initialValue' =>
                        old('shipping_pharmacy_website') ??
                        (isset($model) ? $model->shipping_pharmacy_website : null),
                    'config' => [
                        'required' => false,
                        'infoText' =>
                            trans('messages.websiteFormatInfo') .
                            '<br>' .
                            trans('messages.example_short') .
                            ' ' .
                            config('app.url'),
                    ],
                ])
            </div>
        </div>
    </div>
</div>

{{-- Eingesetzte Systeme --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Eingesetzte Systeme
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'select',
            'inputName' => 'goods_management_system',
            'labelText' => trans('validation.attributes.goodsManagementSystem'),
            'initialValue' =>
                old('goods_management_system') ??
                (isset($model)
                    ? $model->goods_management_system_id ?? $model->custom_goods_management_system
                    : null),
            'config' => [
                'required' => true,
                'canHasCustomInput' => true,
                'values' => $allGoodsManagementSystems,
                'prefix' => [
                    'label' => '',
                    'disabled' => true,
                ],
                'infoManagementSystemAndAccountingCenter' => true,
                'relatedValues' => ['goods_management_system_id', 'custom_goods_management_system'],
                'infoText' => trans('messages.selectOrManually'),
            ],
        ])

        @include('components.inputField', [
            'type' => 'select',
            'inputName' => 'accounting_center',
            'labelText' => trans('validation.attributes.accountingCenter'),
            'initialValue' =>
                old('accounting_center') ??
                (isset($model) ? $model->accounting_center_id ?? $model->custom_accounting_center : null),
            'config' => [
                'required' => true,
                'canHasCustomInput' => true,
                'values' => $allAccountingCenters,
                'prefix' => [
                    'label' => '',
                    'disabled' => true,
                ],
                'infoManagementSystemAndAccountingCenter' => true,
                'relatedValues' => ['accounting_center_id', 'custom_accounting_center'],
                'infoText' => trans('messages.selectOrManually'),
            ],
        ])
    </div>
</div>

{{-- E-Rezept --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                E-Rezept
            </div>
        </div>
        <div class="text flex flex-wrap px-6 py-4 leading-5">
            <label
                    class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                    for="accepts_e_prescription"
            >
                Ich kann in meiner Apotheke die E-Rezepte der Telematikinfrastruktur verarbeiten. Ich habe alle
                notwendigen Vorbereitungen aus der <a
                        class="text-red-500 underline"
                        href="https://www.gematik.de/fileadmin/user_upload/gematik/files/Infoblaetter/rz_210211_gem_infoblaetter_E-Rezept_Apotheke_08_2020_V05.pdf"
                        target="_blank"
                >Checkliste</a> vorgenommen
            </label>
            <div class="mt-1 w-full sm:w-1/2">
                <div>
                    <x-input.toggle
                            id="accepts_e_prescription"
                            name="accepts_e_prescription"
                            :error="$errors->first('accepts_e_prescription')"
                            checked="{{ old('accepts_e_prescription') ? !!old('accepts_e_prescription') : (isset($model) ? $model->accepts_e_prescription : false) }}"
                    />
                </div>
            </div>
            <span class="mt-2 text-sm text-red-500"><span class="font-bold">Hinweis:</span> Wenn Sie dies bestätigen,
                können Patientinnen und Patienten ihre E-Rezepte in Ihrer Apotheke einlösen. Bitte stellen Sie daher
                sicher, dass sie diese wirklich verarbeiten können.</span>
        </div>
    </div>
</div>

{{-- Telematik-ID --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Telematik-ID
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'telematics_id',
            'labelText' => trans('validation.attributes.telematics_id'),
            'initialValue' => old(
                'telematics_id',
                isset($model) && $model->telematicsId ? $model->telematicsId->fullId() : null), // TODO: value
            'config' => [
                'required' => false,
                'infoText' => trans('messages.telematicsIdDescriptionText'),
            ],
        ])

    </div>
</div>

{{-- Digitales Impfzertifikat  --}}
@if (isCovidVaccinationCertificateCenterActive())
    <div class="mb-8 rounded-lg bg-white shadow">
        <div class="px-4 pt-1 sm:px-6 sm:pt-2">
            <div class="border-b border-gray-200 px-6 py-4">
                <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                    COVID-19-Zertifikat
                </div>
            </div>

            <div x-data="{
                featureEnabled: {{ old('vaccination_import') ? !!old('vaccination_import') : (isset($model) ? ($model->vaccination_import ? 1 : 0) : 0) }},
                customAddress: {{ old('custom_vaccination_import_address') ? !!old('custom_vaccination_import_address') : (isset($model) ? ($model->hasVaccinationImportAddress() ? 1 : 0) : 0) }}
            }">
                <div class="border-b border-gray-200">
                    <div class="text flex flex-wrap px-6 py-4 leading-5">
                        <label
                                class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                for="vaccination_import"
                        >
                            Meine Apotheke stellt COVID-19-Zertifikate aus
                        </label>
                        <div class="mt-1 w-full sm:w-1/2">
                            <div>
                                <x-input.toggle
                                        id="vaccination_import"
                                        name="vaccination_import"
                                        x-model="featureEnabled"
                                        :error="$errors->first('vaccination_import')"
                                />
                            </div>
                        </div>
                        <span class="mt-2 text-sm text-red-500"><span class="font-bold">Hinweis:</span> Um diese
                            Funktion zu aktivieren, muss die Telematik-ID dieser Apotheke ausgefüllt werden
                            (s.o.).</span>
                    </div>
                </div>

                <div x-show="featureEnabled">
                    <div class="border-b border-gray-200">
                        <div class="text flex flex-wrap px-6 py-4 leading-5">
                            <label
                                    class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                    for="custom_vaccination_import_address"
                            >
                                Zertifikate werden an einer abweichenden Adresse ausgestellt
                            </label>
                            <div class="mt-1 w-full sm:w-1/2">
                                <div>
                                    <x-input.toggle
                                            id="custom_vaccination_import_address"
                                            name="custom_vaccination_import_address"
                                            x-model="customAddress"
                                            :error="$errors->first('custom_vaccination_import_address')"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div x-show="customAddress">
                        @include('components.inputField', [
                            'type' => 'text',
                            'inputName' => 'vaccination_import_optional_address_line',
                            'labelText' => trans('validation.attributes.optional_address_line'),
                            'initialValue' =>
                                old('vaccination_import_optional_address_line') ??
                                (isset($model) && $model->hasVaccinationImportAddress()
                                    ? $model->vaccinationImportAddress->optional_address_line
                                    : null),
                            'config' => [],
                        ])

                        @include('components.inputField', [
                            'type' => 'text',
                            'inputName' => 'vaccination_import_street',
                            'labelText' => trans('validation.attributes.street'),
                            'initialValue' =>
                                old('vaccination_import_street') ??
                                (isset($model) && $model->hasVaccinationImportAddress()
                                    ? $model->vaccinationImportAddress->street
                                    : null),
                            'config' => [],
                        ])

                        @include('components.inputField', [
                            'type' => 'text',
                            'inputName' => 'vaccination_import_house_number',
                            'labelText' => trans('validation.attributes.house_number'),
                            'initialValue' =>
                                old('vaccination_import_house_number') ??
                                (isset($model) && $model->hasVaccinationImportAddress()
                                    ? $model->vaccinationImportAddress->house_number
                                    : null),
                            'config' => [],
                        ])

                        @include('components.inputField', [
                            'type' => 'number',
                            'inputName' => 'vaccination_import_postcode',
                            'labelText' => trans('validation.attributes.postcode'),
                            'initialValue' =>
                                old('vaccination_import_postcode') ??
                                (isset($model) && $model->hasVaccinationImportAddress()
                                    ? $model->vaccinationImportAddress->postcode
                                    : null),
                            'config' => [],
                        ])

                        @include('components.inputField', [
                            'type' => 'text',
                            'inputName' => 'vaccination_import_city',
                            'labelText' => trans('validation.attributes.city'),
                            'initialValue' =>
                                old('vaccination_import_city') ??
                                (isset($model) && $model->hasVaccinationImportAddress()
                                    ? $model->vaccinationImportAddress->city
                                    : null),
                            'config' => [],
                        ])
                    </div>
                </div>
            </div>

        </div>
    </div>
@endif

{{-- COVID-19 Schnelltest --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                COVID-19 Schnelltest
            </div>
        </div>

        <div x-data="{
            covidTest: @js((bool) (old('corona_rapid_test') ?? ($model?->corona_rapid_test ?? false)))
        }">
            <div class="border-b border-gray-200">
                <div class="text flex flex-wrap px-6 py-4 leading-5">
                    <label
                            class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                            for="corona_rapid_test2"
                    >
                        Meine Apotheke ermöglicht kostenlose COVID-19 Schnelltests
                    </label>
                    <div class="mt-1 w-full sm:w-1/2">
                        <div>
                            <x-input.toggle
                                    id="corona_rapid_test"
                                    name="corona_rapid_test"
                                    x-model="covidTest"
                                    :error="$errors->first('corona_rapid_test')"
                            />
                        </div>
                    </div>
                    <span class="mt-2 text-sm text-red-500"><span class="font-bold">Hinweis:</span> Die Registrierung
                        im
                        Apothekenportal ersetzt nicht die vorherige Beauftragung durch den öffentlichen
                        Gesundheitsdienst.</span>
                </div>
            </div>

            <div x-show="covidTest">
                @include('components.inputField', [
                    'type' => 'text',
                    'inputName' => 'corona_rapid_test_booking_url',
                    'labelText' => trans('validation.attributes.corona_rapid_test_booking_url'),
                    'initialValue' =>
                        old('corona_rapid_test_booking_url') ??
                        (isset($model) ? $model->corona_rapid_test_booking_url : null),
                    'config' => [
                        'required' => false,
                        'infoText' =>
                            trans('messages.websiteFormatInfo') .
                            '<br>' .
                            trans('messages.example_short') .
                            ' ' .
                            config('app.url'),
                    ],
                ])
            </div>
        </div>
    </div>
</div>

@can('administrateSettings', [\App\PharmaceuticalService::class, $model ?? null])
    {{-- Pharmazeutische Dienstleistungen --}}
    <div class="mb-8 rounded-lg bg-white shadow">
        <div class="px-4 pt-1 sm:px-6 sm:pt-2">
            <div class="border-b border-gray-200 px-6 py-4">
                <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                    Pharmazeutische Dienstleistungen
                </div>
            </div>

            <div x-data="{
                does_pharmaceutical_services: @js(old('does_pharmaceutical_services') ? !!old('does_pharmaceutical_services') : isset($model) && $model->does_pharmaceutical_services),
            }">
                <div class="border-b border-gray-200">
                    <div class="text px-6 py-4 leading-5">
                        <div class="flex flex flex-wrap">
                            <label
                                    class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                    for="corona_rapid_test2"
                            >
                                Meine Apotheke bietet pharmazeutische Dienstleistungen an.
                            </label>
                            <div class="mt-1 w-full sm:w-1/2">
                                <x-input.toggle
                                        id="does_pharmaceutical_services"
                                        name="does_pharmaceutical_services"
                                        x-model="does_pharmaceutical_services"
                                        :error="$errors->first('does_pharmaceutical_services')"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div x-show="does_pharmaceutical_services">
                    <div class="text px-6 py-4">
                        <h4 class="font-medium">
                            Meine Apotheke bietet folgende pDL an:
                        </h4>
                    </div>

                    @foreach ($pharmaceuticalServiceTypes as $topic)
                        <div class="border-b border-gray-200">
                            <div class="text flex flex-wrap px-6 py-4 leading-5">
                                <label
                                        class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                        for="corona_rapid_test2"
                                >
                                    {{ $topic->name }}
                                </label>
                                <div class="mt-1 w-full sm:w-1/2">
                                    <div>
                                        <x-input.toggle
                                                id="pharmaceutical_service_topics_{{ $topic->id }}"
                                                name="pharmaceuticalServiceTypes[]"
                                                value="{{ $topic->id }}"
                                                :checked="isset($selectedPharmaceuticalServiceTypes) &&
                                                $selectedPharmaceuticalServiceTypes->contains('id', $topic->id)"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endcan

@if (user()->can('chat', [user(), $model ?? null]) &&
        user()->can('activateChat', isset($model) ? $model : \App\Pharmacy::class))

    @if($model->getAssociationFrameworkContract() == AssociationFrameworkContractEnum::PlusAssociationFrameworkContract->instance() ||
            $model->canUseProduct(TIMStripeProduct::class) || $model->uses_chat)
        {{-- Chat --}}
        <div class="relative mb-8 rounded-lg bg-white shadow">
            <div
                    class="@if (showTermsOfUseHeaderBanner()) -top-32 @endif invisible relative -top-20"
                    id="activate_chat"
            ></div>
            <div class="px-4 pt-1 sm:px-6 sm:pt-2">
                <div class="border-b border-gray-200 px-6 py-4">
                    <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                        Chat
                    </div>
                </div>

                <div
                        x-data="{
                        usesChat: @js(old('uses_chat') ? !!old('uses_chat') : isset($model) && ((bool) $model->uses_chat)),
                        usesPatientChat: @js(old('uses_patient_chat') ? !!old('uses_patient_chat') : isset($model) && !old() && $model->uses_patient_chat),
                        acceptsChat: @js(old('accepts_chat') ? !!old('accepts_chat') : isset($model) && !old() && $model->uses_chat),
                    }"
                        x-init="$nextTick(() => {
                        $watch('usesChat', (value) => {
                            if (!value) {
                                usesPatientChat = false;
                            }
                        });
                    })"
                >
                    <div class="border-b border-gray-200">
                        <div class="text px-6 py-4 leading-5">
                            <div class="flex flex-wrap">
                                <label
                                        class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                        for="corona_rapid_test2"
                                >
                                    Freischaltung der Chat Funktion für die interne Kommunikation
                                </label>
                                <div class="mt-1 w-full sm:w-1/2">
                                    <!-- TOOD (CL): Auf die korrekte Implementierung umbauen. -->
                                    @if (isset($model, $model->cardLinkOrder) && \App\Data\CardLink\OrderInformationData::fromCardLinkOrder($model->cardLinkOrder)?->activateApoGuideVendor)
                                        <p class="text-sm text-gray-500">Die Funktion kann nicht deaktiviert werden, da Sie
                                            Cardlink für ApoGuide nutzen.</p>
                                    @else
                                        <x-input.toggle
                                                id="uses_chat"
                                                name="uses_chat"
                                                x-model="usesChat"
                                                :error="$errors->first('uses_chat')"
                                        />
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div
                            class="border-b border-gray-200"
                            x-show="usesChat"
                    >
                        <div class="text px-6 py-4 leading-5">
                            <div class="flex flex-wrap">
                                <label
                                        class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                        for="uses_patient_chat"
                                >
                                    Freischaltung der Chat Funktion für die externe (Kunden) Kommunikation
                                </label>
                                <div class="mt-1 w-full sm:w-1/2">
                                    @if (isset($model, $model->cardLinkOrder) && \App\Data\CardLink\OrderInformationData::fromCardLinkOrder($model->cardLinkOrder)?->activateApoGuideVendor)
                                        <p class="text-sm text-gray-500">Die Funktion kann nicht deaktiviert werden, da Sie
                                            Cardlink für ApoGuide nutzen.</p>
                                    @else
                                        <x-input.toggle
                                                id="uses_patient_chat"
                                                name="uses_patient_chat"
                                                x-model="usesPatientChat"
                                                :error="$errors->first('uses_patient_chat')"
                                        />
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div x-show="usesChat">
                        <div class="border-b border-gray-200">
                            <div class="text px-6 py-4 leading-5">
                                <div class="flex flex-wrap">
                                    <label
                                            class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                            for="corona_rapid_test2"
                                    >

                                        Mit der Freischaltung des Kommunikationsdienstes akzeptiere ich die
                                        <a
                                                class="text-blue-500 hover:underline"
                                                href="{{ route('uploads.show', 'nutzungsbedingungen-apothekenportal') }}"
                                                target="_blank"
                                        >
                                            Nutzungsbedingungen
                                            <svg class="mb-0.5 inline-block h-4 w-4">
                                                <use href="/icons.svg#arrow-long-top-right-on-square"/>
                                            </svg>
                                        </a>.

                                    </label>
                                    <div class="mt-1 w-full sm:w-1/2">
                                        @if (isset($model, $model->cardLinkOrder) && \App\Data\CardLink\OrderInformationData::fromCardLinkOrder($model->cardLinkOrder)?->activateApoGuideVendor)
                                            <p class="text-sm text-gray-500">Die Funktion kann nicht deaktiviert werden, da
                                                Sie Cardlink für ApoGuide nutzen.</p>
                                        @else
                                            <x-input.toggle
                                                    id="accepts_chat"
                                                    name="accepts_chat"
                                                    x-model="acceptsChat"
                                                    :error="$errors->first('accepts_chat')"
                                            />
                                        @endif
                                    </div>
                                </div>
                                <span
                                        class="mt-2 text-sm text-red-500"
                                        x-show="usesChat && !accepts_chat"
                                ><span class="font-bold">Hinweis:</span> Um den Chat zu nutzen, müssen Sie die
                                    Nutzungsbedingungen akzeptieren.</span>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    @endif
@endif

{{-- SDR --}}
<x-sdr.sdr-activation-toggle :model="$model ?? null"></x-sdr.sdr-activation-toggle>

@if (user()->can('activateCalendar', $model ?? \App\Pharmacy::class))
    {{-- Kalender --}}
    {{-- Terminbuchungen --}}
    <div class="mb-8 rounded-lg bg-white shadow">
        <div class="px-4 pt-1 sm:px-6 sm:pt-2">
            <div class="border-b border-gray-200 px-6 py-4">
                <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                    Terminbuchungen
                </div>
            </div>

            <div x-data="{
                usesCalendar: @js(old('uses_calendar') ? !!old('uses_calendar') : isset($model) && $model->uses_calendar),
                acceptsCalendar: @js(old('accepts_calendar') ? !!old('accepts_calendar') : isset($model) && !old() && $model->uses_calendar),
            }">
                <div class="border-b border-gray-200">
                    <div class="text px-6 py-4 leading-5">
                        <div class="flex flex flex-wrap">
                            <label
                                    class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                    for="corona_rapid_test2"
                            >
                                Meine Apotheke ermöglicht Terminbuchungen via Apomondo
                            </label>
                            <div class="mt-1 w-full sm:w-1/2">
                                <x-input.toggle
                                        id="uses_calendar"
                                        name="uses_calendar"
                                        x-model="usesCalendar"
                                        :error="$errors->first('uses_calendar')"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div x-show="usesCalendar">
                    <div class="border-b border-gray-200">
                        <div class="text px-6 py-4 leading-5">
                            <div class="flex flex flex-wrap">
                                <label
                                        class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                        for="corona_rapid_test2"
                                >
                                    Ich willige ein, dass meine Daten an den Kooperationspartner Apomondo GmbH
                                    übermittelt werden.
                                </label>
                                <div class="mt-1 w-full sm:w-1/2">
                                    <x-input.toggle
                                            id="accepts_calendar"
                                            name="accepts_calendar"
                                            x-model="acceptsCalendar"
                                            :error="$errors->first('accepts_calendar')"
                                    />
                                </div>
                            </div>
                            <span
                                    class="mt-2 text-sm text-red-500"
                                    x-show="usesCalendar && !acceptsCalendar"
                            ><span class="font-bold">Hinweis:</span> Um Terminbuchungen zu aktivieren müssen Sie der
                                Datenübermittlung zustimmen.</span>
                        </div>
                    </div>
                    @include('components.inputField', [
                        'type' => 'text',
                        'inputName' => 'calendar_email',
                        'labelText' => 'E-Mail-Adresse für Terminbuchungen',
                        'initialValue' =>
                            old('calendar_email') ?? (isset($model) ? $model->calendar_email : null),
                        'config' => [
                            'required' => false,
                            'infoText' =>
                                'Optional, wenn Sie die Benachrichtigungen über Terminbuchungen an eine bestimmte E-Mail-Adresse schicken möchten.',
                        ],
                    ])

                    <div class="text px-6 py-4">
                        <h4 class="font-medium">
                            Meine Apotheke bietet Termine zu folgenden Themen an:
                        </h4>
                    </div>

                    @foreach ($calendarTopics as $topic)
                        <div class="border-b border-gray-200">
                            <div class="text flex flex-wrap px-6 py-4 leading-5">
                                <label
                                        class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                        for="corona_rapid_test2"
                                >
                                    {{ $topic->name }}
                                </label>
                                <div class="mt-1 w-full sm:w-1/2">
                                    <div>
                                        <x-input.toggle
                                                id="calendar_topic_{{ $topic->id }}"
                                                name="calendarTopics[]"
                                                value="{{ $topic->id }}"
                                                :checked="isset($selectedCalendarTopics) &&
                                                $selectedCalendarTopics->contains('id', $topic->id)"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach

                    @if (isset($model) && $model->calendar_uuid && $model->uses_calendar)
                        <div class="px-6 py-4">
                            <span class="mt-2 text-sm text-gray-600">
                                Für Ihre Patienten ist die Terminbuchung erreichbar unter: <br>
                                <a
                                        class="text-red-500 underline"
                                        href="{{ \App\Helper\ApomondoApi::getUrl($model) }}"
                                        target="_blank"
                                >{{ \App\Helper\ApomondoApi::getUrl($model) }}</a>
                            </span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endif

@can('activateTelepharmacy', $model ?? \App\Pharmacy::class)
    {{-- Telepharmazie --}}
    <div
            class="mb-8 rounded-lg bg-white shadow"
            dusk="activate-telepharmacy"
    >
        <div class="px-4 pt-1 sm:px-6 sm:pt-2">
            <div class="border-b border-gray-200 px-6 py-4">
                <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                    Telepharmazie
                </div>
            </div>

            <div x-data="{
                usesTelepharmacy: @js(old('uses_telepharmacy') ? !!old('uses_telepharmacy') : isset($model) && $model->uses_telepharmacy),
                acceptsTelepharmacy: @js(old('accepts_telepharmacy') ? !!old('accepts_telepharmacy') : isset($model) && !old() && $model->uses_telepharmacy),
            }">
                <div class="border-b border-gray-200">
                    <div class="text px-6 py-4 leading-5">
                        <div class="flex flex flex-wrap">
                            <label
                                    class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                    for="uses_telepharmacy"
                            >
                                Meine Apotheke ermöglicht Telepharmazie via Apomondo
                            </label>
                            <div class="mt-1 w-full sm:w-1/2">
                                <x-input.toggle
                                        id="uses_telepharmacy"
                                        name="uses_telepharmacy"
                                        x-model="usesTelepharmacy"
                                        :error="$errors->first('uses_telepharmacy')"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div x-show="usesTelepharmacy">
                    <div class="border-b border-gray-200">
                        <div class="text px-6 py-4 leading-5">
                            <div class="flex flex flex-wrap">
                                <label
                                        class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                        for="accepts_telepharmacy"
                                >
                                    Ich willige ein, dass meine Daten an den Kooperationspartner Apomondo GmbH
                                    übermittelt werden.
                                </label>
                                <div class="mt-1 w-full sm:w-1/2">
                                    <x-input.toggle
                                            id="accepts_telepharmacy"
                                            name="accepts_telepharmacy"
                                            x-model="acceptsTelepharmacy"
                                            :error="$errors->first('accepts_telepharmacy')"
                                    />
                                </div>
                            </div>
                            <span
                                    class="mt-2 text-sm text-red-500"
                                    x-show="usesTelepharmacy && !acceptsTelepharmacy"
                            ><span class="font-bold">Hinweis:</span> Um Telepharmazie zu aktivieren müssen Sie der
                                Datenübermittlung zustimmen.</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endcan

{{-- Apomail --}}
<div
        class="mb-8 rounded-lg bg-white shadow"
        dusk="activate-apomail"
>
    <div
            class="@if (showTermsOfUseHeaderBanner()) -top-32 @endif invisible relative -top-20"
            id="activate_apomail"
    ></div>
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                ApoMail
            </div>
        </div>

        <div x-data="{
            usesApomail: @js(old('uses_apomail', isset($model) && $model->uses_apomail))
        }">
            <div class="border-b border-gray-200">
                <div class="text px-6 py-4 leading-5">
                    <div class="flex flex flex-wrap">
                        <label
                                class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                                for="uses_apomail"
                        >
                            Verwaltung von ApoMail-Adressen
                        </label>
                        @can('updateTos', \App\Setting::class)
                            <div class="mt-1 w-full sm:w-1/2">
                                <x-input.toggle
                                        id="uses_apomail"
                                        name="uses_apomail"
                                        x-model="usesApomail"
                                        :error="$errors->first('uses_apomail')"
                                />
                            </div>
                            @if (isset($model) && !$model->hasAcceptedTermsAfterApomail())
                                <span class="mt-2 text-sm text-red-500">
                                    <span
                                            class="font-bold"
                                            dusk="accept-tos-hint"
                                    >Hinweis:</span> Mit der Aktivierung des Services stimmen Sie den aktualisierten <x-link
                                            href="{{ route('terms-of-use') }}"
                                            target="_blank"
                                    >Nutzungsbedingungen</x-link> und dem aktualisierten <x-link
                                        href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                                            target="_blank"
                                    >Auftragsverarbeitungsvertrag</x-link> zu.
                                </span>
                            @endif
                        @else
                            <div class="text-sm text-gray-600">
                                Sie sind nicht berechtigt, die Verwaltung von ApoMail-Adressen
                                zu {{ !isset($model) || $model->uses_apomail ? 'aktivieren' : 'deaktivieren' }}.
                            </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- ApoGuide --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                ApoGuide
            </div>

            <p class="mt-2">
                Einverständniserklärung für die Veröffentlichung in der Apothekensuche ApoGuide App.
            </p>
        </div>

        {{-- ApoGuide Poster download --}}
        <div id="apoguide_poster" x-data="{
                    showInApoguide: @js(old('show_in_apoguide') ? (!!old('show_in_apoguide')) : (isset($model) && $model->show_in_apoguide)),
                }">
            <div class="border-b border-gray-200">
                <div class="px-6 py-4 text leading-5">
                    <div class="flex flex flex-wrap">
                        <label for="corona_rapid_test2"
                               class="block w-full sm:w-1/2 font-medium pr-4 py-1 leading-5 text-gray-700">
                            Informationen zu meiner Betriebsstätte
                        </label>
                        <div class="w-full sm:w-1/2 mt-1">
                            @if (isset($model, $model->cardLinkOrder) && \App\Data\CardLink\OrderInformationData::fromCardLinkOrder($model->cardLinkOrder)?->activateApoGuideVendor)
                                <p class="text-sm text-gray-500">
                                    Die Funktion kann nicht deaktiviert werden, da Sie Cardlink für ApoGuide nutzen.
                                </p>
                            @else
                                <x:input.toggle
                                        name="show_in_apoguide"
                                        id="show_in_apoguide"
                                        x-model="showInApoguide"
                                        :error="$errors->first('show_in_apoguide')"
                                />
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @if(isset($model))
                <div x-show="showInApoguide">
                    <div class="px-6 py-4 text">
                        <h4 class="font-medium">
                            Personalisiertes Plakat
                        </h4>
                        <p class="text-sm text-gray-600">Individuelles ApoGuide-Plakat mit QR-Code herunterladen, um
                            Ihre Apotheke in der ApoGuide-App als Favorit zu speichern.</p>
                    </div>

                    <div class="md:flex border-b border-gray-200 items-end">
                        <div class="flex-1">
                            <div class="relative flex justify-center">
                                <img class="object-contain w-24 opacity-50"
                                     src="{{ asset('/assets/img/thumbnail_plakat_x3.png') }}"
                                     alt="ApoGuide Poster"/>

                                <div class="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center">
                                    <p class="text-3xl font-semibold rotate-45">Muster</p>
                                </div>
                            </div>
                            <div class="flex flex-wrap justify-center px-6 py-4 text leading-5">
                                <livewire:notification-center.close-notification-after-apo-guide-q-r-code-download
                                        :route="route('pharmacies.apoguide-poster', [$model->uuid])"
                                        text="Plakat herunterladen"
                                        :user="user()"
                                        :pharmacy="$model"
                                />
                            </div>
                        </div>

                        <div class="flex-1">
                            <div class="relative flex justify-center">
                                <img class="object-contain w-28 opacity-30"
                                     src="{{ asset('/assets/img/thumbnail_beispiel_qrcode.svg') }}"
                                     alt="ApoGuide QR Code"/>

                                <div class="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center">
                                    <p class="text-3xl font-semibold rotate-45 stroke stroke-2 stroke-white">Muster</p>
                                </div>
                            </div>
                            <div class="flex flex-wrap justify-center px-6 py-4 text leading-5">
                                <livewire:notification-center.close-notification-after-apo-guide-q-r-code-download
                                        :route="route('pharmacies.apoguide-qr-code', [$model->uuid])"
                                        text="Nur QR-Code herunterladen"
                                        :user="user()"
                                        :pharmacy="$model"
                                />
                            </div>
                        </div>

                        <div class="flex-1">
                            <div class="flex justify-center text-sm text-gray-900">
                                <a href="{{ \App\Settings\ApoGuideSettings::deepLink($model->uuid) }}"
                                   target="_blank"
                                   class="text-wrap break-all"
                                >
                                    {{ \App\Settings\ApoGuideSettings::deepLink($model->uuid) }}
                                </a>
                            </div>
                            <div class="flex flex-wrap justify-center px-6 py-4 text leading-5">
                                <x-button type="button"
                                          appearance="brand"
                                          x-clipboard="'{{ \App\Settings\ApoGuideSettings::deepLink($model->uuid) }}'"
                                          x-on:click="$event.target.innerText = 'kopiert'"
                                >
                                    {{ __('Link kopieren') }}
                                </x-button>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div x-data="{
            does_covid_vaccination: @js(old('does_covid_vaccination') ? !!old('does_covid_vaccination') : isset($model) && $model->does_covid_vaccination),
            does_influenza_vaccination: @js(old('does_influenza_vaccination') ? !!old('does_influenza_vaccination') : isset($model) && $model->does_influenza_vaccination)
        }">
            <div class="border-b border-gray-200">
                <div class="text flex flex-wrap px-6 py-4 leading-5">
                    <label
                            class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                            for="corona_rapid_test2"
                    >
                        Meine Apotheke impft gegen Grippe
                    </label>
                    <div class="mt-1 w-full sm:w-1/2">
                        <div>
                            <x-input.toggle
                                    id="does_influenza_vaccination"
                                    name="does_influenza_vaccination"
                                    x-model="does_influenza_vaccination"
                                    :error="$errors->first('does_influenza_vaccination')"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="border-b border-gray-200">
                <div class="text flex flex-wrap px-6 py-4 leading-5">
                    <label
                            class="block w-full py-1 pr-4 font-medium leading-5 text-gray-700 sm:w-1/2"
                            for="corona_rapid_test2"
                    >
                        Meine Apotheke impft gegen COVID-19
                    </label>
                    <div class="mt-1 w-full sm:w-1/2">
                        <div>
                            <x-input.toggle
                                    id="does_covid_vaccination"
                                    name="does_covid_vaccination"
                                    x-model="does_covid_vaccination"
                                    :error="$errors->first('does_covid_vaccination')"
                            />
                        </div>
                    </div>
                    <span class="mt-2 text-sm text-red-500"><span class="font-bold">Hinweis:</span> Die Registrierung
                        im Apothekenportal ersetzt nicht die notwendigen Schulungsmaßnahmen für die Impftätigkeit in
                        Apotheken.</span>
                </div>
            </div>
        </div>
    </div>
</div>

{{-- Besondere Leistungen --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Besondere Leistungen
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'toggle',
            'inputName' => 'courier_service',
            'labelText' => trans('validation.attributes.delivery'),
            'initialValue' => old('courier_service') ?? (isset($model) ? $model->courier_service : null),
            'config' => [],
        ])

        @include('components.inputField', [
            'type' => 'text',
            'inputName' => 'courier_service_radius',
            'labelText' =>
                trans('validation.attributes.courier_service_radius') . ' (' . trans('messages.inKm') . ')',
            'initialValue' =>
                old('courier_service_radius') ??
                (isset($model) && $model->courier_service_radius
                    ? str_replace('.', ',', floatval($model->courier_service_radius) / 1000)
                    : null),
            'config' => [],
        ])

        @include('components.inputField', [
            'type' => 'multiSelect',
            'inputName' => 'focus_areas',
            'labelText' => trans('validation.attributes.focusAreas'),
            'initialValue' => isset($selectedFocusAreas) ? $selectedFocusAreas : [],
            'config' => [
                'values' => $allFocusAreas,
                'labels' => trans('entities.focusAreas'),
            ],
        ])

        @include('components.inputField', [
            'type' => 'businessHours',
            'inputName' => 'business_hours',
            'labelText' => trans('validation.attributes.businessHours'),
            'initialValue' =>
                old('business_hours') ?? (isset($businessHoursAsString) ? $businessHoursAsString : null),
            'config' => [],
        ])

        @include('components.inputField', [
            'type' => 'multiSelect',
            'inputName' => 'languages',
            'labelText' => trans('validation.attributes.spokenLanguages'),
            'initialValue' => isset($selectedLanguages) ? $selectedLanguages : [],
            'config' => [
                'values' => $allLanguages,
                'labels' => trans('languages'),
            ],
        ])

    </div>
</div>

{{-- Lage --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Lage
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'toggle',
            'inputName' => 'has_near_parking_space',
            'labelText' => trans('validation.attributes.nearParkingSpace'),
            'initialValue' =>
                old('has_near_parking_space') ?? (isset($model) ? $model->has_near_parking_space : null),
            'config' => [],
        ])

        @foreach (\App\PublicTransportStation::STOPS as $stop)
            @include('components.inputField', [
                'displayError' => $errors->first('public_transport_' . $stop['key']),
                'type' => 'conditionalText',
                'inputName' => 'public_transport_' . $stop['key'],
                'labelText' => trans('validation.attributes.public_transport_' . $stop['key']),
                'initialValue' =>
                    old('public_transport_' . $stop['key']) ??
                    (isset($model) && $model->publicTransportStations->where('type', '=', $stop['id'])->first()
                        ? $model->publicTransportStations->where('type', '=', $stop['id'])->first()->stop
                        : null),
                'config' => [],
            ])
        @endforeach

        @include('components.inputField', [
            'type' => 'select',
            'inputName' => 'pharmacy_type',
            'labelText' => trans('validation.attributes.type'),
            'initialValue' => old('pharmacy_type')
                ? intval(old('pharmacy_type'))
                : (isset($model)
                    ? $model->pharmacy_type
                    : null),
            'config' => [
                'values' => $allTypes,
                'label-language-prefix' => 'entities.pharmacyTypes.',
            ],
        ])

    </div>
</div>

{{-- Profil --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Profil
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'image',
            'inputName' => 'logo',
            'labelText' => trans('messages.logo'),
            'initialValue' => old('logo')
                ? '/files/temp/' . old('logo')
                : (isset($model)
                    ? ($model->logo
                        ? url(route('pharmacies.logo', $model))
                        : '')
                    : null),
            'config' => [
                'fileType' => 'jpeg',
                'oldValue' => old('logo') ?? null,
                'viewMode' => 0, // https://github.com/fengyuanchen/cropperjs#viewmode
            ],
            'helpText' =>
                isset($model) && $model->getPendingAttribute('logo')
                    ? '<p class="mt-2 text-sm text-gray-500">Wartet noch auf Bestätigung: </p><img class="mt-3 w-64 border rounded-lg" src="' .
                        route('approvals.image.show', $model->getPendingAttribute('logo')) .
                        '">'
                    : '',
        ])

        @include('components.inputField', [
            'type' => 'image',
            'inputName' => 'image',
            'labelText' => trans('messages.image'),
            'initialValue' => old('image')
                ? '/files/temp/' . old('image')
                : (isset($model)
                    ? ($model->images->first()
                        ? url(route('pharmacies.image', $model))
                        : '')
                    : null),
            'config' => [
                'fileType' => 'jpeg',
                'width' => 865,
                'height' => 575,
                'oldValue' => old('image') ?? null,
            ],
            'helpText' =>
                isset($model) && $model->getPendingAttribute('image')
                    ? '<p class="mt-2 text-sm text-gray-500">Wartet noch auf Bestätigung: </p><img class="mt-3 w-64 border rounded-lg" src="' .
                        route('approvals.image.show', $model->getPendingAttribute('image')) .
                        '">'
                    : '',
        ])

        <div class="px-4 py-2">
            <div class="text-sm text-gray-600">
                Die Bilder müssen werbefrei sein. Unterstützte Dateiformate: jpg, png, svg, bmp, webp, gif.
            </div>
        </div>
    </div>
</div>

{{-- Einverständniserklärung --}}
<div class="mb-8 rounded-lg bg-white shadow">
    <div class="px-4 pt-1 sm:px-6 sm:pt-2">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                Einverständniserklärung zur Datenübermittlung
            </div>
        </div>

        @include('components.inputField', [
            'type' => 'toggle',
            'inputName' => 'export_added_value_to_gematik',
            'labelText' =>
                'Ich willige ein, dass die zur Betriebsstätte eingegebenen Mehrwertinformationen in der E-Rezept-App der gematik verwendet werden dürfen',
            'initialValue' =>
                old('export_added_value_to_gematik') ??
                (isset($model) ? $model->export_added_value_to_gematik : null),
            'config' => [],
        ])
    </div>
</div>

{{-- IhreApotheken --}}
@if (Feature::for('app')->active(IhreApotheken::class) && (isset($model) && $model->hasIaEnabled()))
    <div class="mb-8 rounded-lg bg-white shadow">
        <div class="px-4 pt-1 sm:px-6 sm:pt-2">
            <div class="border-b border-gray-200 px-6 py-4">
                <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                    Online-Shop (IhreApotheken Integration)
                </div>
            </div>

            @include('components.inputField', [
                'type' => 'toggle',
                'inputName' => 'ia_integration_enabled',
                'labelText' => 'Integration aktiviert',
                'labelDescription' =>
                    'Wenn Sie die Integration deaktivieren möchten, wenden Sie sich bitte an den GEDISA Support unter <a href="mailto:<EMAIL>"><EMAIL></a>. Eine Deaktivierung der Integration entspricht einer Kündigung der Einbindung des bestehenden Online-Shops ins GEDISA ApothekenPortal gemäß § 5 der Nutzungsbedingungen.',
                'disabled' => true,
                'initialValue' =>
                    old('ia_integration_enabled') ?? (isset($model) ? $model->hasIaEnabled() : null),
                'config' => [],
            ])
        </div>
    </div>
@endif

{{-- CardLink --}}
@if (isset($model) && $model->cardLinkOrder?->isTransmitted())
    <div class="mb-8 rounded-lg bg-white shadow">
        <div class="px-4 pt-1 sm:px-6 sm:pt-2">
            <div class="border-b border-gray-200 px-6 py-4">
                <div class="block w-full py-1 pr-4 text-xl font-semibold leading-5 text-gray-700">
                    CardLink
                </div>
            </div>

            @include('components.inputField', [
                'type' => 'toggle',
                'inputName' => 'card_link_enabled',
                'labelText' => 'CardLink aktiviert',
                'labelDescription' =>
                    'Wenn Sie CardLink deaktivieren möchten, wenden Sie sich bitte an den GEDISA Support unter <a href="mailto:<EMAIL>"><EMAIL></a>. Eine Deaktivierung der Integration entspricht einer Kündigung der Einbindung der bestehenden CardLink Funktion GEDISA ApothekenPortal gemäß § 6 der Nutzungsbedingungen.',
                'disabled' => true,
                'initialValue' =>
                    old('card_link_enabled') ?? (isset($model) ? $model->cardLinkOrder?->isTransmitted() : null),
                'config' => [],
            ])
        </div>
    </div>
@endif

{{-- Info --}}
<div class="rounded-lg bg-white shadow">
    <div class="px-4 py-2 text-sm sm:px-6 sm:py-3">
        <div>
            <div class="my-2">
                <span>*</span>
                @lang('messages.requiredText')
            </div>
            <div class="my-2">
                <span>**</span>
                Wir benötigen diese Angaben, um prüfen zu können, ob Ihre Apotheke/n bereits für eine Teilnahme
                am eRezept-Pilotprojekt Berlin/Brandenburg infrage kommt/kommen. Einige
                Warenwirtschaftssystemanbieter und Apothekenrechenzentren können bereits mit dem
                eRezept-Fachdienst der Apotheken interagieren. Weitere sollen möglichst bald folgen. Selbst wenn
                Ihr Warenwirtschaftssystem/Ihr Apothekenrechenzentrum noch nicht angebunden ist, sind diese
                Informationen wichtig und helfen uns, Ihre Dienstleister zu motivieren, die notwendigen
                Systemanpassungen für eine Teilnahme am eRezept-Pilotprojekt Berlin/Brandenburg vorzunehmen.
                Über die Teilnahme möglichst vieler Apotheken, aber auch über die Teilnahme zahlreicher
                Warenwirtschaftssystemanbieter/Apothekenrechenzentren soll ein größtmöglicher Erkenntnisgewinn
                innerhalb des eRezept-Pilotprojektes Berlin/Brandenburg generiert werden. Für die
                Prozessevaluation sowie die Fehleranalyse bei auftretenden Problemen ist es wichtig, die
                Dienstleister Ihrer Apotheken zu kennen.
            </div>
        </div>
    </div>
</div>
