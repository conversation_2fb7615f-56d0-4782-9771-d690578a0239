@php
    use App\Features\IhreApotheken;
    use App\Helper\IaHelper;
    use App\Domains\Association\Domain\Enums\AssociationFrameworkContractEnum;
    use App\Domains\Subscription\Application\StripeProducts\AddOns\TIMStripeProduct;
@endphp
{{-- navigation --}}
<nav class="flex flex-1 flex-col">
    <ul
            class="flex flex-1 flex-col"
            role="list"
    >
        <li>
            <ul
                    class="-mx-2 space-y-1"
                    role="list"
            >
                <li id="homepage-menu-dashboard">
                    <x-sidebar.nav-item
                            title="{{ __('navigation.dashboard') }}"
                            icon="chart-bar-square"
                            :active="['dashboard']"
                            route="dashboard"
                    />
                </li>

                @if (user()->can('administrateSubOwners', \App\Pharmacy::class))
                    <li>
                        <x-sidebar.nav-item
                                title="{{ __('navigation.company') }}"
                                icon="employees-small"
                                :children="[
                                [
                                    'title' => __('navigation.owners'),
                                    'active' => ['pharmacies.company.users*'],
                                    'route' => 'pharmacies.company.users',
                                ],
                            ]"
                        />
                    </li>
                @endif

                @if (user()->isPharmacyUser() && user()->hasPharmacies())
                    @if (user()->can('accessOverview', currentPharmacy()) ||
                            user()->can('accessCalendar', [currentPharmacy()]) ||
                            user()->can('accessTelepharmacy', [currentPharmacy()]) ||
                            user()->can('administrateUsers', currentPharmacy()) ||
                            user()->can('index', [\App\VaccinationImport::class, currentPharmacy()]) ||
                            user()->can('viewAny', [\App\VaccinationImport::class, currentPharmacy()]) ||
                            user()->can('index', [\App\PharmaceuticalService::class, currentPharmacy()]))
                        <li id="homepage-menu-pharmacy">
                            <x-sidebar.nav-item
                                    title="{{ __('navigation.pharmacy') }}"
                                    icon="home"
                                    :children="[
                                    [
                                        'title' => __('navigation.overview'),
                                        'active' => [
                                            'pharmacies.switch',
                                            'pharmacies.overview',
                                            'pharmacies.edit',
                                            'pharmacies.data.*',
                                        ],
                                        'route' => 'pharmacies.switch',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' => user()->can('accessOverview', currentPharmacy()),
                                    ],
                                    [
                                        'title' => __('navigation.employees'),
                                        'active' => ['pharmacies.users*'],
                                        'route' => 'pharmacies.users',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' => user()->can('administrateUsers', currentPharmacy()),
                                    ],
                                    [
                                        'title' => __('navigation.vaccinationImport'),
                                        'active' => ['pharmacies.importVaccination*'],
                                        'route' => 'pharmacies.importVaccination',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' =>
                                            user()->can('index', [\App\VaccinationImport::class, currentPharmacy()]) &&
                                            (isCovidVaccinationCertificateCenterActive() ||
                                                (isCovidVaccinationCertificateCenterActive() === false &&
                                                    currentPharmacy()->isIbmRegistered())),
                                    ], // TODO: Change when working on AP-1057, look changes from 2023-12-18 at 13:48
                                    [
                                        'title' => __('navigation.vaccinationCenter'),
                                        'active' => ['pharmacies.vaccinate*'],
                                        'route' => 'pharmacies.vaccinate.index',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' => user()->can('viewAny', [\App\Vaccination::class, currentPharmacy()]),
                                    ],
                                    [
                                        'title' => __('navigation.pharmaceuticalServices'),
                                        'active' => ['pharmacies.pharmaceutical-services.*'],
                                        'route' => 'pharmacies.pharmaceutical-services.index',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' => user()->can('index', [
                                            \App\PharmaceuticalService::class,
                                            currentPharmacy(),
                                        ]),
                                    ],
                                    [
                                        'title' => __('navigation.appointment-bookings'),
                                        'active' => ['pharmacies.access-calendar'],
                                        'route' => 'pharmacies.access-calendar',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' => user()->can('accessCalendar', [currentPharmacy()]),
                                        'target' => '_blank',
                                    ],
                                    [
                                        'title' => __('navigation.telepharmacy'),
                                        'active' => ['pharmacies.access-telepharmacy'],
                                        'route' => 'pharmacies.access-telepharmacy',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' => user()->can('accessTelepharmacy', [currentPharmacy()]),
                                        'dusk' => 'telepharmacy',
                                        'target' => '_blank',
                                    ],
                                    [
                                        'title' => __('navigation.doc-space'),
                                        'active' => ['sdr.doc-spaces'],
                                        'route' => 'sdr.doc-spaces',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' => user()->can('sdr.viewMenu', currentPharmacy()),
                                    ],
                                ]"
                            />
                        </li>
                    @endif

                        @if (auth()->user()->isOwner() && currentPharmacy() &&
                            \App\Domains\Subscription\Application\FeatureAccess\ShiftPlanFeatureAccess::check(currentPharmacy())->canUse())
                        <li>
                            <x-sidebar.nav-item
                                    title="{{ __('navigation.shiftplan') }}"
                                    icon="clock"
                                    indicator="{{ user()->shiftPlans->isEmpty() ? 'Neu' : '' }}"
                                    :active="['shiftplans.*']"
                                    route="shiftplans.index"
                                    dusk="shift-plan"
                            />
                        </li>
                    @endif

                    @if (user()->can('chat', [user(), currentPharmacy()]) ||
                            user()->can('accessChat', currentPharmacy()) ||
                            user()->can('activateChat', currentPharmacy()) ||
                            user()->can('sdr.viewMenu', currentPharmacy()) ||
                            user()->can('viewAdministration', [\App\Apomail::class, currentPharmacy()]) ||
                            user()->can('viewAny', [\App\KimAddress::class, currentPharmacy()]))
                        <li id="homepage-menu-communication">
                            <x-sidebar.nav-item
                                    title="{{ __('navigation.communication') }}"
                                    icon="chat-bubble-bottom-center-text"
                                    :children="[
                                    [
                                        'title' => __('navigation.apomail.administration'),
                                        'active' => ['apomails*'],
                                        'route' => 'apomails',
                                        'can' => user()->can('viewAdministration', [
                                            \App\Apomail::class,
                                            currentPharmacy(),
                                        ]),
                                    ],
                                    [
                                        'title' => __('navigation.apomail.link'),
                                        'route' => 'https://apomail.de/mail/',
                                        'dusk' => 'apomail',
                                    ],
                                    [
                                        'title' => __('navigation.chat'),
                                        'active' => ['pharmacies.chat*'],
                                        'route' => 'pharmacies.chat',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'can' =>
                                            currentPharmacy()->getAssociationFrameworkContract() == AssociationFrameworkContractEnum::PlusAssociationFrameworkContract->instance() ||
                                            currentPharmacy()->canUseProduct(TIMStripeProduct::class) || currentPharmacy()->uses_chat && (
                                            user()->can('chat', [user(), currentPharmacy()]) &&
                                            (user()->can('accessChat', currentPharmacy()) ||
                                                user()->can('activateChat', currentPharmacy()))),
                                    ],
                                    [
                                        'title' => __('navigation.kim'),
                                        'active' => ['kim*'],
                                        'route' => 'kims',
                                        'route-data' => ['pharmacy' => currentPharmacy()],
                                        'indicator' => \App\Helper\KimAddressHelper::isNew(user()) ? 'Neu' : '',
                                        'can' => user()->can('viewAny', [\App\KimAddress::class, currentPharmacy()]),
                                    ],
                                ]"
                            />
                        </li>
                    @endif

                    @can('viewAny', \App\CardLinkOrder::class)
                        @if (\Laravel\Pennant\Feature::for(\App\Enums\FeatureScope::App->value)->active(App\Features\CardLink::class))
                            <li id="homepage-menu-cardlink">
                                <x-sidebar.nav-item
                                        title="CardLink"
                                        icon="card"
                                        indicator="{{ \App\Helper\CardLinkHelper::isNew(currentPharmacy()) ? 'Neu' : '' }}"
                                        :active="['card-link.*']"
                                        route="card-link"
                                        :routeData="['pharmacy' => currentPharmacy()]"
                                />
                            </li>
                        @endif
                    @endcan

                    @if (user()->can('seeIaNavigationItem', user()))
                        @if (
                            (\Laravel\Pennant\Feature::active(App\Features\IhreApothekenPreflight::class) && !user()->isPreflightUser()) ||
                                \Laravel\Pennant\Feature::inactive(\App\Features\IhreApotheken::class))
                            <li id="hompage-menu-order-center">
                                <x-sidebar.nav-item
                                        title="Bestellzentrale"
                                        icon="shopping-cart"
                                        indicator="{{ IaHelper::isNew(user()) ? 'Neu' : '' }}"
                                        :active="['ia.*']"
                                        route="ia.ihre-apotheken.index"
                                        dusk="order-center-single"
                                />
                            </li>
                        @endif

                        @if (
                            (\Laravel\Pennant\Feature::active(\App\Features\IhreApotheken::class) &&
                                \Laravel\Pennant\Feature::inactive(App\Features\IhreApothekenPreflight::class)) ||
                                (\Laravel\Pennant\Feature::active(App\Features\IhreApothekenPreflight::class) && user()->isPreflightUser()))
                            <li>
                                <x-sidebar.nav-item
                                        title="{{ __('navigation.order-center.title') }}"
                                        icon="shopping-cart"
                                        indicator="{{ IaHelper::isNew(user()) ? 'Neu' : '' }}"
                                        dusk="order-center-disclosure"
                                        :children="[
                                        [
                                            'title' => 'Bestellungen',
                                            'active' => ['ia.orders*', 'ia.activate'],
                                            'route' => IaHelper::getDefaultRoute('ia.bestellungen') ?? 'ia.activate',
                                            'can' =>
                                                !!IaHelper::getDefaultRoute('ia.bestellungen') ||
                                                auth()->user()->isOwnerOrSubOwnerOfPharmacy(),
                                            'route-data' => ['pharmacy' => currentPharmacy()],
                                        ],
                                        [
                                            'title' => 'Einstellungen',
                                            'active' => ['ia.einstellungen*'],
                                            'route' =>
                                                IaHelper::getDefaultRoute([
                                                    'ia.einstellungen.preis-sortiment*',
                                                    'ia.einstellungen.ihre-website*',
                                                    'ia.einstellungen.konfiguration*',
                                                ]) ?? 'ia.activate',
                                            'can' =>
                                                !!IaHelper::getDefaultRoute([
                                                    'ia.einstellungen.preis-sortiment*',
                                                    'ia.einstellungen.ihre-website*',
                                                    'ia.einstellungen.konfiguration*',
                                                ]) || auth()->user()->isOwnerOrSubOwnerOfPharmacy(),
                                            'route-data' => ['pharmacy' => currentPharmacy()],
                                        ],
                                    ]"
                                />
                            </li>
                        @endif
                    @endif
                @endif

                @if (user()->isAssociationUser() &&
                        user()->associations->count() > 0 &&
                        user()->can('view', user()->associations->first()))
                    <li id="homepage-menu-associations">
                        <x-sidebar.nav-item
                                title="{{ __('navigation.association') }}"
                                icon="clipboard-document-list-inner"
                                :children="[
                                [
                                    'title' => __('navigation.overview'),
                                    'active' => ['associations.overview'],
                                    'route' => 'associations.overview',
                                ],
                                [
                                    'title' => __('navigation.employees'),
                                    'active' => ['associations.users.*'],
                                    'route' => 'associations.users.index',
                                    'can' => user()->can('administrateUsers', user()->associations->first()),
                                ],
                                [
                                    'title' => __('navigation.memberAdministration'),
                                    'active' => ['associations.members.*'],
                                    'route' => 'associations.members.index',
                                    'can' => user()->can('administrateMembers', user()->associations->first()),
                                ],
                                [
                                    'title' => __('navigation.vaccinationCenter'),
                                    'active' => ['associations.vaccination', 'associations.vaccination.*'],
                                    'route' => 'associations.vaccination.index',
                                    'can' => user()->can('vaccinateView', user()->associations->first()),
                                ],
                                [
                                    'title' => __('navigation.associationNews'),
                                    'active' => ['associations.news.*'],
                                    'route' => 'associations.news.index',
                                    'can' => user()->can('manageNews', user()->associations->first()),
                                ],
                                [
                                    'title' => __('navigation.retax'),
                                    'active' => [],
                                    'route' => (new \App\Settings\RetaxSettings())->base_url,
                                    'can' => user()->can('indexRetax', user()->associations->first()),
                                ],
                            ]"
                        />
                    </li>
                @endif

                @can('viewApoGuideShopLink', [currentPharmacy()])
                    <li id="homepage-menu-apoguide-shop">
                        <x-sidebar.nav-item
                            title="{{ __('navigation.shop') }}"
                            icon="cart"
                            :active="['apoguide-shop']"
                            route="apoguide-shop"
                        />
                    </li>
                @endcan

                @if(currentPharmacy())
                    @can('indexRetax', [currentPharmacy()])
                        <li id="homepage-menu-retax">
                            <x-sidebar.nav-item
                                title="{{ __('navigation.retax') }}"
                                icon="archive-box"
                                :active="['retax']"
                                route="retax.index"
                            />
                        </li>
                    @endcan
                @endif

                @can('index', \App\Pharmacy::class)
                    <li id="homepage-menu-pharmacy-list">
                        <x-sidebar.nav-item
                                title="{{ __('navigation.pharmacies') }}"
                                icon="house"
                                :active="['pharmacies', 'pharmacies.create']"
                                route="pharmacies"
                        />
                    </li>
                @endcan

                @if (user()->isPharmacyUser() && user()->can('viewAny', \App\AssociationNews::class))
                    <li id="homepage-menu-cardlink">
                        <x-sidebar.nav-item
                                title="{{ __('navigation.myAssociation') }}"
                                icon="building-office"
                                :indicator="App\Support\NotificationCenter\NewAssociationNewsNotification::count() ? 'Neu' : ''"
                                :active="['my-association/news/*']"
                                route="my-association.index"
                        />
                    </li>
                @endif

            </ul>
        </li>
        <li class="mt-auto">
            <ul
                    class="-mx-2 mt-1 space-y-1"
                    id="sub-menu-1"
            >
                <li id="homepage-menu-blog">
                    <x-sidebar.nav-item
                            title="{{ __('navigation.blog') }}"
                            icon="newspaper"
                            :active="['news*', 'categories*', 'authors*', 'tags*']"
                            route="news.index"
                            :route-data="['blog']"
                    />
                </li>
                <li id="homepage-menu-help">
                    <x-sidebar.nav-item
                            title="{{ __('navigation.help') }}"
                            icon="question-mark-circle"
                            :active="['support']"
                            route="support"
                    />
                </li>
            </ul>
        </li>
    </ul>
</nav>
