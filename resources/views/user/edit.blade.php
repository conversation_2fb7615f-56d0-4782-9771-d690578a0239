@extends('layouts.app')

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                @lang('navigation.account')
            </x-slot>
        </x:header>

        @if (session('newChangeEmailSend'))
            <x-alert class="mb-8" description="Eine E-Mail mit einem neuen Bestätigungs-Link wurde verschickt." />
        @endif

        @if ($currentlyChangingEmail)
            <div class="bg-white shadow rounded-md mb-8">
                <div class="py-8 px-4 sm:px-10">
                    <div class="sm:flex sm:items-start sm:justify-between">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                E-Mail Adressen Änderung
                            </h3>
                            <div class="mt-2 max-w-xl text-sm leading-5 text-gray-500">
                                <p>
                                    Sie haben die Änderung Ihrer E-Mail-Adresse für Benachrichtigungen angefordert.
                                    Damit die neue E-Mail-Adresse mit Ihrem Account verbunden werden kann, müssen Sie
                                    den Link klicken, den wir Ihnen zu Ihrer neuen E-Mail-Adresse
                                    ({{ $currentlyChangingEmail }}) zukommen lassen haben.
                                </p>
                            </div>
                        </div>
                        <div class="mt-5 sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:flex sm:items-center">
                            <form method="POST" action="{{ route('user.email.resend') }}">
                                @csrf
                                <span class="inline-flex rounded-md shadow-sm">
                                    <button type="submit"
                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-indigo active:bg-red-700 transition ease-in-out duration-150">
                                        E-Mail erneut senden
                                    </button>
                                </span>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <x-ui.tabs>
            <x-ui.tab>Stammdaten</x-ui.tab>
            <x-ui.tab>Benachrichtigungen</x-ui.tab>
        </x-ui.tabs>
        <div class="flex flex-wrap -m-4">
            <div class="w-full p-4">

                <x:alert type="info" class="mb-8"
                         description="Auf dieser Seite können Sie die Daten Ihres Apothekenportal-Accounts
                                            anpassen. Die Gedisa-Account-Daten, z.B. E-Mail-Adresse für Login und Passwort,
                                            können Sie im Gedisa-Account ändern."
                >

                    <x-slot name="trailingLink">
                        <a class="flex items-center" target="_blank"
                           href="{{ config('oidc-auth.provider.accountConsoleUrl') }}">
                                            <span class="whitespace-nowrap">
                                                Zum Gedisa-Account
                                            </span>
                            <svg class="ml-2 h-4 w-4">
                                <use href="/icons.svg#arrow-long-right-alt" />
                            </svg>
                        </a>
                    </x-slot>
                </x:alert>

                @if ($errors->any())
                    <div class="w-full mb-8">
                        <x:alert-validation-error />
                    </div>
                @endif

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <form id="edit-form" method="POST" action="{{ route('users.update') }}">
                            @csrf
                            @method('PUT')

                            <div class="sm:flex sm:-mx-3">
                                {{-- Salutation --}}
                                <div class="sm:w-1/2 sm:px-3">

                                    <x-input.select
                                        :label="__('validation.attributes.salutation')"
                                        id="salutation"
                                        name="salutation"
                                        :error="$errors->first('salutation')"
                                    >
                                        @foreach(\App\Enums\SalutationEnum::getForDropdown() as $value => $title)
                                            <option value="{{ $value }}"
                                                    @if((old('salutation') ?? $user->salutation) == $value) selected @endif>@lang('validation.attributes.' . $title)</option>
                                        @endforeach
                                    </x-input.select>
                                </div>

                                {{-- Title --}}
                                <div class="sm:w-1/2 sm:px-3">
                                    <x-input.text
                                        :label="__('validation.attributes.title')"
                                        id="title"
                                        type="text"
                                        name="title"
                                        value="{{ old('title') ?? $user->title }}"
                                        :error="$errors->first('title')"
                                    />
                                </div>
                            </div>

                            {{-- First Name, Last Name --}}
                            <div class="sm:flex sm:-mx-3">
                                {{-- First Name --}}
                                <div class="mt-6 sm:w-1/2 sm:px-3">
                                    <x-input.text
                                        :label="__('validation.attributes.first_name')"
                                        id="first_name"
                                        type="text"
                                        name="first_name"
                                        value="{{ old('first_name') ?? $user->first_name }}"
                                        :error="$errors->first('first_name')"
                                    />
                                </div>

                                {{-- Last Name --}}
                                <div class="mt-6 sm:w-1/2 sm:px-3">
                                    <x-input.text
                                        :label="__('validation.attributes.last_name')"
                                        id="last_name"
                                        type="text"
                                        name="last_name"
                                        value="{{ old('last_name') ?? $user->last_name }}"
                                        :error="$errors->first('last_name')"
                                    />
                                </div>
                            </div>

                            {{-- Phone, Association --}}
                            <div class="sm:flex sm:-mx-3">
                                {{-- Phone --}}
                                <div class="mt-6 sm:w-1/2 sm:px-3">
                                    <x-input.text
                                        :label="__('validation.attributes.your_personal_phone')"
                                        id="phone"
                                        type="text"
                                        name="phone"
                                        value="{{ old('phone') ?? $user->phone }}"
                                        :error="$errors->first('phone')"
                                    />
                                </div>
                            </div>

                            {{-- E-Mail --}}
                            <div class="sm:flex sm:flex-wrap sm:-mx-3">
                                <div class="mt-6 sm:w-1/2 sm:px-3">
                                    <x-input.text
                                        :label="__('validation.attributes.your_personal_email')"
                                        id="email"
                                        type="email"
                                        name="email"
                                        value="{{ old('email') ?? $user->email }}"
                                        :error="$errors->first('email')"
                                    />
                                </div>

                                <div class="mt-6 sm:w-1/2 sm:px-3">
                                    <x-input.text
                                        :label="__('validation.attributes.email_confirmation')"
                                        id="email_confirmation"
                                        type="email"
                                        name="email_confirmation"
                                        value="{{ old('email_confirmation') ?? $user->email }}"
                                        :error="$errors->first('email_confirmation')"
                                    />
                                </div>
                            </div>

                            @if ($user->owner()?->pharmacyProfile?->association_id)
                                {{-- Notifications --}}
                                <div class="sm:flex sm:flex-wrap sm:-mx-3">
                                    <div class="mt-6 sm:w-1/2 sm:px-3">
                                        <x-input.checkbox
                                            id="association_news_active"
                                            name="association_news_active"
                                            value="1"
                                            :checked="old('association_news_active', $user->association_news_active) ? 'checked' : ''"
                                            :error="$errors->first('association_news_active')"
                                        >@lang('validation.attributes.association_news_active')</x-input.checkbox>
                                    </div>
                                </div>
                            @endif
                        </form>
                    </div>
                </div>
                {{-- Submit --}}
                <div class="mt-6">
                    <button type="submit" form="edit-form"
                            class="flex ml-auto py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red active:bg-red-700 transition duration-150 ease-in-out">
                        @lang('messages.submit')
                    </button>
                </div>
            </div>
        </div>
    </x:content>
@endsection
