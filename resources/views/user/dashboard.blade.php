@php use App\Enums\Newsletter\MailcoachList; @endphp
@extends('layouts.app', ['novue' => true])

@section('content')
    <x:content>
        <x:header>
            <x-slot name="main">
                @lang('navigation.dashboard')
            </x-slot>
        </x:header>

        <div class="-m-4 flex flex-wrap">
            {{-- Friendly User Test --}}
            <x:modal
                name="betatest-registered-modal"
                modal-open="{{ session()->has('betaTestRegistrationSuccessful') }}"
            >
                <x:slot name="heading">
                    Registrierung erfolgreich
                </x:slot>
                <x:slot name="body">
                    Sie haben sich erfolgreich als Friendly User registriert. Wir freuen uns auf die Zusammenarbeit.
                </x:slot>
                <x:slot name="footer">
                    <div class="flex justify-end space-x-2">
                        <x:button
                            x-on:click="$dispatch('change-modal-state', { name: 'betatest-registered-modal', state: 'close' })"
                            appearance="primary"
                        >
                            Fertig
                        </x:button>
                    </div>
                </x:slot>
            </x:modal>
            <x:modal
                name="betatest-canceled-modal"
                modal-open="{{ session()->has('betaTestSignOffSuccessful') }}"
            >
                <x:slot name="heading">
                    Abmeldung erfolgreich
                </x:slot>
                <x:slot name="body">
                    Sie haben sich erfolgreich als Friendly User abgemeldet. Vielen Dank für Ihre bisherige
                    Unterstützung.
                    Sie können sich gerne jederzeit wieder für den Friendly User Test anmelden.
                </x:slot>
                <x:slot name="footer">
                    <div class="flex justify-end space-x-2">
                        <x:button
                            x-on:click="$dispatch('change-modal-state', { name: 'betatest-canceled-modal', state: 'close' })"
                            appearance="primary"
                        >
                            Fertig
                        </x:button>
                    </div>
                </x:slot>
            </x:modal>

            {{-- TODO: Remove when working on AP-1057 --}}
            @if (isCovidVaccinationCertificateCenterActive())
                <div class="mx-4 w-full">
                    <x-alert
                        class="mb-6"
                        type="warning"
                        title="WICHTIGE MELDUNG!"
                    >
                        <x-slot name="description">
                            <p>Zertifikatsdienst wird zum 31.12.2023 eingestellt.</p>

                            <x-button
                                class="mt-4"
                                href="https://www.mein-apothekenportal.de/blog/nach-fast-110-mio-covid-19-zertifikaten-ist-schluss"
                                size="xs"
                                target="_blank"
                            >Mehr erfahren
                            </x-button>
                        </x-slot>
                    </x-alert>
                </div>
            @endif

            @if (currentPharmacy() &&
                    \App\Domains\Subscription\Application\FeatureAccess\DataAccessPeriodFeatureAccess::check(currentPharmacy())->canUse())
                <div class="w-full p-4">
                    <livewire:subscription.reactivate-subscription-modal />
                </div>
            @endif

            @if (
                !user()->getGeneralSetting(\App\Enums\Settings\UserSettingTypes::CLOSE_NEWSLETTER_BANNER)?->value &&
                    !user()->hasMailcoachSubscriptions(MailcoachList::Advertising))
                <livewire:newsletter.banner />
            @endif

            @if (\App\Helper\KimAddressHelper::needsModalConfirmation(currentPharmacy()))
                <div class="w-full p-4">
                    <livewire:kim-address.appointment-booking-modal />
                </div>
            @endif

            @if (currentPharmacy() &&
                    \App\Domains\Subscription\Application\FeatureAccess\KimFeatureAccess::check(currentPharmacy())->canUse() &&
                    currentPharmacy()->kimAddresses()->count() === 0)
                <div class="mx-4 w-full">
                    <x-alert
                        class="mb-6"
                        type="warning"
                        title="Jetzt verfügbar: KIM – Kommunikation im Medizinwesen"
                    >
                        <x-slot name="description">
                            <p>Sichern Sie sich hier in wenigen Minuten Ihre erste KIM-Adresse und nutzen Sie diese als
                                Verbandsmitglied unserer Gesellschafter oder Abonnent der Vollvariante bis zum
                                31.12.2024 kostenfrei. Profitieren Sie jetzt von dem sektorenübergreifenden
                                Kommunikationsdienst über die Telematikinfrastruktur.</p>
                            @can('viewAny', [\App\KimAddress::class, currentPharmacy()])
                                <x-button
                                    class="mt-4"
                                    href="{{ route('kims', currentPharmacy()) }}"
                                    size="xs"
                                >Zu Ihrer
                                    KIM-Adresse
                                </x-button>
                            @else
                                <p class="mt-4">Bitte halten Sie dafür Rücksprache mit dem Inhaber Ihrer Apotheke.</p>
                            @endcan
                        </x-slot>
                    </x-alert>
                </div>
            @else
                <x-advertisement.apoportal-commercial-banner
                    wrapper-class="mx-4"></x-advertisement.apoportal-commercial-banner>
            @endif

            <livewire:ti-gateway-banner />

            @if ($amkNews->count() > 0)
                <div class="w-full p-4">
                    <div class="overflow-hidden rounded-lg border-4 border-red-500 bg-white shadow">
                        <div class="flex items-center px-4 pt-5 sm:px-6">
                            <div class="flex-1 pr-4">
                                <h3 class="text-md font-semibold leading-6 text-red-500">
                                    Dringende Arzneimittelmeldung
                                </h3>
                            </div>
                        </div>
                        <div class="px-4 pb-5 pt-2 sm:px-6">
                            <p class="text-sm font-medium leading-5 text-gray-900">
                                <a
                                    class="hover:underline"
                                    href="{{ $amkNews->first()->link }}"
                                    target="_blank"
                                >
                                    {{ $amkNews->first()->title }}
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            @endif

            {{-- Left column --}}
            <div
                class="w-full p-4 md:w-1/2"
                x-data="{ open: false, minimize: false, maximize: false, reset: false }"
                x-bind:class="{ 'md:w-1/2': !maximize }"
            >
                {{-- Blog --}}
                <div class="overflow-hidden rounded-lg bg-white shadow">
                    {{-- Header --}}
                    <div class="flex items-center border-t-4 border-red-500 px-4 py-5 sm:px-6">
                        <div class="flex-1 pr-4">
                            <h3 class="text-md font-semibold leading-6 text-red-500">
                                GEDISA Blog: Digitalisierung und Zukunft
                            </h3>
                        </div>

                        {{-- Actions --}}
                        {{--                        <div class="self-start text-right"> --}}
                        {{--                            <div x-on:keydown.escape="open = false" x-on:click.outside="open = false" class="relative inline-block text-left"> --}}
                        {{--                                <div> --}}
                        {{--                                    <button x-on:click="open = !open" class="flex items-center p-1 text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100" aria-label="Options" id="options-menu" aria-haspopup="true" aria-expanded="true"> --}}
                        {{--                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> --}}
                        {{--                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" /> --}}
                        {{--                                        </svg> --}}
                        {{--                                    </button> --}}
                        {{--                                </div> --}}

                        {{--                                <div x-show="open" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="opacity-0" class="origin-top-right absolute right-0 mt-2 rounded-md shadow-lg"> --}}
                        {{--                                    <div class="rounded-md bg-white shadow-xs"> --}}
                        {{--                                        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu"> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; minimize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Minimieren</button> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; maximize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Maximieren</button> --}}
                        {{--                                            <button x-cloak x-show="reset" x-on:click="open = !open; minimize = false; maximize = false; reset = false;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Wiederherstellen</button> --}}
                        {{--                                        </div> --}}
                        {{--                                    </div> --}}
                        {{--                                </div> --}}
                        {{--                            </div> --}}
                        {{--                        </div> --}}
                    </div>

                    {{-- Body --}}
                    <div
                        class="px-4 pb-5 pt-4 sm:px-6 sm:pb-6"
                        x-show="!minimize"
                    >
                        <div class="space-y-4">
                            @foreach ($blog as $blogItem)
                                <div class="flex">
                                    {{-- Image --}}
                                    @if ($blogItem->hasMedia('header'))
                                        <div class="w-28 overflow-hidden pr-4">
                                            <a href="{{ route('news.show', ['type' => 'blog', 'news' => $blogItem]) }}">
                                                <img
                                                    class="block w-full rounded transition duration-300 ease-in-out group-hover:scale-105 sm:transform"
                                                    src="{{ $blogItem->getFirstMedia('header')->getUrl('masonry') }}"
                                                    alt=""
                                                >
                                            </a>
                                        </div>
                                    @endif

                                    <div class="flex-1">
                                        {{-- Headline --}}
                                        <div>
                                            <a
                                                class="block hover:underline"
                                                href="{{ route('news.show', ['type' => 'blog', 'news' => $blogItem]) }}"
                                            >
                                                <h4 class="text-md font-medium leading-5 text-gray-900">
                                                    {{ $blogItem->title }}
                                                </h4>
                                            </a>
                                        </div>

                                        <div class="mt-2 flex items-center">
                                            <div>
                                                <p class="text-sm font-medium leading-5 text-gray-900">
                                                    <a
                                                        class="hover:underline"
                                                        href="{{ route('authors.show', ['type' => 'blog', 'author' => $blogItem->author]) }}"
                                                    >
                                                        {{ $blogItem->author->full_name }}
                                                    </a>
                                                </p>
                                                <div class="flex text-sm leading-5 text-gray-500">
                                                    <time datetime="{{ $blogItem->release_date->format('Y-m-d') }}">
                                                        {{ $blogItem->release_date->format('d.') . ' ' . $blogItem->release_date->monthName . ' ' . $blogItem->release_date->format('Y') }}
                                                    </time>
                                                    <span class="mx-1">
                                                        ·
                                                    </span>
                                                    <span>
                                                        {{ read_time($blogItem->title . $blogItem->excerpt . $blogItem->text) }}
                                                        Min. Lesezeit
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- Footer --}}
                    <div class="bg-gray-50 px-4 py-4 sm:px-6"></div>
                </div>

                {{-- Pharmazeutische Zeitung online --}}
                <div class="mt-8 overflow-hidden rounded-lg bg-white shadow">
                    {{-- Header --}}
                    <div class="flex items-center border-t-4 border-red-500 px-4 py-5 sm:px-6">
                        <div class="flex-1 pr-4">
                            <h3 class="text-md font-semibold leading-6 text-red-500">
                                Pharmazeutische Zeitung online
                            </h3>
                            <div class="text-xs text-gray-500">
                                (Externe Links)
                            </div>
                        </div>

                        {{-- Actions --}}
                        {{--                        <div class="self-start text-right"> --}}
                        {{--                            <div x-on:keydown.escape="open = false" x-on:click.outside="open = false" class="relative inline-block text-left"> --}}
                        {{--                                <div> --}}
                        {{--                                    <button x-on:click="open = !open" class="flex items-center p-1 text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100" aria-label="Options" id="options-menu" aria-haspopup="true" aria-expanded="true"> --}}
                        {{--                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> --}}
                        {{--                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" /> --}}
                        {{--                                        </svg> --}}
                        {{--                                    </button> --}}
                        {{--                                </div> --}}

                        {{--                                <div x-show="open" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="opacity-0" class="origin-top-right absolute right-0 mt-2 rounded-md shadow-lg"> --}}
                        {{--                                    <div class="rounded-md bg-white shadow-xs"> --}}
                        {{--                                        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu"> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; minimize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Minimieren</button> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; maximize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Maximieren</button> --}}
                        {{--                                            <button x-cloak x-show="reset" x-on:click="open = !open; minimize = false; maximize = false; reset = false;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Wiederherstellen</button> --}}
                        {{--                                        </div> --}}
                        {{--                                    </div> --}}
                        {{--                                </div> --}}
                        {{--                            </div> --}}
                        {{--                        </div> --}}
                    </div>

                    {{-- Body --}}
                    <div
                        class="px-4 pb-5 pt-4 sm:px-6 sm:pb-6"
                        x-show="!minimize"
                    >
                        <div class="space-y-4">
                            @foreach ($pz as $article)
                                <div class="flex items-center">
                                    <div>
                                        <div class="flex text-sm leading-5 text-gray-500">
                                            <time
                                                datetime="{{ optional($article->modification_date)->format('d.m.Y H:i') }}"
                                            >
                                                {{ optional($article->modification_date)->format('d.m.Y H:i') }}
                                            </time>
                                        </div>
                                        <p class="text-sm font-medium leading-5 text-gray-900">
                                            <a
                                                class="hover:underline"
                                                href="{{ $article->link }}"
                                                target="_blank"
                                            >
                                                {{ $article->title }}
                                            </a>
                                        </p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- Footer --}}
                    <div class="bg-gray-50 px-4 py-4 sm:px-6"></div>
                </div>
            </div>

            {{-- Right column --}}
            <div
                class="w-full p-4 md:w-1/2"
                x-data="{ open: false, minimize: false, maximize: false, reset: false }"
                x-bind:class="{ 'md:w-1/2': !maximize }"
            >

                {{-- Friendly User Test --}}
                @if (!user()->is_beta_tester && !user()->isAssociationUser())
                    <div class="mb-8 overflow-hidden rounded-lg bg-white shadow">
                        {{-- Header --}}
                        <div class="flex items-center border-t-4 border-red-500 px-4 py-5 sm:px-6">
                            <div class="flex-1 pr-4">
                                <h3 class="text-md font-semibold leading-6 text-red-500">
                                    WIR BRAUCHEN SIE UND IHRE APOTHEKE - FRIENDLY USER GESUCHT
                                </h3>
                            </div>
                        </div>

                        {{-- Body --}}
                        <div
                            class="px-4 pb-5 pt-4 sm:px-6 sm:pb-6"
                            x-show="!minimize"
                        >
                            <div class="space-y-4">

                                <div class="text-sm leading-5 text-gray-500">
                                    <p>
                                        Unser Anspruch ist es, sinnvolle und funktionale Anwendungen für die
                                        Apothekerinnen
                                        und Apotheker zu entwickeln, die ihnen die Arbeit in der Offizin erleichtern.
                                        Und das am besten gemeinsam mit Ihnen. Wir suchen digitalaffine Apothekerinnen
                                        und
                                        Apotheker, die uns im Entwicklungsprozess von Anfang an beraten und begleiten.
                                        So
                                        wollen wir erreichen, dass am Ende der Entwicklung userfreundliche und
                                        alltagstaugliche Produkte stehen, die den Vergleich am Markt nicht scheuen
                                        müssen.
                                    </p>
                                    <br>
                                    <p>
                                        Wir freuen uns, wenn auch Sie und Ihre Apotheke uns demnächst unterstützen
                                        würden. Wenn Sie Interesse daran haben, klicken Sie bitte
                                        <a
                                            class="text-red-500 underline"
                                            href="{{ route('beta-test.registration') }}"
                                        >
                                            HIER
                                        </a>
                                        .
                                    </p>
                                </div>

                            </div>
                        </div>

                        {{-- Footer --}}
                        <div class="bg-gray-50 px-4 py-4 sm:px-6"></div>
                    </div>
                @endif

                {{-- Abda Newsroom --}}
                <div class="overflow-hidden rounded-lg bg-white shadow">
                    {{-- Header --}}
                    <div class="flex items-center border-t-4 border-red-500 px-4 py-5 sm:px-6">
                        <div class="flex-1 pr-4">
                            <h3 class="text-md font-semibold leading-6 text-red-500">
                                ABDA Newsroom
                            </h3>
                            <div class="text-xs text-gray-500">
                                (Externe Links)
                            </div>
                        </div>

                        {{-- Actions --}}
                        {{--                        <div class="self-start text-right"> --}}
                        {{--                            <div x-on:keydown.escape="open = false" x-on:click.outside="open = false" class="relative inline-block text-left"> --}}
                        {{--                                <div> --}}
                        {{--                                    <button x-on:click="open = !open" class="flex items-center p-1 text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100" aria-label="Options" id="options-menu" aria-haspopup="true" aria-expanded="true"> --}}
                        {{--                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> --}}
                        {{--                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" /> --}}
                        {{--                                        </svg> --}}
                        {{--                                    </button> --}}
                        {{--                                </div> --}}

                        {{--                                <div x-show="open" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="opacity-0" class="origin-top-right absolute right-0 mt-2 rounded-md shadow-lg"> --}}
                        {{--                                    <div class="rounded-md bg-white shadow-xs"> --}}
                        {{--                                        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu"> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; minimize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Minimieren</button> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; maximize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Maximieren</button> --}}
                        {{--                                            <button x-cloak x-show="reset" x-on:click="open = !open; minimize = false; maximize = false; reset = false;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Wiederherstellen</button> --}}
                        {{--                                        </div> --}}
                        {{--                                    </div> --}}
                        {{--                                </div> --}}
                        {{--                            </div> --}}
                        {{--                        </div> --}}
                    </div>

                    {{-- Body --}}
                    <div
                        class="px-4 pb-5 pt-4 sm:px-6 sm:pb-6"
                        x-show="!minimize"
                    >
                        <div class="space-y-4">
                            @foreach ($newsroom as $room)
                                <div class="flex">
                                    {{-- Image --}}
                                    @if (is_array($room->extra) && array_key_exists('images', $room->extra) && array_key_exists(0, $room->extra['images']))
                                        <div class="w-28 overflow-hidden pr-4">
                                            <a
                                                href="{{ $room->link }}"
                                                target="_blank"
                                            >
                                                <img
                                                    class="block w-full rounded transition duration-300 ease-in-out group-hover:scale-105 sm:transform"
                                                    src="{{ $room->extra['images'][0] }}"
                                                    alt=""
                                                >
                                            </a>
                                        </div>
                                    @endif

                                    <div class="flex-1">
                                        {{-- Headline --}}

                                        <div class="mb-2 flex items-center">
                                            <div>
                                                <div class="flex text-sm leading-5 text-gray-500">
                                                    <time
                                                        datetime="{{ optional($room->modification_date)->format('d.m.Y H:i') }}"
                                                    >
                                                        {{ optional($room->modification_date)->format('d.m.Y H:i') }}
                                                    </time>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <a
                                                class="block hover:underline"
                                                href="{{ $room->link }}"
                                                target="_blank"
                                            >
                                                <h4 class="text-md font-medium leading-5 text-gray-900">
                                                    {{ $room->title }}
                                                </h4>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- Footer --}}
                    <div class="bg-gray-50 px-4 py-4 sm:px-6"></div>
                </div>

                {{-- AMK-Nachrichten --}}
                <div class="mt-8 overflow-hidden rounded-lg bg-white shadow">
                    {{-- Header --}}
                    <div class="flex items-center border-t-4 border-red-500 px-4 py-5 sm:px-6">
                        <div class="flex-1 pr-4">
                            <h3 class="text-md font-semibold leading-6 text-red-500">
                                AMK-Nachrichten: Rückrufe, Chargenrückrufe und Chargenüberprüfungen
                            </h3>
                            <div class="text-xs text-gray-500">
                                (Externe Links)
                            </div>
                        </div>

                        {{-- Actions --}}
                        {{--                        <div class="self-start text-right"> --}}
                        {{--                            <div x-on:keydown.escape="open = false" x-on:click.outside="open = false" class="relative inline-block text-left"> --}}
                        {{--                                <div> --}}
                        {{--                                    <button x-on:click="open = !open" class="flex items-center p-1 text-gray-900 hover:bg-gray-50 focus:outline-none focus:bg-gray-100" aria-label="Options" id="options-menu" aria-haspopup="true" aria-expanded="true"> --}}
                        {{--                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> --}}
                        {{--                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" /> --}}
                        {{--                                        </svg> --}}
                        {{--                                    </button> --}}
                        {{--                                </div> --}}

                        {{--                                <div x-show="open" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="opacity-0" class="origin-top-right absolute right-0 mt-2 rounded-md shadow-lg"> --}}
                        {{--                                    <div class="rounded-md bg-white shadow-xs"> --}}
                        {{--                                        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu"> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; minimize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Minimieren</button> --}}
                        {{--                                            <button x-cloak x-show="!reset" x-on:click="open = !open; maximize = true; reset = true;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Maximieren</button> --}}
                        {{--                                            <button x-cloak x-show="reset" x-on:click="open = !open; minimize = false; maximize = false; reset = false;" class="block w-full px-4 py-2 text-sm text-left leading-5 text-gray-700 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:bg-gray-100 focus:text-gray-900" role="menuitem">Wiederherstellen</button> --}}
                        {{--                                        </div> --}}
                        {{--                                    </div> --}}
                        {{--                                </div> --}}
                        {{--                            </div> --}}
                        {{--                        </div> --}}
                    </div>

                    {{-- Body --}}
                    <div
                        class="px-4 pb-5 pt-4 sm:px-6 sm:pb-6"
                        x-show="!minimize"
                    >
                        <div class="space-y-4">
                            @foreach ($amkNews as $rssFeedItem)
                                <div class="flex items-center">
                                    <div>
                                        <div class="flex text-sm leading-5 text-gray-500">
                                            <time
                                                datetime="{{ optional($rssFeedItem->modification_date)->format('d.m.Y H:i') }}"
                                            >
                                                {{ optional($rssFeedItem->modification_date)->format('d.m.Y H:i') }}
                                            </time>
                                        </div>
                                        <p class="text-sm font-medium leading-5 text-gray-900">
                                            <a
                                                class="hover:underline"
                                                href="{{ $rssFeedItem->link }}"
                                                target="_blank"
                                            >
                                                {{ $rssFeedItem->title }}
                                            </a>
                                        </p>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    {{-- Footer --}}
                    <div class="bg-gray-50 px-4 py-4 sm:px-6"></div>
                </div>
            </div>
        </div>
    </x:content>
@endsection
