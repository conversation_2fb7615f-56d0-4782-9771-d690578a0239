<x-mail::message>
# {{ $customerName }},

vielen Dank für Ihre verbindliche Bestellung von CardLink. Wir freuen uns sehr, dass Sie sich für die standeseigene Lösung der GEDISA entschieden haben.

**Wir haben folgende Details Ihrer Bestellung aufgenommen:**

Datum der Bestellung: {{ $cardLinkOrder->ordered_at->translatedFormat('j. F Y') }}

Apotheke:
- {{ $cardLinkOrder->pharmacy->name }}: {{ $package->label() }}

Der Vertrag startet mit Aktivierung von CardLink für Ihre jeweilige Betriebsstätte.

Die Mindestlaufzeit beträgt 12 Monate. Danach können Sie Ihren Vertrag monatlich kündigen.

**Wie geht es weiter?**\
@if(now()->isBefore(app(\App\Settings\CardLinkSettings::class)->transmissionEnabledAt))
Wir informieren Sie rechtzeitig und umfassend per E-Mail zu den nächsten Schritten bzgl. <PERSON><PERSON><PERSON>, die sukzessive erfolgen wird und entsprechend einige Tage in Anspruch nehmen kann. Wir geben alles, um Sie schnellstmöglich freizuschalten.

Sie erhalten bei Freischaltung eine separate Mail mit entsprechender GEDISA-ID, die Sie für die Verbindung mit möglichen App-Anbietern benötigen.

Sollten Sie zwischenzeitlich Fragen zur neuen CardLink-Funktion haben, wenden Sie sich gerne an unseren Support unter <a href="mailto:<EMAIL>" class="underline"><EMAIL></a>.
@else
Nachfolgend finden Sie Ihre individuelle GEDISA-ID.

<x-mail::panel>
GEDISA-ID: {{ $gedisaId }}
</x-mail::panel>

Ihre Rechnung erhalten Sie in Kürze per E-Mail von unserem Zahlungsanbieter Stripe.

Sollten Sie CardLink für die ApoGuide App aktiviert haben, müssen Sie nichts weiter tun. Hier ist Ihre GEDISA-ID automatisch hinterlegt und Sie sind ab sofort für alle KundInnen erreichbar, die via CardLink ihre eRezepte einlösen möchten.

Wenn Sie sich für eines der Alternativ-Angebote/Apps unserer Partner entschieden haben, benötigen Sie Ihre GEDISA-ID für den Anschluss an die jeweilige App. Bitte nutzen Sie den in der „Übersicht der Partner-Apps“ auf der CardLink-Seite des ApothekenPortals hinterlegten Link oder informieren Sie sich direkt beim Anbieter über den jeweiligen Weg der Aktivierung von CardLink.

Sollten Sie weitere Fragen zur neuen CardLink-Funktion haben, wenden Sie sich gerne an unseren Support unter <a href="mailto:<EMAIL>" class="underline"><EMAIL></a>.
@endif
</x-mail::message>
