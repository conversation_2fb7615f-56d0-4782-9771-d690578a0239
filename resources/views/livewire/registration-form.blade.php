<div class="mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
    <div wire:key="wrap" x-data="{
            showCompany: @entangle('showCompany').live
    }">
        @if ($sent)
            <x-thank-you-after-form>
                <x-slot:title>Vielen Dank!</x-slot:title>
                Wir haben Ihre Anfrage erhalten und werden Sie schnellstmöglich bearbeiten.
            </x-thank-you-after-form>
        @else
            @if ($errors->any())
                <div class="w-full mb-8" id="validation-errors">
                    <x:alert-validation-error/>
                </div>
            @endif

            <x:card wire:key="card">
                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.select
                                label="{{ __('validation.attributes.salutation') }}"
                                :options="\App\Enums\SalutationEnum::getLabels()"
                                name="salutation"
                                wire:model="salutation"
                                wire:key="salutation"
                                :error="$errors->first('salutation')"
                        >
                        </x-input.select>
                    </x:col>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.title') }}"
                                name="title"
                                wire:model="title"
                                wire:key="title"
                        >
                        </x-input.text>
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                            label="{{ __('validation.attributes.first_name') }}"
                            name="firstName"
                            wire:model="firstName"
                            wire:key="firstName"
                            :error="$errors->first('firstName')"
                            required
                        >
                        </x-input.text>
                    </x:col>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                            label="{{ __('validation.attributes.last_name') }}"
                            name="lastName"
                            wire:model="lastName"
                            wire:key="lastName"
                            :error="$errors->first('lastName')"
                            required
                        >
                        </x-input.text>
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.email') }}"
                                name="email"
                                wire:model="email"
                                wire:key="email"
                                :error="$errors->first('email')"
                                required
                        />
                    </x:col>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.email_confirmation') }}"
                                name="email_confirmation"
                                wire:model="email_confirmation"
                                wire:key="emailConfirmation"
                                :error="$errors->first('email_confirmation')"
                                required
                        />
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.phone') }}"
                                name="phone"
                                wire:model="phone"
                                wire:key="phone"
                                :error="$errors->first('phone')"
                                required
                        />
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:px-3">
                        <x-input.checkbox
                                x-model="showCompany"
                                wire:model="showCompany"
                                wire:key="showCompany"
                                name="showCompany"
                                id="showCompany"
                        >OHG
                        </x-input.checkbox>
                    </x:col>
                    <x:col class="sm:w-1/2 sm:px-3" x-show="showCompany">
                        <x-input.text
                                label="{{ __('validation.attributes.company_name') }}"
                                name="companyName"
                                wire:model="companyName"
                                wire:key="companyName"
                                :error="$errors->first('companyName')"
                                required
                        />
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.pharmacy_name') }}"
                                name="pharmacyName"
                                wire:model="pharmacyName"
                                wire:key="pharmacyName"
                                :error="$errors->first('pharmacyName')"
                                required
                        />
                    </x:col>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.telematics_id') }}"
                                name="telematicsId"
                                wire:model="telematicsId"
                                wire:key="telematicsId"
                                :error="$errors->first('telematicsId')"
                                required
                        />
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.street') }}"
                                name="street"
                                wire:model="street"
                                wire:key="street"
                                :error="$errors->first('street')"
                                required
                        />
                    </x:col>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.house_number') }}"
                                name="houseNumber"
                                wire:model="houseNumber"
                                wire:key="houseNumber"
                                :error="$errors->first('houseNumber')"
                                required
                        />
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.postcode') }}"
                                type="number"
                                name="postcode"
                                wire:model="postcode"
                                wire:key="postcode"
                                :error="$errors->first('postcode')"
                                required
                        />
                    </x:col>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.text
                                label="{{ __('validation.attributes.city') }}"
                                name="city"
                                id="city"
                                wire:model="city"
                                wire:key="city"
                                :error="$errors->first('city')"
                                required
                        />
                    </x:col>
                </x:row>

                <x:row wire:key="operatingLicense-row">
                    <x:col wire:key="operatingLicense-parent" class="mt-6 sm:px-3">
                        <p class="block text-sm leading-5 text-gray-700">{{ __('validation.attributes.operatingLicense') }} *</p>
                        <p class="block text-xs leading-5 text-gray-400">Erlaubte Dateiformate: PDF, JPEG, PNG (max. {{ \App\Livewire\RegistrationForm::MAX_UPLOAD_SIZE_HUMAN_READABLE}})</p>
                        <div wire:ignore class="mt-1 relative rounded-md shadow-sm" wire:key="operatingLicense">
                                <x-input.filepond
                                    acceptedFileTypes="['image/jpeg', 'image/png', 'application/pdf']"
                                    :allow-image-preview="false"
                                    :maxFileSize="\App\Livewire\RegistrationForm::MAX_UPLOAD_SIZE_HUMAN_READABLE"
                                    id="operatingLicense"
                                    name="operatingLicense"
                                    wire:model="operatingLicense"
                                    :files="[]"
                                />
                        </div>
                        @error('operatingLicense')
                        <p class="text-red-500 text-xs italic mt-2">{{ $message }}</p>
                        @endError
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:px-3">
                        <p class="block text-sm leading-5 text-gray-700">{{ __('validation.attributes.activityCertificate') }} [aktueller
                            Bescheid
                            des Nacht- und Notdienstfonds oder aktuelle Abrechnung
                            eines Apothekenrechenzentrums (jeweils mit geschwärzten Beträgen)] *</p>
                        <p class="block text-xs leading-5 text-gray-400">Erlaubte Dateiformate: PDF, JPEG, PNG (max. {{ \App\Livewire\RegistrationForm::MAX_UPLOAD_SIZE_HUMAN_READABLE}})</p>
                        <div wire:ignore class="mt-1 relative rounded-md shadow-sm" wire:key="activityCertificate">
                            <div>
                                <x-input.filepond
                                    acceptedFileTypes="['image/jpeg', 'image/png', 'application/pdf']"
                                    :allow-image-preview="false"
                                    :maxFileSize="\App\Livewire\RegistrationForm::MAX_UPLOAD_SIZE_HUMAN_READABLE"
                                    id="activityCertificate"
                                    name="activityCertificate"
                                    wire:model="activityCertificate"
                                />
                            </div>
                        </div>
                        @error('activityCertificate')
                        <p class="text-red-500 text-xs italic mt-2">{{ $message }}</p>
                        @endError
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:w-1/2 sm:px-3">
                        <x-input.select
                            label="{{ __('validation.attributes.associationId') }}"
                            name="associationId"
                            x-model="associationId"
                            wire:model="associationId"
                            wire:key="associationId"
                            :error="$errors->first('associationId')"
                        >
                            <option value="">keine</option>
                            @foreach(\App\Association::orderBy('name')->where('name', '!=', 'Testverband')->get() as $association)
                            <option value="{{ $association->id }}" @if (intval($associationId) === $association->id) selected @endif>
                                {{ $association->name }}
                            </option>
                            @endforeach
                        </x-input.select>
                    </x:col>
                </x:row>

                <x:row x-show="associationId">
                    <x:col class="mt-6 sm:px-3">
                        <p class="block text-sm leading-5 text-gray-700">Upload {{ __('validation.attributes.associationProof') }} *</p>
                        <p class="block text-xs leading-5 text-gray-400">Erlaubte Dateiformate: PDF, JPEG, PNG (max. {{ \App\Livewire\RegistrationForm::MAX_UPLOAD_SIZE_HUMAN_READABLE }})</p>
                        <div wire:ignore class="mt-1 relative rounded-md shadow-sm" wire:key="associationProof">
                            <div>
                                <x-input.filepond
                                    acceptedFileTypes="['image/jpeg', 'image/png', 'application/pdf']"
                                    :allow-image-preview="false"
                                    :maxFileSize="\App\Livewire\RegistrationForm::MAX_UPLOAD_SIZE_HUMAN_READABLE"
                                    id="associationProof"
                                    name="associationProof"
                                    wire:model="associationProof"
                                />
                            </div>
                        </div>
                        @error('associationProof')
                        <p class="text-red-500 text-xs italic mt-2">{{ $message }}</p>
                        @endError
                    </x:col>
                </x:row>

                <x:row>
                    <x:col class="mt-6 sm:px-3">
                        <x-input.checkbox
                                wire:model="consentForm"
                                wire:key="consentForm"
                                :error="$errors->first('consentForm')"
                                name="consentForm"
                                id="consentForm"
                        >
                            Zustimmung
                        </x-input.checkbox>
                        <div class="text-sm mt-4">
                            <p class="mb-4">
                                Ich bestätige die Richtigkeit der von mir vorstehend gemachten Angaben und beantrage
                                hiermit die Einrichtung eines Zugangs zum Apothekenportal "Mein Apothekenportal" der
                                GEDISA. Die
                                <x-link target="_blank"
                                        href="{{ asset('downloads/apothekenportal_nutzungsbedingungen.pdf') }}">
                                    Nutzungsbedingungen
                                </x-link>
                                sowie den
                                <x-link target="_blank"
                                        href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}">
                                    Auftragsverarbeitungsvertrag
                                </x-link>
                                habe ich zur Kenntnis genommen und erkläre mich mit den dortigen Bestimmungen sowie
                                der Zahlung des Nutzungsentgelts einverstanden. Ich kann meine Vertragsunterlagen
                                jederzeit nach erfolgreichem Login im ApothekenPortal unter dem Menüpunkt
                                <x-link target="_blank" href="{{ route('users.contracts') }}">Vertragsunterlagen
                                </x-link>
                                einsehen.
                            </p>
                            <p class="font-bold mt-4 mb-2">
                                Informationen zum Datenschutz
                            </p>
                            <p>
                                GEDISA verarbeitet die von Ihnen im Rahmen der Registrierung angegebenen Daten, um
                                Ihren Zugang zum Apothekenportal sowie die dort verfügbaren Funktionalitäten
                                bereitzustellen sowie zu Abrechnungszwecken. Weitere Informationen zum Datenschutz
                                bei GEDISA sowie zu Ihren Rechten als Betroffener finden Sie in unserer
                                <x-link target="_blank"
                                        href="{{ route('uploads.show', 'datenschutzerklaerung-apothekenportal') }}">
                                    Datenschutzerklärung
                                </x-link>
                                .
                            </p>
                            <p class="font-bold mt-4 mb-2">
                                Informationen zum Auftragsverarbeitungsvertrag
                            </p>
                            <p>
                                Die GEDISA und die von ihr beauftragten Unterauftragnehmer erbringen die
                                Dienstleistungen des Apothekenportals im Rahmen einer Auftragsverarbeitung gemäß
                                Artikel 28 Datenschutz-Grundverordnung (“DSGVO”). Die GEDISA agiert hierbei als
                                Auftragsverarbeiter für die datenschutzrechtlich verantwortlichen Apotheken. Der
                                gesetzlich vorgeschriebene Auftragsverarbeitungsvertrag, welcher zwischen der GEDISA
                                und den Apotheken zu schließen ist, wird über das GEDISA ApothekenPortal
                                bereitgestellt. Das erforderliche Einverständnis der verantwortlichen Apotheken mit
                                diesem Vertrag ist durch Anklicken des entsprechenden Feldes zu erklären. Für die
                                apothekeninterne Dokumentation kann der GEDISA-seitig bereits unterschriebene
                                <x-link target="_blank"
                                        href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}">
                                    Vertrag
                                </x-link>
                                über das GEDISA ApothekenPortal heruntergeladen
                                werden.

                            <div class="mt-2">
                                <ul class="list-disc pl-5">
                                    <li>
                                        Der Vertrag sollte von der Apotheke aufbewahrt werden.
                                    </li>
                                    <li>
                                        Die Übersendung einer seitens der Apotheke unterzeichneten Fassung an die
                                        GEDISA ist nicht
                                        erforderlich.
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </x:col>
                </x:row>
            </x:card>

            <div class="mt-12 w-full flex justify-center">
                <x-button
                    wire:click="saveRequest"
                    size="lg" class="w-72">
                    Speichern
                </x-button>

            </div>
        @endif
    </div>
</div>
