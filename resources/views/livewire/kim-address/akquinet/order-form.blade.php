@php use Illuminate\Support\Str; @endphp
<div>
    @if($apiError)
        <x-alert class="mb-8" type="warning" :title="__('kim.service_not_available_title')" :description="__('kim.service_not_available_description')"/>
    @endif

    <form wire:submit="submit">
        <x-header>
            <x-slot name="section">
                @lang('kim.register.title')
            </x-slot>
            <x-slot name="description">
                <x-kim.payment-details :pharmacy="$pharmacy"/>
            </x-slot>
        </x-header>

        <x-alert class="mb-8" type="info" title="Voraussetzung für die Installation des KIM Client-Moduls ist ein aktuelles Betriebssystem, d. h. mindestens Windows 10, Windows Server 2016 oder MacOS 10.13 (High Sierra)."/>

        @if(! user()->owner()->phone)
            <x-alert class="mb-8" type="warning" :title="__('kim.user_needs_phone_title')">
                <x-slot:description>
                    <p>@lang('kim.user_needs_phone_description')</p>

                    <br>
                    <x-button :href="route('users.edit')">
                        @lang('kim.user_needs_phone_button')
                    </x-button>
                </x-slot:description>
            </x-alert>
        @else
            @if ($kimAddress->exists && strlen(Str::before($kimAddress->email, '@')) < 3)
                <x-alert type="error" class="mb-8">
                    <x-slot name="title">
                        Die angegebene KIM-Adresse ist zu kurz
                    </x-slot>
                    <x-slot name="description">
                        Bitte kontaktieren Sie den Support unter <a class="underline" href="mailto:<EMAIL>"><EMAIL></a> um eine gültige KIM-Adresse zu erhalten.
                    </x-slot>
                </x-alert>
            @else
                <x:card>
                    @if(!$kimAddress->exists)
                        <x:row class="mb-4">
                            <x:col>
                                <x-input.text
                                        autocomplete="off"
                                        :label="__('messages.kimEmail')"
                                        name="email"
                                        placeholder="apothekenname.ort"
                                        :trailingAddOn="'@' . \App\Helper\KimAddressHelper::getKimAddressDomain($pharmacy)"
                                        trailingAddOnClass="pr-28 sm:pr-44"
                                        helpText="Bitte geben Sie nur den Teil vor dem @-Zeichen ein."
                                        wire:model="state.email"
                                        :required="true"
                                />
                            </x:col>
                            @if ($errors->first('state.email'))
                                <x-alert type="error" class="mb-8 mx-3">
                                    <x-slot name="description">
                                        {{ $errors->first('state.email') }}
                                    </x-slot>
                                </x-alert>
                            @endif
                        </x:row>
                    @endif

                    <x:row>
                        <x:col>
                            <x:input.checkbox
                                    id="terms-of-use-checkbox"
                                    wire:model="state.tosCheckboxAccepted"
                                    :error="$errors->first('tosCheckboxAccepted')">
                                Hiermit bestätige ich, dass ich die <a target="_blank" class="underline" href="{{ route('uploads.show', 'nutzungsbedingungen-kim') }}">Nutzungsbedingungen</a> und die <a target="_blank" class="underline" href="{{ route('uploads.show', 'datenschutzerklaerung-kim') }}">Datenschutzerklärung</a> gelesen habe und akzeptiere diese.
                            </x:input.checkbox>
                        </x:col>
                    </x:row>
                    <div class="pt-8 mt-8 flex justify-end space-x-2 border-t border-gray-200">
                        <x-button size="md" appearance="secondary" :href="route('kims', $pharmacy)">
                            {{ __('messages.abort') }}
                        </x-button>
                        <x-button size="md">
                            <div class="flex items-center">
                                <svg wire:loading wire:target="submit" class="animate-spin h-4 w-4 mr-2"
                                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                            stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span>
                                 {{ __('kim.register.kim_register_button') }}
                             </span>
                            </div>
                        </x-button>
                    </div>
                </x:card>
            @endif
        @endif
    </form>
</div>
