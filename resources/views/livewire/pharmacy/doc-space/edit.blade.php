<div class="grid md:grid-cols-3 gap-4 w-full">

    <div class="md:col-span-2" x-data="{value: @entangle('attr.hard_quota')}">
        @if ($editMode)

            <x-card title="Mein Datenraum" class="w-full" no-padding>
                <div class="w-full py-4 px-4 sm:px-10 border-b text-sm">
                    Das technische Kürzel für diese Apotheke lautet <b>{{ $pharmacy->getPharmacyIdBase64() }}</b>.
                    Es wird zur Identifikation im Datenraum verwendet und automatisch dem Gruppen- und Datenraumnamen
                    hinten angestellt.
                </div>
                <div class="w-full py-4 px-4 sm:px-10 border-b text-sm">
                    <div class="w-full">
                        @if (! $docSpace)
                            <x-input.text :disabled="$docSpace"
                                          wire:model.live="attr.name"
                                          :error="$errors->first('attr.name')"
                                          label="Name*"
                                          trailing-add-on=" [{{ $pharmacy->getPharmacyIdBase64() }}]"
                                          trailing-add-on-class="pr-28 sm:pr-24"
                                          placeholder="Wie soll der Datenraum heißen?"
                                          helpText="Um die Verwaltung von Datenräumen für mehrere Apotheken zu erleichtern, empfehlen wir Ihnen,
ein eindeutiges Kürzel für jeden Standort an den Anfang des Namens zu stellen.
Verwenden Sie zum Beispiel die ersten drei Buchstaben der jeweiligen Apotheke oder der Straße,
etwa „{{ $pharmacy->upperShortName }}_Impfen“ für „{{ $pharmacy->name }}“."/>
                        @else
                            <x-input.text :disabled="true"
                                          value="{{ $attr['full_name'] }}"
                                          label="Name*" />
                        @endif
                    </div>

                    @if ($errors->has('name'))
                        <p class="mt-2 text-sm text-red-600">{{ $errors->first('name') }}</p>
                    @endif

                    <div class="w-full mt-4">
                        <x-input.text wire:model.live="attr.description" :error="$errors->first('attr.description')"
                                      label="Kurzbeschreibung" placeholder="Wozu soll der Datenraum dienen?"/>
                    </div>

                    <div class="w-full mt-4">
                        @if (! $docSpace)
                            <x:input.toggle
                                label="Revisionssicher"
                                :value="\App\Enums\DocSpaceType::REVISION_SAFE->value"
                                name="attr.type"
                                id="attr.type"
                                wire:model.live="attr.type"
                                :error="$errors->first('attr.type')"
                            />
                        @else
                            <div class="block text-sm font-medium leading-5 text-gray-700">Art</div>
                            <p class="text-sm text-gray-700 mt-1">{{ \App\Enums\DocSpaceType::getLabel( $attr['type'] ) }}</p>
                        @endif
                    </div>

                    <div class="w-full mt-4">
                        <div class="block text-sm font-medium leading-5 text-gray-700">Speicherplatz</div>
                        <p class="text-sm text-gray-700 mt-1">{{ config('sdr.createGuided.hardQuotaFixed') }} GB</p>
                    </div>
                </div>
            </x-card>

        @else

            <x-card title="Mein Datenraum" class="w-full" no-padding>
                <div class="w-full py-4 px-4 sm:px-10 border-b text-sm">
                    Das technische Kürzel für diese Apotheke lautet <b>{{ $pharmacy->getPharmacyIdBase64() }}</b>. Es
                    wird zur Identifikation im Datenraum verwendet und automatisch dem Gruppen- und Datenraumnamen
                    hinten angestellt.
                </div>
                <div class="pt-4 pb-8 px-4 sm:px-10 border-b text-sm">
                    <x-font.h2 class="w-full">
                        {{ $attr['full_name'] }}
                    </x-font.h2>
                    <div class="w-full">
                        <p class="mt-1 text-sm leading-5 text-gray-500">
                            {{ $attr['description'] }}
                        </p>

                        @if(! user()->can('sdr.openInRiseUI', $docSpace))
                            <p class="mt-1 text-sm leading-5 text-red-500">
                                Sie sind aktuell keiner Gruppe zugewiesen, die diesen Datenraum nutzen kann.
                            </p>
                        @endif
                    </div>
                    <div class="w-fill mt-4">
                        <div class="flex justify-between text-sm font-medium leading-5 text-gray-700">
                            <label for="" class="block">{{ number_format($docSpace->current_usage_in_gb, 3) }}/<span
                                    x-text="value"></span> GB belegt</label>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-red-500 h-2.5 rounded-full"
                                 style="width: {{ $docSpace->current_usage * 100 / $docSpace->hard_quota }}%"></div>
                        </div>
                    </div>
                </div>
            </x-card>

        @endif
    </div>

    <div class="md:col-span-1">
        <x-card
            title="Zugewiesene Gruppen"
            class="w-full"
            no-padding
        >
            <div class="py-4 px-4 sm:px-10 border-b text-sm">
                @if ($editMode)
                    <p>Hier bestimmen Sie, wer Zugriff auf diesen Datenraum hat.
                        Sie können beliebig viele Gruppen hinzufügen.</p>
                @else
                    <p>Hier sehen Sie die Nutzergruppen und die dazugehörigen berechtigten Personen.</p>
                @endif
            </div>
            <div class="py-4" id="docSpaceGroups">
                @forelse($docSpaceGroups as $docSpaceGroup)
                    @if ($editMode)
                        <div class="py-2 flex justify-between items-center pl-4 pr-2 sm:px-10 text-sm">
                            <x-input.checkbox wire:model="attr.groups" value="{{ $docSpaceGroup['id'] }}"
                                              id="{{ $docSpaceGroup['id'] }}">
                                <p class="hyphens-auto break-all" lang="de">{{ $docSpaceGroup['full_name'] }}</p>
                            </x-input.checkbox>
                        </div>
                    @elseif (in_array($docSpaceGroup['id'], $attr['groups']))
                        <div class="py-2 flex gap-2 justify-between items-center pl-4 pr-2 sm:px-10 text-sm">
                            <div class="hyphens-auto break-all" lang="de">{{ $docSpaceGroup['full_name'] }}</div>
                            <div class="flex items-center">
                                <div>
                                    <span class="tooltip" data-tippy-content="
                                        <div class='p-3'>
                                            <div class='mb-2'>Mitglieder in dieser Gruppe</div>
                                            @if (count($docSpaceGroup['users']))
                                                <ul class='list-inside list-disc'><li>{!! implode('</li><li>', $docSpaceGroup['users']) !!}</li></ul>
                                            @else
                                                - keine -
                                            @endif
                                        </div>
                                    ">
                                        <svg class="h-5 w-5 text-gray-400 hover:text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                            <use href="/icons.svg#information-circle"/>
                                        </svg>
                                    </span>
                                </div>
                                @if (user()->can('administrate', $docSpaceGroup['model']))
                                    <x-sdr.docspace-group-action-dropdown
                                            :doc-space-group="$docSpaceGroup['model']"
                                    />
                                @endif
                            </div>
                        </div>
                    @endif
                @empty
                    <div class="flex px-10 py-2 text-sm justify-center">
                        noch keine Gruppen vorhanden
                    </div>
                @endforelse
            </div>
            @if ($editMode)
                <div class="flex px-10 justify-center">
                    @if ($errors->has('attr.groups'))
                        <p class="mt-2 text-sm text-red-600 px-10">{{ $errors->first('attr.groups') }}</p>
                    @endif
                </div>

                <div class="flex p-4 justify-center">
                    <x-button x-data="{}" x-on:click="Livewire.dispatch('open-doc-space-create-group-modal')"
                              appearance="secondary">
                        Gruppe erstellen
                    </x-button>
                </div>
            @endif
        </x-card>
    </div>

    <div class="md:col-span-2">
        <div class="flex justify-end">
            @if (! $editMode)
                <div>
                    @if(user()->can('sdr.openInRiseUI', $docSpace))
                        <x-button href="{{ config('sdr.uiUrl') }}/documents?docSpaceId={{ $docSpace->sdr_doc_space_id }}" target="_blank">
                            Datenraum öffnen
                        </x-button>
                    @endif
                </div>
                @if (user()->can('sdr.viewAny', currentPharmacy()))
                    <div class="ml-4">
                        <x-button wire:click="toggleEditMode" appearance="secondary">
                            Datenraum bearbeiten
                        </x-button>
                    </div>
                @endif
            @endif
            @if ($editMode && user()->can('sdr.viewAny', currentPharmacy()))
                @if ($docSpace)
                    <div class="mr-4">
                        <x-button href="{{ route('sdr.doc-spaces.show', [$pharmacy, $docSpace]) }}" appearance="secondary">
                            Abbrechen
                        </x-button>
                    </div>
                @endif
                <x-button wire:click="save" wire:loading.attr="disabled">
                    @if ($docSpace)
                        Speichern
                    @else
                        Jetzt erstellen
                    @endif
                </x-button>
            @endif
        </div>
    </div>

</div>
