<div class="w-full">

    <div class="flex justify-between items-center mb-8">
        <x-font.h1>Gruppen verwalten</x-font.h1>

        @if (user()->can('store', [\App\DocSpaceGroup::class, $pharmacy]))
            <x-button x-data="{}" x-on:click="Livewire.dispatch('open-doc-space-create-group-modal')" appearance="primary">
                Gruppe erstellen
            </x-button>
        @endif
    </div>

    <p class="text-sm leading-5 text-gray-500 font-normal mb-8">
        Hier sehen Sie die Nutzergruppen und die dazugehörigen berechtigten Personen.
    </p>


    <ul id="docSpaceGroups">
        @foreach($docSpaceGroups as $docSpaceGroup)
            <x-sdr.docspace-group-card
                :doc-space-group="$docSpaceGroup"
            />
        @endforeach
    </ul>

</div>
