<div>
    @if($showMoreText)
        <p class="mt-2">
            Datenschutzrechtlich sind die zertifikatsausstellenden und impfenden (COVID-19 und Grippeschutz) Apotheken
            verantwortlich für die Übermittlung der Patientendaten an das Robert-Koch-Institut. Der Deutsche
            Apothekerverband e.V. und die von ihm beauftragten Unterauftragnehmer agieren dabei als Auftragsverarbeiter.
            Der entsprechende Auftragsverarbeitungsvertrag, der zwischen dem Deutschen Apothekerverband e.V. und den
            Apotheken zu schließen ist, wird über das Portal bereitgestellt. Das datenschutzrechtlich erforderliche
            Einverständnis der zertifikatsausstellenden Apotheken mit diesen Vertragsbedingungen ist im
            Registrierungsbereich des Portals durch Anklicken des entsprechenden Feldes zu erklären. Für die
            apothekeninterne Dokumentation kann der GEDISA-seitig bereits unterschriebene <a
                href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}" target="_blank"
                class="underline text-gray-600">Vertrag</a> über das Portal heruntergeladen werden.
        </p>

        @if($setting)
            @if((bool) $setting->value)
                <p class="mt-2">Sie haben am {{ $setting->created_at->setTimezone('Europe/Berlin')->format('d.m.Y') }} um {{ $setting->created_at->format('H:i') }} eingewilligt.</p>
            @else
                <p class="mt-2">Sie haben nicht eingewilligt.</p>
            @endif
        @endif

        @if($isPharmacyOwner && (!$setting || !$setting->value))
            <div class="mt-8">
                <x-button
                    size="lg"
                    appearance="secondary"
                    wire:click="$set('avvstate', true)"
                    class="bg-white"
                >
                    Einwilligen
                </x-button>
            </div>
        @endif
    @endif
    <x:modal name="avv-modal" modal-model-name="avvstate" close-button="false">
        <div>
            <div class="mt-3 sm:mt-5">
                <h3 class="text-lg leading-6 font-medium text-gray-900 text-center" id="modal-headline">
                    Auftragsverarbeitungsvertrag
                </h3>
                <div class="mt-2">
                    <p class="text-sm leading-5 text-gray-500">
                        Datenschutzrechtlich sind die zertifikatsausstellenden und impfenden (COVID-19 und Grippeschutz)
                        Apotheken verantwortlich für die Übermittlung der Patientendaten an das Robert-Koch-Institut.
                        Der Deutsche Apothekerverband e.V. und die von ihm beauftragten Unterauftragnehmer agieren dabei
                        als Auftragsverarbeiter. Der entsprechende Auftragsverarbeitungsvertrag, der zwischen dem
                        Deutschen Apothekerverband e.V. und den Apotheken zu schließen ist, wird über das Portal
                        bereitgestellt. Das datenschutzrechtlich erforderliche Einverständnis der
                        zertifikatsausstellenden Apotheken mit diesen Vertragsbedingungen ist im Registrierungsbereich
                        des Portals durch Anklicken des entsprechenden Feldes zu erklären. Für die apothekeninterne
                        Dokumentation kann der GEDISA-seitig bereits unterschriebene <a
                            href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                            target="_blank"
                            class="underline text-gray-600">Vertrag</a> über das Portal heruntergeladen werden.
                    </p>
                </div>
                <div class="mt-2">
                    <ul class="list-disc text-sm leading-5 pl-5 text-gray-500">
                        <li>
                            Dieser Vertrag sollte von der Apotheke aufbewahrt werden.
                        </li>
                        <li>
                            Eine Übersendung einer seitens der Apotheke unterzeichneten Fassung an den GEDISA ist nicht erforderlich.
                        </li>
                    </ul>
                </div>
                <div class="mt-6 sm:px-3">
                    <x:input.checkbox
                        wire:model.live="checkboxActive"
                        id="data-processing-contract-checkbox"
                    >
                        Ich erkläre mein Einverständnis mit dem <a
                            href="{{ asset('//downloads/apothekenportal_auftragsverarbeitungsvertrag.pdf') }}"
                            target="_blank" class="underline text-gray-600">Vertrag zur Auftragsverarbeitung</a>
                        zwischen dem Deutschen Apothekerverband e.V. und mir.
                    </x:input.checkbox>
                </div>
            </div>
        </div>
        <div class="mt-5 sm:mt-6">
            <span class="">
                <span class="w-1/2 rounded-md shadow-sm mx-2">
                    <button
                        wire:click="save"
                        type="button"
                        class="inline-flex justify-center w-full rounded-md border border-transparent px-4 py-2 bg-red-600 text-base leading-6 font-medium text-white shadow-sm hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-red transition ease-in-out duration-150 sm:text-sm sm:leading-5 disabled:opacity-50"
                    >
                        Speichern
                    </button>
                </span>
            </span>
        </div>
    </x:modal>
</div>
