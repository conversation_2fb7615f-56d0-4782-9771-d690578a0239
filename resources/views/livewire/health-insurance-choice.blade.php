<div wire:ignore>
    @push('scripts')
        <script>
            function initialize() {
                return {
                    isPrivate: 0,
                    value: @entangle('value'),
                    options: @entangle('options'),
                    init() {
                        this.$nextTick(() => {
                            let choices = new Choices(this.$refs.select, {
                                allowHTML: false,
                                searchChoices: false,
                                loadingText: 'Lädt...',
                                noResultsText: 'Keine Ergebnisse gefunden',
                                noChoicesText: 'Keine Einträge vorhanden',
                                itemSelectText: 'auswählen',
                                uniqueItemText: 'Nur einzigartige Werte können hinzugefügt werden',
                                customAddItemText: 'Nur Werte, die bestimmte Bedingungen erfüllen, können hinzugefügt werden',
                                searchPlaceholderValue: 'Krankenkasse oder IK Nummer suchen (min. 3 Zeichen)',
                                searchFields: ['label'],
                            })

                            let refreshChoices = () => {
                                let selection = [this.value]
                                choices.clearStore()
                                choices.setChoices(this.options.map(({value, label}) => ({
                                    value,
                                    label,
                                    selected: selection.includes(value),
                                })))
                            }

                            refreshChoices()

                            this.$refs.select.addEventListener('change', () => {
                                this.value = choices.getValue(true)
                            })

                            this.$refs.select.addEventListener('search', (event) => {
                                if (event.detail.value.length >= 3) {
                                    Livewire.dispatch('update-health-insurance-choices', {pattern: event.detail.value})
                                }
                            })

                            this.$refs.select.addEventListener('showDropdown', () => {
                                document.getElementById('content-wrapper').classList.add('overflow-visible')
                            })

                            this.$refs.select.addEventListener('hideDropdown', () => {
                                document.getElementById('content-wrapper').classList.remove('overflow-visible')
                                choices.clearInput();
                                Livewire.dispatch('update-health-insurance-choices', {pattern: ''})
                            })

                            this.$watch('value', () => refreshChoices())
                            this.$watch('options', () => refreshChoices())
                        })
                    }
                }
            }
        </script>
    @endpush
    <form action="" METHOD="POST">
        @csrf
        <div x-data="initialize()">
            <x:card>
                <x:row>
                    <x:col>
                        <div class="mt-10">
                            <x:input.radio-group
                                    label="Versicherungstyp"
                                    name="insurance_type"
                                    :labels="[0 => 'Gesetzlich', 1 => 'Privat']"
                                    :checked="$healthInsuranceType"
                                    wire:model="isPrivate"
                                    wire:loading.attr="disabled"
                                    orientation="horizontal"
                                    :error="$errors->first('insurance_type')"
                            />
                        </div>
                    </x:col>
                    <x:col>
                        <div>
                            <label for="health_insurance_company_id"
                                   class="block text-sm font-medium leading-5 text-gray-700">Krankenversicherung</label>
                            <div class="w-full relative z-50">
                                <div>
                                    <select x-ref="select" name="health_insurance_company_id"></select>
                                </div>
                            </div>
                            @if ($errors->first('health_insurance_company_id'))
                                <p class="mt-2 text-sm text-red-600">
                                    {{ $errors->first('health_insurance_company_id') }}
                                </p>
                            @endif
                        </div>

                    </x:col>
                </x:row>

            </x:card>
            <div class="mt-12 w-full flex justify-center">
                <x-button x-ref="saveBtn" x-bind:disabled="!value" wire:loading.attr="disabled" type="submit" size="lg"
                          class="w-72">
                    Speichern
                </x-button>
            </div>
        </div>
    </form>
</div>
