@php
    use App\Domains\Association\Domain\Enums\AssociationMemberListEntryStatus;use App\Domains\Association\Domain\Enums\MemberImportStatus;
@endphp

<div>
    <div class="ml-2 mb-2 inline-flex gap-2">
        <p class="text-sm leading-5 text-gray-500">*</p>
        <p class="text-sm leading-5 text-gray-500">
            Diese Inhaber haben ihre Daten nicht vollständig im Apothekenportal hinterlegt. Damit die Daten
            vollständig<br>
            angezeigt werden können, muss sich der Inhaber am Apothekenportal anmelden und seine Daten aktualisieren.
        </p>
    </div>

    <div class="w-full flex -mx-4">
        <div class="w-full md:w-1/2 xl:w-1/4 p-4">
            <x-input.text
                name="search"
                label="Suche"
                wire:model.live="search"
            />
        </div>
    </div>

    <x:stackedList>
        <x:stackedListHead>
            <div class="w-8/12">
                <x-link href="javascript:void(0)" class="items-center" wire:click="changeSorting('last_name')">
                    Name
                    @if ($sortBy === 'last_name')
                        <svg class="h-5 w-5 text-gray-400">
                            <use href="/icons.svg#chevron-up-down"/>
                        </svg>
                    @endif
                </x-link>
            </div>
            <div class="w-8/12">
                <x:link href="javascript:void(0)" class="items-center" wire:click="changeSorting('email')">
                    E-Mail
                    @if ($sortBy === 'email')
                        <svg class="h-5 w-5 text-gray-400">
                            <use href="/icons.svg#chevron-up-down"/>
                        </svg>
                    @endif
                </x:link>
            </div>
            <div class="w-8/12">
                <x:link href="javascript:void(0)" class="items-center" wire:click="changeSorting('pharmacies_count')">
                    Apotheken
                    @if ($sortBy === 'pharmacies_count')
                        <svg class="h-5 w-5 text-gray-400">
                            <use href="/icons.svg#chevron-up-down"/>
                        </svg>
                    @endif
                </x:link>
            </div>
            <div class="w-8/12">
                <x:link href="javascript:void(0)" class="items-center" wire:click="changeSorting('status')">
                    Status
                    @if ($sortBy === 'status')
                        <svg class="h-5 w-5 text-gray-400">
                            <use href="/icons.svg#chevron-up-down"/>
                        </svg>
                    @endif
                </x:link>
            </div>
            <div class="w-8/12">
                <x:link href="javascript:void(0)" class="items-center" wire:click="changeSorting('created_at')">
                    Erstellungsdatum
                    @if ($sortBy === 'created_at')
                        <svg class="h-5 w-5 text-gray-400">
                            <use href="/icons.svg#chevron-up-down"/>
                        </svg>
                    @endif
                </x:link>
            </div>
            <div class="w-5"></div>
        </x:stackedListHead>

        <x:stackedListBody>
            @forelse($this->members as $member)
                <x-stackedListRow
                    wire:key="members-{{ $member->id }}"
                    @class([
                        'bg-gray-100 hover:bg-gray-100 text-gray-400' => ! $member->isEditable(),
                        'hover:bg-gray-50' => $member->isEditable(),
                    ])
                >
                    {{-- Name --}}
                    <div class="flex items-center w-full md:w-8/12">
                        <div>
                            @if(empty($member->lastName) || empty($member->firstName))
                                <div class="text-sm leading-5 font-medium text-gray-400">
                                    Nachname, Vorname *
                                </div>
                            @else
                                <div class="text-sm px-5 font-medium">
                                    {{ trim($member->title . ' ' . $member->lastName) . ', ' . $member->firstName }}
                                </div>
                            @endif

                        </div>
                    </div>

                    {{-- Email --}}
                    @if($member->email)
                        <div class="flex items-center w-full md:w-8/12">
                            <div>
                                <div class="text-sm px-5 break-all">
                                    {{ trim($member->email) }}
                                </div>
                            </div>
                        </div>
                    @endif

                    {{-- Anzahl Apotheken --}}
                    <div class="flex items-center w-full md:w-8/12">
                        <div>
                            <div class="text-sm leading-5">
                                {{ $member->pharmaciesCount }}
                            </div>
                        </div>
                    </div>

                    {{-- Status --}}
                    <div class="flex items-center w-full md:w-8/12">
                        <div>
                            <x:badge color="{{ $member->status->color() }}" class="gap-1">
                                <p>{{ $member->status->translate() }}</p>
                                <div>
                                    @if ($member->statusDescription)
                                        <flux:tooltip content="{{ $member->statusDescription }}">
                                            <svg class="inline-block h-4 w-4 text-red-800 hover:text-red-900">
                                                <use
                                                    href="/icons.svg#question-mark-circle"/>
                                            </svg>
                                        </flux:tooltip>
                                    @endif
                                </div>
                            </x:badge>
                        </div>
                    </div>

                    {{-- Erstellungsdatum --}}
                    <div class="flex items-center w-full md:w-8/12">
                        <div>
                            <div class="text-sm leading-5">
                                {{ $member->createdAt->format('d.m.Y, H:i:s') }}
                            </div>
                        </div>
                    </div>

                    <div class="w-5 h-5">
                        @if($member->isAssociationChangable())
                            <button x-data="{}"
                                    wire:click="removeMember('{{ $member->id }}')"
                            >
                                <svg class="w-5 h-5">
                                    <use href="/icons.svg#x-mark"></use>
                                </svg>
                            </button>
                        @endif
                        @if($member->isDeletable())
                            <button x-data="{}"
                                    wire:click="removeMemberImport('{{ $member->id }}')"
                            >
                                <x-svg-icon class="w-5 h-5" icon-id="trash"/>
                            </button>
                        @endif
                    </div>
                </x-stackedListRow>
            @empty
                <x:stackedListRow>
                    <div class="text-center w-full">
                        Keine Einträge
                    </div>
                </x:stackedListRow>
            @endforelse
        </x:stackedListBody>
    </x:stackedList>

    <x:modal close-button="true"
             :modal-open="$modalOpen"
             :name="$modalName"
             title="Upload löschen"
    >
        <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center space-x-2 mb-8">
            Mitglieder hinzufügen
        </h3>
        <p class="mb-4">
            Laden Sie eine Excel Datei hoch, die die Mitglieder-Liste enthält.<br>
            Eine entsprechende Vorlage können Sie
            <a href="{{ route('association-member-import') }}"
               class="font-medium text-red-600 hover:text-red-500 focus:outline-none focus:underline transition ease-in-out duration-150"
            >hier herunterladen</a>.<br>
            Erlaubte Dateitypen sind: {{ $acceptedFileExtensions }}
        </p>
        <div class="mt-1 relative rounded-md shadow-sm">
            <x-input.filepond
                wire:model="file"
                accepted-file-types="{!! $acceptedFileTypes !!}"
                :allow-image-preview="false"
                id="addMembersFileUpload"
                :display-all-errors="true"
            />
        </div>
        <x:slot name="footer">
            <div class="flex justify-end space-x-2">
                <x:button x-on:click="$dispatch('close-modal')" appearance="secondary">
                    Abbrechen
                </x:button>
                <x:button wire:click="addMembers">
                    Mitglieder hinzufügen
                </x:button>
            </div>
        </x:slot>
    </x:modal>

    <x:success-alert-modal name="successfully-imported"
                           title="Import erfolgreich">
        Die Datei wurde erfolgreich importiert. Die Datensätze werden nun automatisiert geprüft.
        Den Status der Registrierung entnehmen Sie bitte der Mitgliederverwaltungsübersicht.
        <x:slot name="actionButton">
            <x:button x-on:click="$dispatch('close-modal')" wire:click="$refresh">
                OK
            </x:button>
        </x:slot>
    </x:success-alert-modal>

    <x-danger-alert-modal
        name="delete-association-member"
        title="Verbandsmitglied entfernen"
    >
        @if($userToRemove)
            Zum Ende welchen Monats (letzter Monat der Mitgliedschaft) wollen Sie den ausgewählten
            Apotheker {{ trim($userToRemove->title . ' ' . $userToRemove->last_name) . ', ' . $userToRemove->first_name }}
            ({{ $userToRemove->email }}) aus Ihrem Verband entfernen? Dieser
            Vorgang kann nicht rückgängig gemacht werden, da die Daten unwiderruflich gelöscht werden.

            <div class="mt-2">
                <x-input.select
                    label="Austrittsdatum"
                    :options="$this->getRemoveMemberDateOptions()"
                    :selected="$this->setSelectedRemoveMemberDate()"
                    name="removeMemberDate"
                    wire:model.live="removeMemberDate"
                    wire:key="removeMemberDate"
                    :error="$errors->first('removeMemberDate')"
                />
            </div>

            <x-slot name="actionButton">
                <x-button wire:click="removeAssociationMember('{{ $userToRemove->id }}')">
                    Verbandsmitglied entfernen
                </x-button>
            </x-slot>

            <x-slot name="cancelButton">
                <x-button x-on:click="$dispatch('close-modal')" appearance="secondary">
                    Abbrechen
                </x-button>
            </x-slot>
        @endif
    </x-danger-alert-modal>

    <x-success-alert-modal name="successfully-deleted-association-member"
                           title="Entfernen war erfolgreich">
        Das Mitglied wurde erfolgreich zur Entfernung vorgemerkt und per E-Mail informiert.
        Es kann nicht mehr bearbeitet werden.
        <x:slot name="actionButton">
            <x:button x-on:click="$dispatch('close-modal')">
                OK
            </x:button>
        </x:slot>
    </x-success-alert-modal>

    <x-danger-alert-modal name="failed-to-delete-association-member"
                          title="Entfernen fehlgeschlagen">
        Leider konnte das Mitglied nicht entfernt werden.
        Bitte versuchen Sie es später noch mal oder wenden Sie sich an den Support!
        <x:slot name="actionButton">
            <x:button x-on:click="$dispatch('close-modal')">
                OK
            </x:button>
        </x:slot>
    </x-danger-alert-modal>

    <x-danger-alert-modal
        name="delete-member-import"
        title="Import-Eintrag löschen"
    >
        Möchten Sie diesen Import-Eintrag wirklich löschen?
        Dieser Vorgang kann nicht rückgängig gemacht werden.

        <x-slot name="actionButton">
            <x-button wire:click="confirmRemoveMemberImport">
                Löschen
            </x-button>
        </x-slot>

        <x-slot name="cancelButton">
            <x-button x-on:click="$dispatch('close-modal')" appearance="secondary">
                Abbrechen
            </x-button>
        </x-slot>
    </x-danger-alert-modal>
</div>

