<div>
    @can('updateTos', \App\Setting::class)
        @if($this->hasAcceptedOldTerms())
            <x-alert type="warning">
                <x-slot:description>
                    Sie haben bereits unseren vorherigen Nutzungsbedingungen sowie dem Auftragsverarbeitungsvertrag
                    zugestimmt. Diese können Sie hier einsehen:

                    <x-tos.old-terms-of-service-links/>
                    <x-link href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}" target="_blank">Auftragsverarbeitungsvertrag</x-link>

                    <p class="mt-2">Bitte bestätigen Sie bis
                        zum {{ app(\App\Settings\TermsOfServiceSettings::class)->new_terms_of_service_deadline->format('d.m.Y') }}
                        die neuen Nutzungsbedingungen, um das ApothekenPortal weiterhin ohne Unterbrechung nutzen zu
                        können. </p>

                    @if ($this->canDeclineTerms())
                        <span x-data="{open: false}" class="mt-2">
                    <button class="my-2 underline" x-on:click="open = !open">Vorherige Nutzungsbedingungen und Auftragsverarbeitungsvertrag ablehnen</button>
                        <span x-cloak x-show="open">
                            <form wire:submit="declineOld">
                                    <x:input.checkbox id="accepted2" wire:model.live="tosDeclineOldTerms"
                                                      :error="$errors->first('tosDeclineOldTerms')">
                                        Ich bestätige, dass ich keinen Zugriff mehr auf das Portal haben werde, wenn ich die neuen Nutzungsbedingungen ablehne.
                                    </x:input.checkbox>
                                <div class="mt-2">
                                    <x:input.checkbox id="accepted3" wire:model.live="tosDeclineOldDataContract"
                                                      :error="$errors->first('tosDeclineOldDataContract')">
                                        Ich bestätige, dass ich keinen Zugriff mehr auf das Portal haben werde, wenn ich dem Auftragsverarbeitungsvertrag nicht zustimme.
                                    </x:input.checkbox>
                                </div>


                                <div class="mt-4 flex justify-end">
                                    <x:button type="submit" wire:loading.attr="disabled"
                                              wire:target="declineOld">Nutzungsbedingungen und Auftragsverarbeitungsvertrag ablehnen</x:button>
                                </div>
                            </form>
                        </span>
                    </span>
                    @else
                        <div class="pt-2">
                            {{$this->termsNotDeclinableText}}
                        </div>
                    @endif
                </x-slot:description>
            </x-alert>
        @endif

        @if($this->tosAccepted)
            <div class="mb-4">
                <x:alert type="success">
                    <x:slot name="description">
                        <p>Sie haben die aktuellen Nutzungsbedingungen sowie den Auftragsverarbeitungsvertrag akzeptiert
                            und können das ApothekenPortal weiterhin nutzen. {{  !$this->canDeclineTerms() ? 'Aufgrund Ihrer bestehenden Mitgliedschaft
                            ist eine Ablehnung der Nutzungsbedingungen und des Auftragsverarbeitungsvertrags erst nach
                            erfolgter Kündigung möglich.' : ''}}</p>
                    </x:slot>
                </x:alert>
            </div>

            <div>
                <div class="mb-4">
                    <x:subscriptions.section-headline
                            headline="Nutzungsbedingungen"
                    />
                </div>

                <x:tos.terms-of-service-links/>
                @if ($this->canDeclineTerms())
                    <p class="my-4">Sie können die Nutzungsbedingungen ablehnen. Anschließend haben Sie keinen Zugriff
                        mehr
                        auf die Funktionen.</p>

                    <div class="mt-4">
                        <x:input.checkbox id="accepted" wire:model.live="tosDeclineCheckboxAccepted"
                                          :error="$errors->first('tosDeclineCheckboxAccepted')">
                            Ich bestätige, dass ich keinen Zugriff mehr auf das Portal haben werde, wenn ich die
                            Nutzungsbedingungen ablehne.
                        </x:input.checkbox>
                    </div>
                @endif

            </div>
            <div>
                <x:subscriptions.section-headline
                        headline="Auftragsverarbeitungsvertrag"
                />
                <div class="mt-2">
                    <p class="leading-5">
                        Die aktuelle Version des Auftragsverarbeitungsvertrags finden Sie hier:
                    </p>
                </div>

                <div class="mt-2">
                    <p class="leading-5">
                        <x-link href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                                target="_blank">
                            Auftragsverarbeitungsvertrag
                        </x-link>
                    </p>
                </div>

                <div class="mt-3">
                    <p class="leading-5">
                        Die GEDISA und die von ihr beauftragten Unterauftragnehmer erbringen die Dienstleistungen des
                        Apothekenportals im Rahmen einer Auftragsverarbeitung gemäß Artikel 28
                        Datenschutz-Grundverordnung (“DSGVO”). Die GEDISA agiert hierbei als Auftragsverarbeiter für die
                        datenschutzrechtlich verantwortlichen Apotheken. Der gesetzlich vorgeschriebene
                        Auftragsverarbeitungsvertrag, welcher zwischen der GEDISA und den Apotheken zu schließen ist,
                        wird über das GEDISA ApothekenPortal bereitgestellt. Das erforderliche Einverständnis der
                        verantwortlichen Apotheken mit diesem Vertrag ist durch Anklicken des entsprechenden Feldes zu
                        erklären. Für die apothekeninterne Dokumentation kann der GEDISA-seitig bereits unterschriebene
                        <x-link
                                href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                                target="_blank">Vertrag
                        </x-link>
                        über das GEDISA ApothekenPortal
                        heruntergeladen werden.
                    </p>
                </div>

                <div class="mt-2">
                    <ul class="list-disc pl-5">
                        <li>
                            Der Vertrag sollte von der Apotheke aufbewahrt werden.
                        </li>
                        <li>
                            Die Übersendung einer seitens der Apotheke unterzeichneten Fassung an die GEDISA ist nicht
                            erforderlich.
                        </li>
                    </ul>
                </div>

                @if ($this->canDeclineTerms())
                    <form wire:submit="declineTerms">

                        <div class="mt-4">
                            <x:input.checkbox id="avvDeclineCheckboxAccepted"
                                              wire:model.live="avvDeclineCheckboxAccepted"
                                              :error="$errors->first('avvDeclineCheckboxAccepted')">
                                Ich bestätige, dass ich keinen Zugriff mehr auf das Portal haben werde, wenn ich dem
                                Auftragsverarbeitungsvertrag nicht zustimme.
                            </x:input.checkbox>
                        </div>

                        <div class="mt-4 flex justify-end">
                            <x:button type="submit">Nutzungsbedingungen und Auftragsverarbeitungsvertrag ablehnen
                            </x:button>
                        </div>
                    </form>
                @endif
            </div>
        @else
            @if(! $this->hasAcceptedOldTerms())
                <div class="mb-4">
                    <x:alert type="warning">
                        <x:slot name="description">
                            Sie haben die aktuellen Nutzungsbedingungen und den Auftragsverarbeitungsvertrag noch nicht
                            akzeptiert.
                            Bitte akzeptieren Sie die
                            Nutzungsbedingungen und den Auftragsverarbeitungsvertrag, um die Dienste des
                            Apothekenportals
                            nutzen zu können.
                        </x:slot>
                    </x:alert>
                </div>
            @endif

            <div>
                <div class="mb-4">
                    <x:subscriptions.section-headline
                            headline="Nutzungsbedingungen akzeptieren"
                    />
                </div>

                <x-tos.terms-of-service-links/>

                <p class="mt-4">Bitte lesen Sie diese Nutzungsbedingungen sorgfältig. Falls Sie die Nutzungsbedingungen
                    nicht
                    akzeptieren, dürfen Sie das ApothekenPortal nicht nutzen.</p>
                <div class="mt-4">
                    <x:input.checkbox id="accepted" wire:model.live="tosCheckboxAccepted"
                                      :error="$errors->first('tosCheckboxAccepted')">
                        Hiermit bestätige ich, dass ich die Nutzungsbedingungen gelesen habe und akzeptiere
                        diese.
                    </x:input.checkbox>
                </div>
            </div>
            <div>
                <x:subscriptions.section-headline
                        headline="Auftragsverarbeitungsvertrag akzeptieren"
                />

                <div class="mt-2">
                    <p class="leading-5">
                        Die aktuelle Version des Auftragsverarbeitungsvertrags finden Sie hier:
                    </p>
                </div>

                <div class="mt-2">
                    <p class="leading-5">
                        <x-link href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                                target="_blank">
                            Auftragsverarbeitungsvertrag
                        </x-link>
                    </p>
                </div>

                <div class="mt-3">
                    <p class="leading-5">
                        Die GEDISA und die von ihr beauftragten Unterauftragnehmer erbringen die Dienstleistungen
                        des
                        Apothekenportals im Rahmen einer Auftragsverarbeitung gemäß Artikel 28
                        Datenschutz-Grundverordnung (“DSGVO”). Die GEDISA agiert hierbei als Auftragsverarbeiter für
                        die
                        datenschutzrechtlich verantwortlichen Apotheken. Der gesetzlich vorgeschriebene
                        Auftragsverarbeitungsvertrag, welcher zwischen der GEDISA und den Apotheken zu schließen
                        ist,
                        wird über das GEDISA ApothekenPortal bereitgestellt. Das erforderliche Einverständnis der
                        verantwortlichen Apotheken mit diesem Vertrag ist durch Anklicken des entsprechenden Feldes
                        zu
                        erklären. Für die apothekeninterne Dokumentation kann der GEDISA-seitig bereits
                        unterschriebene
                        <x-link
                                href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                                target="_blank">Vertrag
                        </x-link>
                        über das GEDISA
                        ApothekenPortalAuftragsverarbeitungsvertrags
                        heruntergeladen werden.
                    </p>
                </div>

                <div class="mt-2">
                    <ul class="list-disc pl-5">
                        <li>
                            Der Vertrag sollte von der Apotheke aufbewahrt werden.
                        </li>
                        <li>
                            Die Übersendung einer seitens der Apotheke unterzeichneten Fassung an die GEDISA ist
                            nicht
                            erforderlich.
                        </li>
                    </ul>
                </div>

                <div class="mt-4">
                    <x:input.checkbox
                            wire:model.live="avvCheckboxAccepted"
                            id="data-processing-contract-checkbox"
                            :error="$errors->first('avvCheckboxAccepted')"
                    >
                        Ich erkläre mein Einverständnis mit dem
                        Auftragsverarbeitungsvertrag
                        zwischen der
                        GEDISA
                        und mir.
                    </x:input.checkbox>
                </div>
            </div>

            @if ($this->hasToAcceptAssociationFrameworkContractDiscount)
                <div>
                    <x:subscriptions.section-headline
                            headline="Verbandspaket"
                    />

                    <div class="mt-4">
                        <x:input.checkbox
                                wire:model.live="frameworkContractAccepted"
                                id="data-processing-framework-contract-checkbox"
                                :error="$errors->first('frameworkContractAccepted')"
                        >
                            Ihre Mitgliedschaft ist
                            bis {{ $pharmacy->owner()->pharmacyProfile->association->frameworkContractEnd()->format('d.m.Y') }}
                            durch Ihren Verbandsbeitrag abgedeckt. Nach Ablauf des Vertrags Ihres Verbands
                            (Rahmenvertrag) mit der GEDISA oder im Falle eines Austritts aus Ihrem Verband sind Sie
                            verpflichtet, die monatliche Gebühr
                            von {{ \Illuminate\Support\Number::currency(\App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct::make()->getOneTimeStripePrice($pharmacy)->price / 100, 'EUR') }}
                            für eine Basismitgliedschaft selbst zu zahlen. Zusätzliche gewünschte Leistungen können dann
                            als Optionen hinzu gebucht werden.
                        </x:input.checkbox>
                    </div>
                </div>
            @endif

            <form wire:submit="acceptTerms">
                <div class="mt-4 flex justify-end">
                    <x:button type="submit">Nutzungsbedingungen und Auftragsverarbeitungsvertrag akzeptieren
                    </x:button>
                </div>
            </form>
        @endif

    @endcan
    @cannot('updateTos', \App\Setting::class)
        @if($this->tosAccepted)
            <x:alert type="success" class="mt-6" title='Nutzungsbedingungen zugestimmt'>
                <x-slot name="description">
                    <p>
                        Ihr/e Inhaber/in der Apotheke hat den neuen Nutzungsbestimmungen und dem
                        Auftragsverarbeitungsvertrag zugestimmt.
                    </p>
                </x-slot>
            </x:alert>
        @else
            <x:alert type="warning" class="mt-6"
                     title='Nutzungsbedingungen und Auftragsverarbeitungsvertrag zustimmen'>
                <x-slot name="description">
                    <p>
                        Eine Nutzung des Apothekenportals ist nur noch dann möglich, wenn die neuen
                        Nutzungsbedingungen
                        und der Auftragsverarbeitungsvertrag akzeptiert wurden.
                    </p>

                    @if(now() < app(\App\Settings\TermsOfServiceSettings::class)->new_terms_of_service_deadline)
                        <p class="mt-1">
                            Die Bestätigung MUSS durch den Apothekeninhaber bzw. die -inhaberin bis zum <span
                                    class="font-medium">{{ app(\App\Settings\TermsOfServiceSettings::class)->new_terms_of_service_deadline->format('d.m.Y') }}</span>
                            erfolgen.
                        </p>
                    @else
                        <p class="mt-1">
                            Die Bestätigung MUSS, seit dem <span
                                    class="font-medium">{{ app(\App\Settings\TermsOfServiceSettings::class)->new_terms_of_service_deadline->format('d.m.Y') }}</span>,
                            durch den Apothekeninhaber bzw. die -inhaberin erfolgen.
                        </p>
                    @endif
                </x-slot>
            </x:alert>
        @endif
    @endcannot
</div>
