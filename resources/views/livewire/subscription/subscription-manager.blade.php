<form x-data="{ buttonDisabled: @entangle('form.buttonDisabled') }" @submit.prevent="$wire.save; buttonDisabled = true">
    <x-panel class="p-6 space-y-6">
        @if($successBannerType === \App\Domains\Subscription\Domain\Enums\SubscriptionStateEnum::Started->value)
            <x-alert type="success" class="mb-4" title="Mitgliedschaft erfolgreich gebucht">
                <x-slot:description>
                    Ihre Mitgliedschaft wurde erfolgreich aktiviert. In Kürze erhalten Sie eine Bestätigung sowie Ihre
                    erste Rechnung per E-Mail.
                </x-slot:description>
            </x-alert>
        @elseif($successBannerType === \App\Domains\Subscription\Domain\Enums\SubscriptionStateEnum::Updated->value)
            <x-alert type="success" class="mb-4" title="Mitgliedschaft erfolgreich geändert">
                <x-slot:description>
                    Ihre Mitgliedschaft wurde erfolgreich aktualisiert.
                </x-slot:description>
            </x-alert>
        @endif

        <div>
            <p class="text-lg font-semibold">Mitgliedschaft {{ !$pharmacy->isSubscribedToProduct(\App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct::class) ? 'buchen' : '' }}</p>

            <p class="text-sm text-gray-500">
                Buchen Sie ab sofort Ihr individuelles Produktpaket für Ihre Apotheke {{ $pharmacy->name }}.
                Die Basismitgliedschaft erhalten Sie für nur 25 € monatlich – weitere Funktionen können jederzeit
                flexibel hinzugebucht werden.
            </p>
        </div>


        @if ($pharmacy->isSubscribedToProduct(\App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct::class))
            <div class="md:flex md:space-x-4 space-y-4 md:space-y-0">
                <div class="p-4 border border-gray-300 rounded-lg w-full">
                    <div class="flex justify-between">
                        <div class="flex flex-wrap -ml-2">
                            <div class="pl-2">
                            <p class="font-semibold">{{ $subscribedBaseProduct->text?->name }}</p>
                            </div>
                            <div class="flex space-x-2 pl-2">
                                @if($this->hasFrameworkContract())
                                    <flux:badge size="sm" class="tooltip"
                                                data-tippy-content="{!! $this->frameworkContractFeatureTooltipText() !!}
                                                "
                                                color="red">{{ $this->frameworkContract() instanceof \App\Domains\Subscription\Application\AssociationFrameworkContracts\PlusAssociationFrameworkContract ? 'Plus' : 'Basis' }}</flux:badge>
                                @endif
                                <x-badge color="brand">Aktiv</x-badge>
                            </div>
                        </div>
                        <div>
                            <p class="text-sm"><span
                                        class="font-semibold text-base">{{ \Illuminate\Support\Number::currency((max(0, $subscribedBaseProduct->price + $this->subscribedAddonsPrice - $this->frameworkDiscount)) / 100, 'EUR', 'de') }}</span>
                                pro Monat</p>
                        </div>
                    </div>

                    <div class="mt-6 text-sm leading-6">
                        <p class="font-semibold">Gesamtpreis</p>
                        <p>{{ \Illuminate\Support\Number::currency($subscribedBaseProduct->price / 100, 'EUR', 'de') }}
                            Basis</p>
                        @if ($this->hasFrameworkContract())
                            <p>
                                - {{ \Illuminate\Support\Number::currency($this->frameworkDiscount / 100, 'EUR', 'de') }}
                                Verbandspaket
                            </p>
                        @endif
                        @if ($this->subscribedAddonsPrice > 0)
                            <p>
                                + {{ \Illuminate\Support\Number::currency($this->subscribedAddonsPrice / 100, 'EUR', 'de') }}
                                Zusatzoptionen</p>
                        @endif
                    </div>
                </div>
                <div class="p-4 border border-gray-300 rounded-lg w-full">
                    <p class="font-semibold">Zahlungsinformationen</p>

                    <div class="p-4 border rounded-lg w-full mt-4 border-gray-300">
                        <div class="flex space-x-2 justify-between items-center">
                            <div class="border p-1.5 border-gray-200 rounded-md flex justify-center">
                                @if ($pharmacy->selected_pm_type === \App\Enums\PaymentMethod::SEPA)
                                    <x-svg-icon iconId="sepa" class="size-8"/>
                                @else
                                    <x-svg-icon iconId="pay-later" class="size-8"/>
                                @endif
                            </div>
                            <div class="text-xs w-full">
                                @if ($pharmacy->selected_pm_type === \App\Enums\PaymentMethod::SEPA)
                                    <p class="font-semibold">SEPA Lastschrift</p>
                                @else
                                    <p class="font-semibold">Auf Rechnung</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <!-- AP-2492: das variiert je nach Rahmenvertrag glaube ich -->
            <div class="grid {{ $this->hasPlusAssociationFrameworkContract() ? 'sm:grid-cols-2' : '' }} gap-4">
                <x-pricing-card heading="Basis" name="membership"
                                :variant="$this->hasPlusAssociationFrameworkContract() ? 'muted' : 'primary'"
                                :selectable="$this->hasFrameworkContract()">
                    <ul>
                        @foreach ((new \App\Domains\Subscription\Application\AssociationFrameworkContracts\BaseAssociationFrameworkContract)->getPublicRepresentationData($pharmacy)->text->features as $feature => $description)
                            <li class="mb-1 flex items-center gap-1.5">
                                <flux:icon.check-circle variant="solid" class="size-4 text-brand-600 mt-0.5"/>
                                <flux:tooltip content="{{ $description }}">
                                    <p class="text-sm font-medium text-brand-600 border-b-2 border-dotted border-gray-300 cursor-pointer">{{ $feature }}</p>
                                </flux:tooltip>
                            </li>
                        @endforeach
                    </ul>

                    @if ($this->hasFrameworkContract() && !$this->hasPlusAssociationFrameworkContract())
                        <x-subscription.association-discount-info
                                :association="$pharmacy->ownerOrFail()?->pharmacyProfile?->association" class="mt-3"/>
                    @endif
                </x-pricing-card>

                @if ($this->hasPlusAssociationFrameworkContract())
                    <x-pricing-card heading="Verband Plus" name="membership"
                                    :variant="$this->hasPlusAssociationFrameworkContract() ? 'primary' : 'muted'"
                                    :selectable="$this->hasFrameworkContract()">
                        <ul>
                            <li class="text-sm font-medium text-brand-600 mb-2">Alle Funktionen aus dem Basispaket
                                UND:
                            </li>
                            @foreach ((new \App\Domains\Subscription\Application\AssociationFrameworkContracts\PlusAssociationFrameworkContract)->getPublicRepresentationData($pharmacy)->text->features as $feature => $description)
                                <li class="flex items-center gap-1.5">
                                    <flux:icon.check-circle variant="solid" class="size-4 text-brand-600 mt-0.5"/>
                                    <flux:tooltip content="{{ $description }}">
                                        <p class="text-sm font-medium text-brand-600 border-b-2 border-dotted border-gray-300 cursor-pointer">{{ $feature }}</p>
                                    </flux:tooltip>
                                </li>
                            @endforeach
                        </ul>

                        @if ($this->hasFrameworkContract() && $this->hasPlusAssociationFrameworkContract())
                            <x-subscription.association-discount-info
                                    :association="$pharmacy->ownerOrFail()?->pharmacyProfile?->association"
                                    class="mt-3"/>
                        @endif
                    </x-pricing-card>
                @endif
            </div>
        @endif

        <!-- AP-2493 -->
        <div x-data="{ open: @js($this->isUpdate) }">
            @if(! $this->isUpdate)
                <div class="font-semibold text-brand-600 flex items-center gap-2 cursor-pointer"
                     x-on:click="open = !open">
                    <flux:icon.plus x-show="! open" class="size-6"/>
                    <flux:icon.minus x-show="open" class="size-6"/>
                    <p>Zusätzliche Optionen buchen</p>
                </div>
            @endif

            <div x-show="open" x-cloak class="mt-6 space-y-3">
                @if (count($this->frameworkContract->getAvailableAddons()) > 0)
                    <div>
                        @if($this->isUpdate)
                            <p class="font-semibold">Buchen Sie zusätzliche Features</p>
                            <p class="text-sm text-gray-500">Passen Sie die Leistungen Ihrer Mitgliedschaft ganz einfach
                                Ihren individuellen Bedürfnissen an. Wählen Sie aus einer Vielzahl zusätzlicher
                                Funktionen, die individuell auf die Anforderungen Ihrer Apotheke abgestimmt sind.</p>
                        @else
                            <p class="font-semibold">Upgraden Sie Ihr Paket für mehr Flexibilität</p>
                            <p class="text-sm text-gray-500">Erweitern Sie Ihr Paket ganz nach Ihren Wünschen, mit einer
                                Auswahl an zusätzlichen Optionen, die individuell auf Ihre Bedürfnisse abgestimmt werden
                                konnen.</p>
                        @endif
                    </div>

                    @foreach ($this->frameworkContract->getAvailableAddons() as $addon)
                        <x-pricing-card
                                heading="{{ $addon::make()->getPublicRepresentationData($pharmacy)->text->name }}"
                                subheading="{{ $addon::make()->getPublicRepresentationData($pharmacy)->displayPrice() }} pro Monat"
                                collapsable
                                :collapsed="$loop->first === false"
                                :disabled="$addon::make()->isRequired($pharmacy)"
                                name="{{ $addon::make()->uniqueIdentifier() }}"
                                toggleable
                                wire:model.live="form.addons.{{ $addon::make()->uniqueIdentifier() }}"
                        >
                            <p class="text-sm text-gray-700">{{ $addon::make()->getPublicRepresentationData($pharmacy)->text->description }}</p>
                        </x-pricing-card>
                    @endforeach
                @endif

                <div class="mt-6 space-y-3">
                    @if(! $this->isUpdate)
                        <div>
                            <p class="font-semibold">Weitere buchbare Optionen</p>
                            <p class="text-sm text-gray-500">Nach Abschluss Ihrer Mitgliedschaft können Sie Ihr Paket
                                ganz nach Ihren Wünschen erweitern, indem Sie aus einer Vielzahl von zusätzlichen
                                Optionen wählen, die speziell auf Ihre Bedürfnisse zugeschnitten sind.</p>
                        </div>
                    @endif

                    @foreach($this->form->additionalAddons as $addon => $value)
                        @php
                            $addonClass = app(\App\Domains\Subscription\Application\Helper\StripeProductResolver::class)->fromUniqueIdentifier($addon);
                        @endphp
                        <x-pricing-card
                                heading="{{ $addonClass->make()->getPublicRepresentationData($pharmacy)->text->name }}"
                                subheading="{{ $addonClass->make()->getPublicRepresentationData($pharmacy)->displayPrice() }} pro Monat"
                                collapsable
                                :collapsed="true"
                                :toggleable="data_get($form->additionalAddons, $addon)"
                                disabled
                                :bookingUrl="$pharmacy->isSubscribedToProduct(\App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct::class) ? $addonClass->make()->getPublicRepresentationData($pharmacy)->bookingUrl?->url : ''"
                                :bookingText="$pharmacy->isSubscribedToProduct(\App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct::class) ? $addonClass->make()->getPublicRepresentationData($pharmacy)->bookingUrl?->text : ''"
                                name="{{ $addonClass->make()->uniqueIdentifier() }}"
                                wire:model.live="form.additionalAddons.{{ $addon }}"
                        >
                            <p class="text-sm text-gray-700">{{ $addonClass->make()->getPublicRepresentationData($pharmacy)->text->description }}</p>
                        </x-pricing-card>
                    @endforeach
                </div>
            </div>
        </div>

        @if(! $this->isUpdate || $this->hasSelectedUpdate)
            <!-- AP-2493 -->
            <div class="p-4 border border-gray-300 rounded-lg" wire:key="preisliste">
                <p class="font-semibold">Preisübersicht</p>
                <div class="mt-3 space-y-1 text-sm" wire:key="preisliste-items">
                    <div wire:key="basisprodukt" class="flex items-center justify-between gap-4 text-gray-500">
                        <p>{{ $selectedBaseProduct->text->name }}</p>
                        <p>{{ $selectedBaseProduct->displayPrice() }} pro
                            Monat</p>
                    </div>

                    @foreach($this->selectedAddons as $addon)
                        <div wire:key="class-{{ $addon->text->name }}" class="flex items-center justify-between gap-4 text-gray-500">
                            <p>{{ $this->isAddition($addon->class) ? '+ ' : '' }}{{ $addon->text->name }}</p>
                            <p style="font-variant-ligatures: none">{{ $this->isAddition($addon->class) ? '+ ' : '' }} {{ $this->getPriceListCount($addon->class::make()) > 1 ? $this->getPriceListCount($addon->class::make()) . ' x ' : '' }}{{ $addon->displayPrice() }} pro
                                Monat</p>
                        </div>
                    @endforeach

                    @php($kimProduct = \App\Domains\Subscription\Application\StripeProducts\AddOns\KimProduct::make())
                    <div>
                    @if ($this->kimCount > 0 && ! $this->selectedAddons->contains('class', $kimProduct::class))
                        <div wire:key="kim-product" class="flex items-center justify-between gap-4 text-gray-500">
                            <p wire:key="kim-name">{{ $kimProduct->getPublicRepresentationData($pharmacy)->text?->name }}</p>
                            <p wire:key="kim-price" style="font-variant-ligatures: none">{{ $this->getPriceListCount($kimProduct) > 1 ? $this->getPriceListCount($kimProduct) . ' x ' : '' }}{{ $kimProduct->getPublicRepresentationData($pharmacy)->displayPrice(false) }}
                                pro Monat</p>
                        </div>
                    @endif
                    </div>

                    <div>
                    @if ($this->hasFrameworkContract())
                        <div wire:key="verbandsrabatt" class="flex items-center justify-between gap-4 text-gray-500">
                            <p>Verbandspaket</p>
                            <p>{{ \Illuminate\Support\Number::currency(-1 * $this->frameworkDiscount / 100, 'EUR', 'de') }}
                                pro Monat</p>
                        </div>
                    @endif
                    </div>
                </div>

                <hr class="my-3"/>

                <div class="space-y-1 text-sm">
                    <div class="flex items-center justify-between gap-4">
                        <p class="font-semibold">Gesamt</p>
                        @if ($this->hasFrameworkContract())
                            <p>{{ \Illuminate\Support\Number::currency(($this->totalSum - $this->frameworkDiscount) / 100, 'EUR', 'de') }}
                                pro Monat</p>
                        @else
                            <p>{{ \Illuminate\Support\Number::currency($this->totalSum / 100, 'EUR', 'de') }} pro
                                Monat</p>
                        @endif
                    </div>

                    <div class="flex justify-between gap-4 text-gray-500 w-full text-xs">
                        @if ($this->hasFrameworkContract())
                            <p class="text-red-400 w-3/4">ACHTUNG: Nach Ablauf des Vertrags zugunsten Dritter
                                (Rahmenvertrag) Ihres Verbands mit der GEDISA oder im Falle eines Austritts aus Ihrem
                                Verband wechseln Sie automatisch in eine Basismitgliedschaft und sind verpflichtet, die
                                monatliche Gebühr
                                von {{ \Illuminate\Support\Number::currency(\App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct::make()->getOneTimeStripePrice($pharmacy)->price / 100, 'EUR') }}
                                selbst zu zahlen. Zusätzliche gewünschte Leistungen können dann als Optionen
                                hinzugebucht werden.</p>
                        @endif
                        <div @class([
                            'text-right',
                            'w-1/4' => $this->hasFrameworkContract(),
                            'w-full' => !$this->hasFrameworkContract(),
                         ])>
                            @if($pharmacy->has_vat)
                                <p>zuzüglich gesetzlicher Mwst. (19%)</p>
                            @endif
                            <p>quartalsweise Abrechnung</p>
                        </div>
                    </div>
                </div>
            </div>


            <div x-data="{openPaymentMethods: @js($this->isUpdate)}">
                @if (! $this->isUpdate)
                    <div x-show="!openPaymentMethods">
                        <div class="p-4 text-sm border border-gray-300 rounded-lg flex items-center gap-4">
                            <div class="p-2 border border-gray-300 rounded-lg">
                                <flux:icon.information-circle class="text-gray-500 size-5"/>
                            </div>
                            <div>
                                <p class="font-semibold">Hinweis: Zahlungsdaten</p>
                                <p class="text-gray-500">
                                    Im nächsten Schritt werden Sie dazu aufgefordert, Ihre Zahlungsdaten einzugeben.
                                    Bitte geben Sie diese auch dann an, wenn die Kosten durch Ihren Verband abgedeckt
                                    werden. Dies ist erforderlich, um zukünftige Buchungen von Zusatzleistungen oder den
                                    Fall eines Endes des Vertrags Ihres Verbands zugunsten Dritter (Rahmenvertrag) zu
                                    berücksichtigen. Sie können die
                                    Buchung nur dann abschließen, wenn Sie Ihre Zahlungsdaten hinterlegt haben.
                                </p>
                            </div>
                        </div>
                        <div class="flex justify-end mt-5">
                            <x-button appearance="brand" type="button" @click="openPaymentMethods = true">
                                Buchungsprozess fortsetzen
                            </x-button>
                        </div>
                    </div>
                    <div x-show="openPaymentMethods">
                        <p class="font-semibold">
                            Zahlungsart
                        </p>

                        <div class="mt-3 space-y-2">
                            <x-subscription.select-box
                                :selected="$form->selectedPaymentMethod === \App\Enums\PaymentMethod::Invoice">
                                {{ \App\Enums\PaymentMethod::Invoice->label() }}
                                <x-slot name="icon">
                                    <x-svg-icon iconId="pay-later" class="size-8"/>
                                </x-slot>
                            </x-subscription.select-box>

                            @error('form.selectedPaymentMethod')
                            <div class="text-red-500 text-sm mt-2">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                @endif

                <!-- AP-2495 -->
                <div x-show="openPaymentMethods">
                    <div class="mt-4">
                        <p class="font-semibold">Rechnungsadresse</p>

                        <div class="mt-3 border border-gray-300 rounded-lg p-4 text-sm">
                            @if ($form->billingAddress)
                                <p>{{ $form->billingAddress->email }}</p>
                                <p>{{ $form->billingAddress->company }}</p>
                                <p>{{ $form->billingAddress->first_name . ' ' . $form->billingAddress->last_name }}</p>
                                <p>{{ $form->billingAddress->optional_address_line }}</p>
                                <p>{{ $form->billingAddress->street . ' ' . $form->billingAddress->house_number }}</p>
                                <p>{{ $form->billingAddress->postal_code . ' ' . $form->billingAddress->city }}</p>
                                <p>{{ $form->billingAddress->phone }}</p>
                            @else
                                <p class="italic">Bitte fügen Sie eine Rechnungsadresse hinzu.</p>
                            @endif

                            <div class="mt-3">
                                <a href="{{ route('users.billing-addresses') }}?backlink=true" class="font-semibold">Rechnungsadresse
                                    ändern</a>
                            </div>
                        </div>
                        @error('form.billingAddress')
                        <div class="mt-2 text-red-500 text-sm">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="my-4 space-y-4">
                        <x-input.checkbox appearance="brand" id="acceptedTerms" wire:model="form.acceptedTerms"
                                          :error="$errors->first('form.acceptedTerms')">
                            Ich habe die <a href="{{ route('uploads.show', 'nutzungsbedingungen-apothekenportal') }}"
                                            class="underline">Nutzungsbedingungen
                                mit Widerrufsbelehrung</a> und die
                            <a href="{{ route('uploads.show', 'datenschutzerklaerung-apothekenportal') }}"
                               class="underline">Datenschutzbestimmungen</a>
                            gelesen und akzeptiere diese.
                        </x-input.checkbox>
                    </div>

                    <x-input.checkbox appearance="brand" id="acceptedDataContract"
                                      wire:model="form.acceptedDataContract"
                                      :error="$errors->first('form.acceptedDataContract')">
                        Ich habe den <a
                                href="{{ route('uploads.show', 'auftragsverarbeitungsvertrag-apothekenportal') }}"
                                class="underline">Auftragsverarbeitungsvertrag</a> gelesen und akzeptiere diesen.
                    </x-input.checkbox>

                    <div class="flex items-center justify-end">
                        <x-button appearance="brand" type="submit" x-bind:disabled="buttonDisabled">
                            @if ($pharmacy->isSubscribedToProduct(\App\Domains\Subscription\Application\StripeProducts\Base\BaseStripeProduct::class))
                                Änderungen kostenpflichtig bestätigen
                            @else
                                Kostenpflichtig buchen
                            @endif
                        </x-button>
                    </div>
                </div>
            </div>
        @endif

            @if($error)
                <x-alert type="error" title="Fehler bei der Buchung">
                    <x-slot:description>
                        {{ $error }}
                    </x-slot:description>
                </x-alert>
            @endif
    </x-panel>

</form>
