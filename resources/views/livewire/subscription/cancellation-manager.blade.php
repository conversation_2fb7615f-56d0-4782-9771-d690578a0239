<div class="space-y-6">

    {{-- Cancellation Request Section --}}
    @if (!$cancellation && $subscribedBaseProduct)
        <x-panel class="p-6 space-y-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ $subscribedBaseProduct->text?->name }} kündigen</h3>
            <x-alert type="info" class="mb-6" title="Wichtige Informationen zur Kündigung">
                <x-slot:description>
                    <ul class="list-disc list-inside space-y-1">
                        <li><strong>Mindestvertragslaufzeit:</strong> 3 Monate</li>
                        <li><strong>Kündigungsfrist:</strong> 1 Monat zum Quartalsende</li>
                        <li><strong>Nächstmögliches Kündigungsdatum:</strong> {{ $pharmacy->calculateNextQuarterEnd()->format('d.m.Y') }}</li>
                        <li>Nach der Kündigung können keine neuen Features aktiviert werden</li>
                    </ul>
                </x-slot:description>
            </x-alert>

            <div class="mt-4 flex justify-end">
                <flux:modal.trigger name="cancel-subscription">
                    <x-button>
                        {{ $subscribedBaseProduct->text?->name }} kündigen
                    </x-button>
                </flux:modal.trigger>
            </div>
        </x-panel>

        <flux:modal name="cancel-subscription">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">Kündigung bestätigen</flux:heading>
                    <flux:text class="mt-2">
                        Sind Sie sicher, dass Sie Ihre Basismitgliedschaft kündigen möchten?
                        Die Kündigung wird zum {{ $pharmacy->calculateNextQuarterEnd()->format('d.m.Y') }} wirksam.
                    </flux:text>
                </div>


                <div>
                    <x:input.textarea
                        label="Grund (optional)"
                        name="text"
                        wire:model.blur="cancellationReason"
                        placeholder="Warum möchten Sie kündigen?"
                        rows="4"
                        :error="$errors->first('cancellationReason')"
                    />
                </div>

                <div class="mt-4">
                    <x:input.checkbox id="confirmCancellation" wire:model.live="confirmCancellation"
                                      :error="$errors->first('confirmCancellation')">
                        Ich bestätige, dass ich meine {{ $subscribedBaseProduct->text?->name }} zum {{ $pharmacy->calculateNextQuarterEnd()->format('d.m.Y') }} kündigen möchte.
                        Mir ist bewusst, dass nach der Kündigung keine neuen Features aktiviert werden können.
                    </x:input.checkbox>
                </div>

                @error('cancellation')
                <div class="mt-4 bg-red-50 border border-red-200 rounded-md p-3">
                    <p class="text-sm text-red-600">{{ $message }}</p>
                </div>
                @enderror


                <div class="flex space-x-2 justify-end">
                    <flux:modal.close>
                        <x-button appearance="secondary">Abbrechen</x-button>
                    </flux:modal.close>

                    <x-button wire:click="requestCancellation">Kündigung bestätigen</x-button>
                </div>
            </div>
        </flux:modal>
    @endif

    {{-- Current Cancellation Status --}}
    @if ($cancellation)
        <x-alert type="warning">
            <x-slot:title>
                Kündigung {{ $cancellation->status->label() }}
            </x-slot:title>

            <x-slot:description>
                <p>{{ $cancellation->status->description() }}</p>
                <p class="mt-1">
                    <strong>Kündigungsdatum:</strong> {{ $cancellation->effective_date->format('d.m.Y') }}
                </p>
                @if ($cancellation->reason)
                    <p class="mt-1">
                        <strong>Grund:</strong> {{ $cancellation->reason }}
                    </p>
                @endif
                <div class="mt-4 flex">
                    <flux:modal.trigger name="revoke-cancellation">
                        <x-button>
                            Kündigung widerrufen
                        </x-button>
                    </flux:modal.trigger>
                </div>
            </x-slot:description>
        </x-alert>

        <flux:modal name="revoke-cancellation">
            <div class="space-y-6">
                <div>
                    <flux:heading size="lg">Kündigung widerrufen</flux:heading>
                    <flux:text class="mt-2">
                        Möchten Sie Ihre Kündigung widerrufen? Ihre Basismitgliedschaft wird dann fortgesetzt.
                    </flux:text>
                </div>


                <div class="mt-4">
                    <x:input.checkbox id="confirmRevoke" wire:model.live="confirmRevoke"
                                      :error="$errors->first('confirmRevoke')">
                        Ich bestätige, dass ich meine Kündigung widerrufen möchte und meine Basismitgliedschaft fortsetzen will.
                    </x:input.checkbox>
                </div>

                @error('revoke')
                <div class="mt-4 bg-red-50 border border-red-200 rounded-md p-3">
                    <p class="text-sm text-red-600">{{ $message }}</p>
                </div>
                @enderror


                <div class="flex space-x-2 justify-end">
                    <flux:modal.close>
                        <x-button appearance="secondary">Abbrechen</x-button>
                    </flux:modal.close>

                    <x-button wire:click="revokeCancellation">Widerruf bestätigen</x-button>
                </div>
            </div>
        </flux:modal>
    @endif

</div>
