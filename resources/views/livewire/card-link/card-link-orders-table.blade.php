<div>
    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
                <h1 class="text-base font-semibold leading-6 text-gray-900">
                    {{ __('CardLink-Bestellungen') }}
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                    {{ __('Hier finden Sie eine Übersicht Ihrer CardLink-Bestellungen.') }}
                </p>
        </div>
    </div>

    @if ($cardLinkOrders->isEmpty())
        <div class="mt-8">
            <x-alert type="info">
                <x-slot name="title">{{ __('Keine Bestellungen vorhanden') }}</x-slot>
                <x-slot name="description">{{ __('Sie haben noch keine Bestellung getätigt.') }}</x-slot>
            </x-alert>
        </div>
    @else
        <x:stackedList class="mt-2">
            <x:stackedListHead caret>
                <div class="w-full md:w-1/5 lg:w-4/12">Apotheke</div>
                <div class="w-full md:w-1/5 lg:w-2/12">Status</div>
                <div class="w-full md:w-1/5 lg:w-2/12">Reserviert am</div>
                <div class="w-full md:w-1/5 lg:w-2/12">Bestellt am</div>
                <div class="w-full md:w-1/5 lg:w-2/12">Aktiviert am</div>
            </x:stackedListHead>

            <x:stackedListBody>
                @foreach ($cardLinkOrders as $cardLinkOrder)
                    <x:stackedListRow :href="route('card-link', $cardLinkOrder->pharmacy)" caret>
                        <div class="w-full md:w-1/5 lg:w-4/12 text-sm">
                            {{ $cardLinkOrder->pharmacy->name }}
                        </div>

                        <div class="text-sm text-gray-500 md:w-1/5 lg:w-2/12 md:pt-0 flex flex-wrap items-center gap-2">
                            <x-badge color="{{ $cardLinkOrder->status->color() }}">{{ $cardLinkOrder->status->label() }}</x-badge>
                        </div>
                        <div class="text-sm text-gray-500 md:w-1/5 lg:w-2/12 md:pt-0 flex flex-wrap items-center gap-2">
                            <p>{{ $cardLinkOrder->reserved_at?->format('d.m.Y') ?? '-' }}</p>
                        </div>
                        <div class="text-sm text-gray-500 md:w-1/5 lg:w-2/12 md:pt-0 flex flex-wrap items-center gap-2">
                            <p>{{ $cardLinkOrder->ordered_at?->format('d.m.Y') ?? '-' }}</p>
                        </div>
                        <div class="text-sm text-gray-500 md:w-1/5 lg:w-2/12 md:pt-0 flex flex-wrap items-center gap-2">
                            <p>{{ $cardLinkOrder->activated_at?->format('d.m.Y') ?? '-' }}</p>
                        </div>
                    </x:stackedListRow>
                @endforeach
            </x:stackedListBody>
        </x:stackedList>
    @endif

</div>
