<div class="flex flex-wrap -m-4">
    <div class="w-full p-4">
        @if ($this->newChangeEmailSend)
            <x-alert class="mb-8" description="Eine E-Mail mit einem neuen Bestätigungs-Link wurde verschickt." />
        @endif

        @if ($this->currentlyChangingEmail)
            <div class="bg-white shadow rounded-md mb-8">
                <div class="py-8 px-4 sm:px-10">
                    <div class="sm:flex sm:items-start sm:justify-between">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                E-Mail Adressen Änderung
                            </h3>
                            <div class="mt-2 max-w-xl text-sm leading-5 text-gray-500">
                                <p>
                                    Sie haben die Änderung Ihrer E-Mail-Adresse für Benachrichtigungen angefordert.
                                    Damit die neue E-Mail-Adresse mit Ihrem Account verbunden werden kann, müssen Sie
                                    den Link klicken, den wir Ihnen zu Ihrer neuen E-Mail-Adresse
                                    ({{ $this->currentlyChangingEmail }}) zukommen lassen haben.
                                </p>
                            </div>
                        </div>
                        <div class="mt-5 sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:flex sm:items-center">
                            <form method="POST" action="{{ route('user.email.resend') }}">
                                @csrf
                                <span class="inline-flex rounded-md shadow-sm">
                            <button type="submit"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-red-600 hover:bg-red-500 focus:outline-none focus:border-red-700 focus:shadow-outline-indigo active:bg-red-700 transition ease-in-out duration-150">
                                E-Mail erneut senden
                            </button>
                        </span>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if ($errors->any())
            <div class="w-full mb-8">
                <x-alert-validation-error />
            </div>
        @endif

        <form wire:submit="save">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        @if ($user->isCompany())
                            <x-input.text
                                :error="$errors->first('name')"
                                :label="__('validation.attributes.name')"
                                type="text"
                                wire:model="form.name"
                            />
                        @endif

                        {{-- Salutation --}}
                        <x-input.select
                            :error="$errors->first('salutation')"
                            :label="__('validation.attributes.salutation')"
                            wire:model="form.salutation"
                        >
                            @foreach(\App\Enums\SalutationEnum::getForDropdown() as $value => $title)
                                <option value="{{ $value }}">
                                    @lang('validation.attributes.' . $title)
                                </option>
                            @endforeach
                        </x-input.select>

                        {{-- Title --}}
                        <x-input.text
                            :error="$errors->first('title')"
                            :label="__('validation.attributes.title')"
                            type="text"
                            wire:model="form.title"
                        />


                        {{-- First Name --}}
                        <x-input.text
                            :error="$errors->first('first_name')"
                            :label="__('validation.attributes.first_name')"
                            type="text"
                            wire:model="form.first_name"
                        />

                        {{-- Last Name --}}
                        <x-input.text
                            :error="$errors->first('last_name')"
                            :label="__('validation.attributes.last_name')"
                            type="text"
                            wire:model="form.last_name"
                        />

                        <div class="sm:col-span-2 grid grid-cols-2 gap-4">
                            {{-- Phone --}}
                            <x-input.text
                                :error="$errors->first('phone')"
                                :label="__('validation.attributes.your_personal_phone')"
                                type="text"
                                wire:model="form.phone"
                            />
                        </div>

                        {{-- E-Mail --}}
                        <x-input.text
                            :error="$errors->first('email')"
                            :label="__('validation.attributes.your_personal_email')"
                            type="email"
                            wire:model="form.email"
                        />

                        <x-input.text
                            :error="$errors->first('email_confirmation')"
                            :label="__('validation.attributes.email_confirmation')"
                            type="email"
                            wire:model="form.email_confirmation"
                        />

                        {{-- Notifications --}}
                        <x-input.checkbox
                            :error="$errors->first('association_news_active')"
                            wire:model="form.association_news_active"
                        >@lang('validation.attributes.association_news_active')</x-input.checkbox>
                    </div>
                </div>
            </div>

            {{-- Submit --}}
            <div class="flex items-center justify-end mt-6">
                <x-button type="submit">
                    @lang('messages.submit')
                </x-button>
            </div>
        </form>
    </div>
</div>
