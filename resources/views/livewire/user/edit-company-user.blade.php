<div>
    <div class="flex flex-wrap -m-4">
        <div class="w-full p-4">
            @if ($errors->any())
                <div class="w-full mb-8">
                    <x:alert-validation-error />
                </div>
            @endif
            <form wire:submit="save">
                <div class="bg-white overflow-hidden shadow rounded-lg px-4 py-5 sm:p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div class="sm:col-span-2 grid grid-cols-2 gap-4">
                            {{-- Name --}}
                            <x-input.text
                                :error="$errors->first('name')"
                                :label="__('validation.attributes.name') . ' (*)'"
                                type="text"
                                wire:model="form.name"
                            />
                        </div>

                        {{-- Salutation, Title --}}
                        <x-input.select
                            :error="$errors->first('salutation')"
                            :label="__('validation.attributes.salutation')"
                            wire:model="form.salutation"
                        >
                            @foreach(\App\Enums\SalutationEnum::getForDropdown() as $value => $title)
                                <option value="{{ $value }}"
                                        @if((old('salutation') ?? $user->salutation) == $value) selected @endif
                                >
                                    @lang('validation.attributes.' . $title)
                                </option>
                            @endforeach
                        </x-input.select>

                        <x-input.text
                            :error="$errors->first('title')"
                            :label="__('validation.attributes.title')"
                            type="text"
                            wire:model="form.title"
                        />

                        {{-- Firstname, Lastname --}}
                        <x-input.text
                            :error="$errors->first('first_name')"
                            :label="__('validation.attributes.first_name') . ' (*)'"
                            type="text"
                            wire:model="form.first_name"
                        />

                        <x-input.text
                            :error="$errors->first('last_name')"
                            :label="__('validation.attributes.last_name') . ' (*)'"
                            type="text"
                            wire:model="form.last_name"
                        />

                        <div class="sm:col-span-2 grid grid-cols-2 gap-4">
                            {{-- Phone --}}
                            <x-input.text
                                :error="$errors->first('phone')"
                                :label="__('validation.attributes.phone') . ' (*)'"
                                type="text"
                                wire:model="form.phone"
                            />
                        </div>

                        {{-- E-Mail --}}
                        @if ($user->email)
                            <x-input.text
                                :error="$errors->first('email')"
                                :label="__('validation.attributes.your_personal_email') . ' (*)'"
                                type="email"
                                wire:model="form.email"
                            />

                            <x-input.text
                                :error="$errors->first('email_confirmation')"
                                :label="__('validation.attributes.email_confirmation') . ' (*)'"
                                type="email"
                                wire:model="form.email_confirmation"
                            />
                        @endif

                        {{-- Submit --}}
                        <div class="mt-6">
                            <x-button type="submit">
                                @lang('messages.submit')
                            </x-button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
