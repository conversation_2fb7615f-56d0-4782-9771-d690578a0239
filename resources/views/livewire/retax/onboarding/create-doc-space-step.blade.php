<x-modal.layout
    :title="$this->title()"
    :subtitle="$this->subtitle()"
    :icon="$this->icon()"
    :next-step="$this->nextStep()"
    :prev-step="$this->prevStep()"
    :step-number="$this->stepNumber()"
    :total-steps="$totalSteps"
    :has-fatal-error="$this->hasFatalError()"
    :fatal-error-message="$this->fatalErrorMessage()"
>
    <x-modal.container class="h-full px-12 py-6 text-justify">
        <h2 class="font-semibold mb-7"><PERSON><PERSON><PERSON> Datenraum (SDR) für Retax aktivieren</h2>

        @if ($processes->count() === 0)
            <p class="mb-4">
                Zwei <span class="underline">sichere</span> Datenräume werden automatisch für Sie eingerichtet, damit Sie Dokumente für Retax speichern können.
            </p>
        @else
            <div wire:poll="refreshProcess">
            @if ($processes->whereIn('status', [\App\Enums\Sdr\GuidedDocspaceProcessStatus::PENDING, \App\Enums\Sdr\GuidedDocspaceProcessStatus::PROCESSING])->count())
                <x-alert type="warning" class="bg-white rounded-md">
                    <x-slot:description>
                        Die automatische Erstellung der Datenräume wird noch ausgeführt. Der Vorgang kann einige Minuten in Anspruch nehmen.
                    </x-slot:description>
                </x-alert>
            @elseif ($processes->whereIn('status', [\App\Enums\Sdr\GuidedDocspaceProcessStatus::FAILED])->count())
                <x-alert type="error" class="bg-white rounded-md">
                    <x-slot:description>
                        Entschuldigung! Die automatische Erstellung der Datenräume ist leider fehlgeschlagen. Bitte wenden Sie sich an den Support.
                    </x-slot:description>
                </x-alert>
            @elseif ($processes->where('status', \App\Enums\Sdr\GuidedDocspaceProcessStatus::COMPLETED)->count())
                <x-alert type="success" class="bg-white rounded-md">
                    <x-slot:description>
                        Die automatische Erstellung der Datenräume ist abgeschlossen. Sollten Sie nicht automatisch weitergeleitet werden, klicken Sie bitte auf den Button <button wire:click="goToNextStep()">"hier"</button>.
                    </x-slot:description>
                </x-alert>
            @endif
            </div>
        @endif

    </x-modal.container>
</x-modal.layout>
