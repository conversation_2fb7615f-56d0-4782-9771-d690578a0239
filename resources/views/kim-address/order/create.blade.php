@extends('layouts.app')

@section('content')
    <x:content>
        <x-header>
            <x-slot name="main">
                @lang('kim.create_title')
            </x-slot>
            <x-slot name="section">
                {{ $pharmacy->name }}
            </x-slot>
            <x-slot name="description">
                @lang('kim.create_description')
            </x-slot>
        </x-header>

        <x-alert class="mb-8" type="warning" :description="__('kim.create_warning_no_change')"/>

        <livewire:is :component="'kim-address.' . strtolower(\App\Helper\KimAddressHelper::getVendor($pharmacy)) . '.order-form'" :pharmacy="$pharmacy" />
    </x:content>
@endsection
