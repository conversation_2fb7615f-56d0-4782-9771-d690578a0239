@extends('layouts.app')

@section('content')
    <x:content>
        <x:layout.container size="max-w-2xl">
            <x:card>
                <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100">
                    <svg class="h-12 w-12 text-green-600"><use href="/icons.svg#check"/></svg>
                </div>

                <div class="text-center">
                    <h3 class="font-bold my-3 text-2xl">Impfung abgeschlossen!</h3>
                </div>

                <div class="text-center text-sm mt-5">
                    @if($vaccination->reasons_to_abort)
                    @else
                        <p>Wir leiten Ihre Impfung an das Robert Koch Institut weiter.</p>
                    @endif
                </div>


                {{-- TODO: Remove when working on AP-1057 --}}
                @unless($vaccination->reasons_to_abort || $certificateUrl === null || isCovidVaccinationCertificateCenterActive() === false)
                    <div class="text-center text-sm mt-5">
                        <p>Möchten Sie nun direkt im Anschluss ein Impfzertifikat ausstellen?</p>
                    </div>

                    <div class="mt-5 text-center">
                        <x-button appearance="secondary" :href="$certificateUrl" target="_blank" size="lg" class="w-72">
                            Ja
                        </x-button>
                    </div>

                    <div class="mt-3 text-center">
                        <x-button :href="route('pharmacies.vaccinate-covid.index', [$pharmacy])" size="lg" class="w-72">
                            Nein
                        </x-button>
                    </div>
                @else
                    <div class="mt-3 text-center">
                        <x-button :href="route('pharmacies.vaccinate-covid.index', [$pharmacy])" size="lg" class="w-72">
                            Zurück zur Übersicht
                        </x-button>
                    </div>
                @endunless
            </x:card>
        </x:layout.container>
    </x:content>
@endsection
