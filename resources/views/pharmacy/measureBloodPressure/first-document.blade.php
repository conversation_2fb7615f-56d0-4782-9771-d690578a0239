@extends('layouts.app')

@section('content')
    <x:content>
        @if ($pharmaceuticalService['step'] > 2)
            @include('pharmacy.measureBloodPressure.partials.subnavbar', [
                'currentStep' => 2,
                'savedStep' => $pharmaceuticalService['step'],
                'backLink' => route('pharmacies.pharmaceutical-services.measure-blood-pressures.personal-data', [$pharmaceuticalService->pharmacy, $pharmaceuticalService]),
                'forwardLink' => route('pharmacies.pharmaceutical-services.measure-blood-pressures.acquisition-data', [$pharmaceuticalService->pharmacy, $pharmaceuticalService]),
            ])
        @else
            @include('pharmacy.measureBloodPressure.partials.subnavbar', [
                'currentStep' => 2,
                'savedStep' => $pharmaceuticalService['step'],
                'backLink' => route('pharmacies.pharmaceutical-services.measure-blood-pressures.personal-data', [$pharmaceuticalService->pharmacy, $pharmaceuticalService]),
            ])
        @endif
        <x:measureBloodPressure.headline>
            Vereinbarung
        </x:measureBloodPressure.headline>

        <x:layout.container size="max-w-2xl">
            <form method="POST">
                @csrf
                <div x-data="{downloaded: @js(old('downloaded', $accepted))}">
                    <x:card>
                        <x:row>
                            <x:col>
                                <x:alert type="info" title="Vereinbarung">
                                    <x:slot name="description">
                                        Zur Inanspruchnahme der pharmazeutischen Dienstleistung (pDL) „Standardisierte
                                        Risikoerfassung hoher Blutdruck“ muss zwischen dem/der Versicherten und der
                                        Apotheke eine Vereinbarung geschlossen werden.<br/>
                                        Neben der Unterzeichnung der Vereinbarung ist der Erhalt der erbrachten
                                        Dienstleistung durch den Versicherten/die Versicherte auf dieser Vereinbarung zu
                                        quittieren. Da sich die/der Versicherte bezüglich <u>dieser</u> pDL an die
                                        Vertragsapotheke bindet, reicht bei erneuter Erbringung dieser pDL eine weitere
                                        Quittierung des Erhalts und die Bestätigung der Anspruchsvoraussetzungen. Diese
                                        ist zusammen mit der Vereinbarung aufzubewahren.
                                    </x:slot>
                                </x:alert>
                            </x:col>
                            <x:col>
                                @livewire('pharmacy.pharmaceutical-services.further-information-allowed', [
                                    'pharmaceuticalService' => $pharmaceuticalService
                                ])
                            </x:col>
                            <x:col>
                                <x:button
                                        href="{{ route('pharmacies.pharmaceutical-services.measure-blood-pressures.first-document.download', [$pharmaceuticalService->pharmacy, $pharmaceuticalService]) }}">
                                    Dokument herunterladen
                                </x:button>
                            </x:col>
                            <x:col>
                                <x:input.checkbox
                                        label="Hiermit bestätige ich, dass die Datei heruntergeladen, ausgedruckt und vom Patienten unterschrieben worden ist."
                                        name="downloaded"
                                        id="downloaded"
                                        x-model="downloaded"
                                        :error="$errors->first('downloaded')"
                                >Hiermit bestätige ich, dass die Datei heruntergeladen, ausgedruckt und vom Patienten
                                    unterschrieben worden ist.
                                </x:input.checkbox>
                            </x:col>
                        </x:row>
                    </x:card>

                    <div class="mt-12 w-full flex justify-center">
                        <x:button
                                x-bind:disabled="!downloaded"
                                size="lg" class="w-72">
                            Weiter
                        </x:button>
                    </div>

                    <div class="mt-3 w-full flex justify-center">
                        <x:button
                                href="{{ route('pharmacies.pharmaceutical-services.measure-blood-pressures.abort', [
                            'pharmacy' => $pharmacy,
                            'pharmaceuticalService' => $pharmaceuticalService,
                            'reasons' => \App\Enums\MeasureBloodPressure\ReasonToAbortEnum::ABORT,
                        ]) }}"
                                appearance="secondary"
                                size="lg" class="w-72"
                        >
                            Abbrechen
                        </x:button>
                    </div>
                </div>
            </form>
        </x:layout.container>
    </x:content>
@endsection
