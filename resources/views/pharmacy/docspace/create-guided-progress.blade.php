@extends('layouts.app')

@section('content')
    <x:content>
        <x-header>
            <x-slot name="main">
                <PERSON><PERSON><PERSON>
            </x-slot>

            <x-slot name="description">
                {!! __('sdr.welcome_message') !!}
            </x-slot>
        </x-header>

        @if (currentPharmacy()->guidedDocspaceCreationProcesses()->where('status', \App\Enums\Sdr\GuidedDocspaceProcessStatus::COMPLETED)->count())
            <x-alert type="success" class="bg-white rounded-md">
                <x-slot:description>
                    Die automatische Erstellung der Datenräume ist abgeschlossen.
                </x-slot:description>
                <x-slot name="trailingLink">
                    <a class="flex items-center"
                       href="{{ route('sdr.doc-spaces', ['pharmacy' => $pharmacy]) }}">Zu den Datenräumen
                        <svg class="h-4 w-4 ml-2">
                            <use href="/icons.svg#arrow-long-right-alt" />
                        </svg>
                    </a>
                </x-slot>
            </x-alert>
        @elseif (currentPharmacy()->guidedDocspaceCreationProcesses()->whereIn('status', [\App\Enums\Sdr\GuidedDocspaceProcessStatus::PENDING, \App\Enums\Sdr\GuidedDocspaceProcessStatus::PROCESSING])->count())
            <x-alert type="warning" class="bg-white rounded-md">
                <x-slot:description>
                    Die automatische Erstellung der Datenräume wird ausgeführt und kann einige Minuten dauern. Bitte
                    aktualisieren Sie die Seite, um den Abschluss des Vorgangs zu überprüfen.
                </x-slot:description>
                <x-slot name="trailingLink">
                    <a class="flex items-center"
                       href="{{ route('sdr.create-guided-progress', ['pharmacy' => $pharmacy]) }}">Aktualisieren
                        <svg class="h-4 w-4 ml-2">
                            <use href="/icons.svg#arrow-long-right-alt" />
                        </svg>
                    </a>
                </x-slot>
            </x-alert>
        @elseif (currentPharmacy()->guidedDocspaceCreationProcesses()->whereIn('status', [\App\Enums\Sdr\GuidedDocspaceProcessStatus::FAILED])->count())
            <x-alert type="error" class="bg-white rounded-md">
                <x-slot:description>
                    Entschuldigung! Die automatische Erstellung der Datenräume ist leider fehlgeschlagen. Bitte
                    versuchen Sie es noch einmal. Sollten weiterhin Fehler auftreten, können Sie die Datenräume auch
                    manuell anlegen oder sich an den Support werden.
                </x-slot:description>
            </x-alert>
            @if(currentPharmacy()->guidedDocspaceCreationProcesses()->whereIn('status', [\App\Enums\Sdr\GuidedDocspaceProcessStatus::FAILED])->count())
                @can('storeGuided', [\App\DocSpace::class, $pharmacy])
                    <div class="mt-4">
                        <x:button
                            href="{{ route('sdr.create-guided-progress', [$pharmacy, \App\GuidedDocspaceCreationProcess::RESTART_PROCESS]) }}">
                            <svg class="-ml-1 mr-2 h-5 w-5">
                                <use href="/icons.svg#arrow-path" />
                            </svg>
                            Erneut beginnen
                        </x:button>
                    </div>
                @endcan
            @endif
        @elseif(currentPharmacy()->docSpaces()->count())
            <x-alert type="discrete" class="bg-white rounded-md">
                <x-slot:description>
                    Die automatische Erstellung der Datenräume kann nicht mehr ausgeführt werden, da es bereits
                    mindestens einen Datenraum gibt.
                </x-slot:description>
            </x-alert>
        @else
            <form action="{{ route('sdr.create-guided', ['pharmacy' => $pharmacy]) }}" method="post">
                @csrf

                Alle Mitarbeiter haben Zugriff auf alle hier angelegten Datenräume. Sie können im Anschluss jederzeit
                die Gruppen bearbeiten und die Zugriffsrechte verwalten.

                <div class="mt-4">
                    <div class="-mb-3 text-sm">
                        Sie können Datenräume <b>automatisch</b> anlegen lassen:
                    </div>
                    <div class="mt-3 divide-y divide-gray-200">
                        @foreach(config('sdr.createGuided.options') as $key => $option)
                            <div class="py-4 text-sm">
                                <x-input.radio name="selected_doc_space_option"
                                               label="{{ $option['label'] }}"
                                               value="{{ $key }}"></x-input.radio>
                                <ul class="ml-10 mt-2">
                                    @foreach ($option['spaces'] as $space)
                                        <li class="list-disc">
                                            <div class="flex">
                                                {{ $space['name'] }} ({{ $space['description'] }})
                                                <span class="tooltip ml-1"
                                                      data-tippy-content="{{ $space['footnote'] }}">
                                                    <svg class="h-5 w-5 text-gray-400 hover:text-gray-900"
                                                         xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                                                        <use href="/icons.svg#information-circle" />
                                                    </svg>
                                                </span>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endforeach
                        <div class="py-4 text-sm">
                            <x-input.radio name="selected_doc_space_option"
                                           label="Manuell anlegen"
                                           value="{{ \App\GuidedDocspaceCreationProcess::CREATE_MANUALLY }}"></x-input.radio>
                            <ul class="ml-10 mt-2">
                                <li class="list-disc">
                                    alle Angaben werden von Ihnen selbst getätigt
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="w-full justify-end">
                    <x-button>Datenräume anlegen</x-button>
                </div>
            </form>
        @endif

    </x:content>

@endsection

