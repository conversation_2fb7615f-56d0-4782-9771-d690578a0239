@extends('layouts.app')

@section('content')
    <x-content>
        <x-header>
            <x-slot name="main">
                <PERSON><PERSON><PERSON>
            </x-slot>

            <x-slot name="description">
                {!! __('sdr.welcome_message') !!}

                @if (user()->cannot('sdr.viewAny', $pharmacy) && user()->getAccessibleDocSpaces()->count() === 0)
                    <x-alert type="warning" class="bg-white rounded-md">
                        <x-slot:description>
                            Für Sie sind leider noch keine Datenräume verfügbar. Bitte wenden Sie sich an den Inhaber der Apotheke.
                        </x-slot:description>
                    </x-alert>
                @endif
            </x-slot>
        </x-header>

        @if (user()->can('sdr.viewAny', $pharmacy))
            @include('components.sdr.sdr-tab-menu')
        @endif

        @livewire('pharmacy.doc-space.edit', ['pharmacy' => $pharmacy, 'docSpace' => $docSpace, 'details' => $details ?? 1])
        @livewire('pharmacy.doc-space.group.create', ['pharmacy' => $pharmacy, 'docSpace' => null])
        @livewire('pharmacy.doc-space.group.delete')

    </x-content>

@endsection

