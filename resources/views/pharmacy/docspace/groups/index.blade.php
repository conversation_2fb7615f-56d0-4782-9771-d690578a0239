@extends('layouts.app')

@section('content')
    <x-content>
        <x-header>
            <x-slot name="main">
                <PERSON><PERSON><PERSON> Datenraum
            </x-slot>

            <x-slot name="description">
                Legen Sie eigene Gruppen an und weisen Sie Mitarbeitende zu. Neue Datenräume können direkt für Gruppen
                freigeschaltet werden – so verwalten Sie flexibel Gruppen und deren Nutzer.
            </x-slot>
        </x-header>

        @if (user()->cannot('sdr.viewAny', $pharmacy) && user()->getAccessibleDocSpaces()->count() === 0)
            <x-alert type="warning" class="bg-white rounded-md">
                <x-slot:description>
                    Für Sie sind leider noch keine Datenräume verfügbar. Bitte wenden Sie sich an den Inhaber der
                    Apotheke.
                </x-slot:description>
            </x-alert>
        @endif

        @if (user()->can('sdr.viewAny', $pharmacy))
            @include('components.sdr.sdr-tab-menu')
        @endif

        @livewire('pharmacy.doc-space.group.index', ['pharmacy' => $pharmacy])

    </x-content>

    <div>
        @livewire('pharmacy.doc-space.group.create', ['pharmacy' => $pharmacy])
    </div>
    <div>
        @livewire('pharmacy.doc-space.group.delete')
    </div>

@endsection

